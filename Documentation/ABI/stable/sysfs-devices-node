What:		/sys/devices/system/node/possible
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		Nodes that could be possibly become online at some point.

What:		/sys/devices/system/node/online
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		Nodes that are online.

What:		/sys/devices/system/node/has_normal_memory
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		Nodes that have regular memory.

What:		/sys/devices/system/node/has_cpu
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		Nodes that have one or more CPUs.

What:		/sys/devices/system/node/has_high_memory
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		Nodes that have regular or high memory.
		Depends on CONFIG_HIGHMEM.

What:		/sys/devices/system/node/nodeX
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		When CONFIG_NUMA is enabled, this is a directory containing
		information on node X such as what CPUs are local to the
		node. Each file is detailed next.

What:		/sys/devices/system/node/nodeX/cpumap
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		The node's cpumap.

What:		/sys/devices/system/node/nodeX/cpulist
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		The CPUs associated to the node.

What:		/sys/devices/system/node/nodeX/meminfo
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		Provides information about the node's distribution and memory
		utilization. Similar to /proc/meminfo, see Documentation/filesystems/proc.rst

What:		/sys/devices/system/node/nodeX/numastat
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		The node's hit/miss statistics, in units of pages.
		See Documentation/admin-guide/numastat.rst

What:		/sys/devices/system/node/nodeX/distance
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		Distance between the node and all the other nodes
		in the system.

What:		/sys/devices/system/node/nodeX/vmstat
Date:		October 2002
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		The node's zoned virtual memory statistics.
		This is a superset of numastat.

What:		/sys/devices/system/node/nodeX/compact
Date:		February 2010
Contact:	Mel Gorman <<EMAIL>>
Description:
		When this file is written to, all memory within that node
		will be compacted. When it completes, memory will be freed
		into blocks which have as many contiguous pages as possible

What:		/sys/devices/system/node/nodeX/hugepages/hugepages-<size>/
Date:		December 2009
Contact:	Lee Schermerhorn <<EMAIL>>
Description:
		The node's huge page size control/query attributes.
		See Documentation/admin-guide/mm/hugetlbpage.rst

What:		/sys/devices/system/node/nodeX/accessY/
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		The node's relationship to other nodes for access class "Y".

What:		/sys/devices/system/node/nodeX/accessY/initiators/
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		The directory containing symlinks to memory initiator
		nodes that have class "Y" access to this target node's
		memory. CPUs and other memory initiators in nodes not in
		the list accessing this node's memory may have different
		performance.

What:		/sys/devices/system/node/nodeX/accessY/targets/
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		The directory containing symlinks to memory targets that
		this initiator node has class "Y" access.

What:		/sys/devices/system/node/nodeX/accessY/initiators/read_bandwidth
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		This node's read bandwidth in MB/s when accessed from
		nodes found in this access class's linked initiators.

What:		/sys/devices/system/node/nodeX/accessY/initiators/read_latency
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		This node's read latency in nanoseconds when accessed
		from nodes found in this access class's linked initiators.

What:		/sys/devices/system/node/nodeX/accessY/initiators/write_bandwidth
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		This node's write bandwidth in MB/s when accessed from
		found in this access class's linked initiators.

What:		/sys/devices/system/node/nodeX/accessY/initiators/write_latency
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		This node's write latency in nanoseconds when access
		from nodes found in this class's linked initiators.

What:		/sys/devices/system/node/nodeX/memory_side_cache/indexY/
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		The directory containing attributes for the memory-side cache
		level 'Y'.

What:		/sys/devices/system/node/nodeX/memory_side_cache/indexY/indexing
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		The caches associativity indexing: 0 for direct mapped,
		non-zero if indexed.

What:		/sys/devices/system/node/nodeX/memory_side_cache/indexY/line_size
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		The number of bytes accessed from the next cache level on a
		cache miss.

What:		/sys/devices/system/node/nodeX/memory_side_cache/indexY/size
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		The size of this memory side cache in bytes.

What:		/sys/devices/system/node/nodeX/memory_side_cache/indexY/write_policy
Date:		December 2018
Contact:	Keith Busch <<EMAIL>>
Description:
		The cache write policy: 0 for write-back, 1 for write-through,
		other or unknown.

What:		/sys/devices/system/node/nodeX/x86/sgx_total_bytes
Date:		November 2021
Contact:	Jarkko Sakkinen <<EMAIL>>
Description:
		The total amount of SGX physical memory in bytes.

What:		/sys/devices/system/node/nodeX/memory_failure/total
Date:		January 2023
Contact:	Jiaqi Yan <<EMAIL>>
Description:
		The total number of raw poisoned pages (pages containing
		corrupted data due to memory errors) on a NUMA node.

What:		/sys/devices/system/node/nodeX/memory_failure/ignored
Date:		January 2023
Contact:	Jiaqi Yan <<EMAIL>>
Description:
		Of the raw poisoned pages on a NUMA node, how many pages are
		ignored by memory error recovery attempt, usually because
		support for this type of pages is unavailable, and kernel
		gives up the recovery.

What:		/sys/devices/system/node/nodeX/memory_failure/failed
Date:		January 2023
Contact:	Jiaqi Yan <<EMAIL>>
Description:
		Of the raw poisoned pages on a NUMA node, how many pages are
		failed by memory error recovery attempt. This usually means
		a key recovery operation failed.

What:		/sys/devices/system/node/nodeX/memory_failure/delayed
Date:		January 2023
Contact:	Jiaqi Yan <<EMAIL>>
Description:
		Of the raw poisoned pages on a NUMA node, how many pages are
		delayed by memory error recovery attempt. Delayed poisoned
		pages usually will be retried by kernel.

What:		/sys/devices/system/node/nodeX/memory_failure/recovered
Date:		January 2023
Contact:	Jiaqi Yan <<EMAIL>>
Description:
		Of the raw poisoned pages on a NUMA node, how many pages are
		recovered by memory error recovery attempt.
