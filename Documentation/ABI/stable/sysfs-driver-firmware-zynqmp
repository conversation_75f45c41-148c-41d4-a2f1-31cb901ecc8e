What:		/sys/devices/platform/firmware\:zynqmp-firmware/ggs*
Date:		March 2020
KernelVersion:	5.6
Contact:	"Jo<PERSON> <PERSON>" <<EMAIL>>
Description:
		Read/Write PMU global general storage register value,
		GLOBAL_GEN_STORAGE{0:3}.
		Global general storage register that can be used
		by system to pass information between masters.

		The register is reset during system or power-on
		resets. Three registers are used by the FSBL and
		other Xilinx software products: GLOBAL_GEN_STORAGE{4:6}.

		Usage::

		    # cat /sys/devices/platform/firmware\:zynqmp-firmware/ggs0
		    # echo <value> > /sys/devices/platform/firmware\:zynqmp-firmware/ggs0

		Example::

		    # cat /sys/devices/platform/firmware\:zynqmp-firmware/ggs0
		    # echo 0x1234ABCD > /sys/devices/platform/firmware\:zynqmp-firmware/ggs0

Users:		Xilinx

What:		/sys/devices/platform/firmware\:zynqmp-firmware/pggs*
Date:		March 2020
KernelVersion:	5.6
Contact:	"Jo<PERSON> <PERSON>" <<EMAIL>>
Description:
		Read/Write PMU persistent global general storage register
		value, PERS_GLOB_GEN_STORAGE{0:3}.
		Persistent global general storage register that
		can be used by system to pass information between
		masters.

		This register is only reset by the power-on reset
		and maintains its value through a system reset.
		Four registers are used by the FSBL and other Xilinx
		software products: PERS_GLOB_GEN_STORAGE{4:7}.
		Register is reset only by a POR reset.

		Usage::

		    # cat /sys/devices/platform/firmware\:zynqmp-firmware/pggs0
		    # echo <value> > /sys/devices/platform/firmware\:zynqmp-firmware/pggs0

		Example::

		    # cat /sys/devices/platform/firmware\:zynqmp-firmware/pggs0
		    # echo 0x1234ABCD > /sys/devices/platform/firmware\:zynqmp-firmware/pggs0

Users:		Xilinx

What:		/sys/devices/platform/firmware\:zynqmp-firmware/shutdown_scope
Date:		March 2020
KernelVersion:	5.6
Contact:	"Jolly Shah" <<EMAIL>>
Description:
		This sysfs interface allows to set the shutdown scope for the
		next shutdown request. When the next shutdown is performed, the
		platform specific portion of PSCI-system_off can use the chosen
		shutdown scope.

		Following are available shutdown scopes(subtypes):

		subsystem:
				Only the APU along with all of its peripherals
				not used by other processing units will be
				shut down. This may result in the FPD power
				domain being shut down provided that no other
				processing unit uses FPD peripherals or DRAM.
		ps_only:
				The complete PS will be shut down, including the
				RPU, PMU, etc.  Only the PL domain (FPGA)
				remains untouched.
		system:
				The complete system/device is shut down.

		Usage::

		    # cat /sys/devices/platform/firmware\:zynqmp-firmware/shutdown_scope
		    # echo <scope> > /sys/devices/platform/firmware\:zynqmp-firmware/shutdown_scope

		Example::

		    # cat /sys/devices/platform/firmware\:zynqmp-firmware/shutdown_scope
		    # echo "subsystem" > /sys/devices/platform/firmware\:zynqmp-firmware/shutdown_scope

Users:		Xilinx

What:		/sys/devices/platform/firmware\:zynqmp-firmware/health_status
Date:		March 2020
KernelVersion:	5.6
Contact:	"Jolly Shah" <<EMAIL>>
Description:
		This sysfs interface allows to set the health status. If PMUFW
		is compiled with CHECK_HEALTHY_BOOT, it will check the healthy
		bit on FPD WDT expiration. If healthy bit is set by a user
		application running in Linux, PMUFW will do APU only restart. If
		healthy bit is not set during FPD WDT expiration, PMUFW will do
		system restart.

		Usage:

		Set healthy bit::

		    # echo 1 > /sys/devices/platform/firmware\:zynqmp-firmware/health_status

		Unset healthy bit::

		    # echo 0 > /sys/devices/platform/firmware\:zynqmp-firmware/health_status

Users:		Xilinx

What:		/sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
Date:		Feb 2022
KernelVersion:	5.18
Contact:	"Ronak Jain" <<EMAIL>>
Description:
		This sysfs interface allows user to configure features at
		runtime. The user can enable or disable features running at
		firmware as well as the user can configure the parameters of
		the features at runtime. The supported features are over
		temperature and external watchdog. Here, the external watchdog
		is completely different than the /dev/watchdog as the external
		watchdog is running on the firmware and it is used to monitor
		the health of firmware not APU(Linux). Also, the external
		watchdog is interfaced outside of the zynqmp soc.

		The supported config ids are for the feature configuration is,
		1. PM_FEATURE_OVERTEMP_STATUS = 1, the user can enable or
		disable the over temperature feature.
		2. PM_FEATURE_OVERTEMP_VALUE = 2, the user can configure the
		over temperature limit in Degree Celsius.
		3. PM_FEATURE_EXTWDT_STATUS = 3, the user can enable or disable
		the external watchdog feature.
		4. PM_FEATURE_EXTWDT_VALUE = 4, the user can configure the
		external watchdog feature.

		Usage:

		Select over temperature config ID to enable/disable feature
		# echo 1 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id

		Check over temperature config ID is selected or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		The expected result is 1.

		Select over temperature config ID to configure OT limit
		# echo 2 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id

		Check over temperature config ID is selected or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		The expected result is 2.

		Select external watchdog config ID to enable/disable feature
		# echo 3 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id

		Check external watchdog config ID is selected or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		The expected result is 3.

		Select external watchdog config ID to configure time interval
		# echo 4 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id

		Check external watchdog config ID is selected or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		The expected result is 4.

Users:		Xilinx

What:		/sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value
Date:		Feb 2022
KernelVersion:	5.18
Contact:	"Ronak Jain" <<EMAIL>>
Description:
		This sysfs interface allows to configure features at runtime.
		The user can enable or disable features running at firmware.
		Also, the user can configure the parameters of the features
		at runtime. The supported features are over temperature and
		external watchdog. Here, the external watchdog is completely
		different than the /dev/watchdog as the external watchdog is
		running on the firmware and it is used to monitor the health
		of firmware not APU(Linux). Also, the external watchdog is
		interfaced outside of the zynqmp soc.

		By default the features are disabled in the firmware. The user
		can enable features by querying appropriate config id of the
		features.

		The default limit for the over temperature is 90 Degree Celsius.
		The default timer interval for the external watchdog is 570ms.

		The supported config ids are for the feature configuration is,
		1. PM_FEATURE_OVERTEMP_STATUS = 1, the user can enable or
		disable the over temperature feature.
		2. PM_FEATURE_OVERTEMP_VALUE = 2, the user can configure the
		over temperature limit in Degree Celsius.
		3. PM_FEATURE_EXTWDT_STATUS = 3, the user can enable or disable
		the external watchdog feature.
		4. PM_FEATURE_EXTWDT_VALUE = 4, the user can configure the
		external watchdog feature.

		Usage:

		Enable over temperature feature
		# echo 1 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		# echo 1 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value

		Check whether the over temperature feature is enabled or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value
		The expected result is 1.

		Disable over temperature feature
		# echo 1 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		# echo 0 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value

		Check whether the over temperature feature is disabled or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value
		The expected result is 0.

		Configure over temperature limit to 50 Degree Celsius
		# echo 2 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		# echo 50 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value

		Check whether the over temperature limit is configured or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value
		The expected result is 50.

		Enable external watchdog feature
		# echo 3 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		# echo 1 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value

		Check whether the external watchdog feature is enabled or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value
		The expected result is 1.

		Disable external watchdog feature
		# echo 3 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		# echo 0 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value

		Check whether the external watchdog feature is disabled or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value
		The expected result is 0.

		Configure external watchdog timer interval to 500ms
		# echo 4 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_id
		# echo 500 > /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value

		Check whether the external watchdog timer interval is configured or not
		# cat /sys/devices/platform/firmware\:zynqmp-firmware/feature_config_value
		The expected result is 500.

Users:		Xilinx
