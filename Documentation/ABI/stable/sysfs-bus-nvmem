What:		/sys/bus/nvmem/devices/.../force_ro
Date:		June 2024
KernelVersion:	6.11
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		This read/write attribute allows users to set read-write
		devices as read-only and back to read-write from userspace.
		This can be used to unlock and relock write-protection of
		devices which are generally locked, except during sporadic
		programming operation.
		Read returns '0' or '1' for read-write or read-only modes
		respectively.
		Write parses one of 'YyTt1NnFf0', or [oO][NnFf] for "on"
		and "off", i.e. what kstrtobool() supports.
		Note: This file is only present if CONFIG_NVMEM_SYSFS
		is enabled.

What:		/sys/bus/nvmem/devices/.../nvmem
Date:		July 2015
KernelVersion:	4.2
Contact:	Sri<PERSON><PERSON>ndagatla <<EMAIL>>
Description:
		This file allows user to read/write the raw NVMEM contents.
		Permissions for write to this file depends on the nvmem
		provider configuration.
		Note: This file is only present if CONFIG_NVMEM_SYSFS
		is enabled

		ex::

		  hexdump /sys/bus/nvmem/devices/qfprom0/nvmem

		  0000000 0000 0000 0000 0000 0000 0000 0000 0000
		  *
		  00000a0 db10 2240 0000 e000 0c00 0c00 0000 0c00
		  0000000 0000 0000 0000 0000 0000 0000 0000 0000
		  ...
		  *
		  0001000

What:		/sys/bus/nvmem/devices/.../type
Date:		November 2018
KernelVersion:	5.0
Contact:	Alexandre Belloni <<EMAIL>>
Description:
		This read-only attribute allows user to read the NVMEM
		device type. Supported types are "Unknown", "EEPROM",
		"OTP", "Battery backed", "FRAM".
		Note: This file is only present if CONFIG_NVMEM_SYSFS
		is enabled.
