What:		/sys/class/backlight/<backlight>/bl_power
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<PERSON> <<EMAIL>>
Description:
		Control BACKLIGHT power, values are compatible with
		FB_BLANK_* from fb.h

		 - 0 (FB_BLANK_UNBLANK)   : power on.
		 - 4 (FB_BLANK_POWERDOWN) : power off
Users:		HAL

What:		/sys/class/backlight/<backlight>/brightness
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<PERSON> <<EMAIL>>
Description:
		Control the brightness for this <backlight>. Values
		are between 0 and max_brightness. This file will also
		show the brightness level stored in the driver, which
		may not be the actual brightness (see actual_brightness).
Users:		HAL

What:		/sys/class/backlight/<backlight>/actual_brightness
Date:		March 2006
KernelVersion:	2.6.17
Contact:	<PERSON> <rpur<PERSON>@rpsys.net>
Description:
		Show the actual brightness by querying the hardware.
Users:		HAL

What:		/sys/class/backlight/<backlight>/max_brightness
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<PERSON> <<EMAIL>>
Description:
		Maximum brightness for <backlight>.
Users:		HAL

What:		/sys/class/backlight/<backlight>/type
Date:		September 2010
KernelVersion:	2.6.37
Contact:	Matthew Garrett <<EMAIL>>
Description:
		The type of interface controlled by <backlight>.
		"firmware": The driver uses a standard firmware interface
		"platform": The driver uses a platform-specific interface
		"raw": The driver controls hardware registers directly

		In the general case, when multiple backlight
		interfaces are available for a single device, firmware
		control should be preferred to platform control should
		be preferred to raw control. Using a firmware
		interface reduces the probability of confusion with
		the hardware and the OS independently updating the
		backlight state. Platform interfaces are mostly a
		holdover from pre-standardisation of firmware
		interfaces.
