What:		/sys/devices/system/xen_memory/xen_memory0/max_retry_count
Date:		May 2011
KernelVersion:	2.6.39
Contact:	<PERSON> <<EMAIL>>
Description:
		The maximum number of times the balloon driver will
		attempt to increase the balloon before giving up.  See
		also 'retry_count' below.
		A value of zero means retry forever and is the default one.

What:		/sys/devices/system/xen_memory/xen_memory0/max_schedule_delay
Date:		May 2011
KernelVersion:	2.6.39
Contact:	<PERSON> <<EMAIL>>
Description:
		The limit that 'schedule_delay' (see below) will be
		increased to. The default value is 32 seconds.

What:		/sys/devices/system/xen_memory/xen_memory0/retry_count
Date:		May 2011
KernelVersion:	2.6.39
Contact:	<PERSON> <<EMAIL>>
Description:
		The current number of times that the balloon driver
		has attempted to increase the size of the balloon.
		The default value is one. With max_retry_count being
		zero (unlimited), this means that the driver will attempt
		to retry with a 'schedule_delay' delay.

What:		/sys/devices/system/xen_memory/xen_memory0/schedule_delay
Date:		May 2011
KernelVersion:	2.6.39
Contact:	<PERSON> Wilk <<EMAIL>>
Description:
		The time (in seconds) to wait between attempts to
		increase the balloon.  Each time the balloon cannot be
		increased, 'schedule_delay' is increased (until
		'max_schedule_delay' is reached at which point it
		will use the max value).

What:		/sys/devices/system/xen_memory/xen_memory0/target
Date:		April 2008
KernelVersion:	2.6.26
Contact:	Konrad Rzeszutek Wilk <<EMAIL>>
Description:
		The target number of pages to adjust this domain's
		memory reservation to.

What:		/sys/devices/system/xen_memory/xen_memory0/target_kb
Date:		April 2008
KernelVersion:	2.6.26
Contact:	Konrad Rzeszutek Wilk <<EMAIL>>
Description:
		As target above, except the value is in KiB.

What:		/sys/devices/system/xen_memory/xen_memory0/info/current_kb
Date:		April 2008
KernelVersion:	2.6.26
Contact:	Konrad Rzeszutek Wilk <<EMAIL>>
Description:
		Current size (in KiB) of this domain's memory
		reservation.

What:		/sys/devices/system/xen_memory/xen_memory0/info/high_kb
Date:		April 2008
KernelVersion:	2.6.26
Contact:	Konrad Rzeszutek Wilk <<EMAIL>>
Description:
		Amount (in KiB) of high memory in the balloon.

What:		/sys/devices/system/xen_memory/xen_memory0/info/low_kb
Date:		April 2008
KernelVersion:	2.6.26
Contact:	Konrad Rzeszutek Wilk <<EMAIL>>
Description:
		Amount (in KiB) of low (or normal) memory in the
		balloon.

What:		/sys/devices/system/xen_memory/xen_memory0/scrub_pages
Date:		September 2018
KernelVersion:	4.20
Contact:	<EMAIL>
Description:
		Control scrubbing pages before returning them to Xen for others domains
		use. Can be set with xen_scrub_pages cmdline
		parameter. Default value controlled with CONFIG_XEN_SCRUB_PAGES_DEFAULT.
