What:		/sys/bus/dsa/devices/dsa<m>/version
Date:		Apr 15, 2020
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:	The hardware version number.

What:           /sys/bus/dsa/devices/dsa<m>/cdev_major
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:	The major number that the character device driver assigned to
		this device.

What:           /sys/bus/dsa/devices/dsa<m>/errors
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The error information for this device.

What:           /sys/bus/dsa/devices/dsa<m>/max_batch_size
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The largest number of work descriptors in a batch.
                It's not visible when the device does not support batch.

What:           /sys/bus/dsa/devices/dsa<m>/max_work_queues_size
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The maximum work queue size supported by this device.

What:           /sys/bus/dsa/devices/dsa<m>/max_engines
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The maximum number of engines supported by this device.

What:           /sys/bus/dsa/devices/dsa<m>/max_groups
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The maximum number of groups can be created under this device.

What:           /sys/bus/dsa/devices/dsa<m>/max_read_buffers
Date:           Dec 10, 2021
KernelVersion:  5.17.0
Contact:        <EMAIL>
Description:    The total number of read buffers supported by this device.
		The read buffers represent resources within the DSA
		implementation, and these resources are allocated by engines to
		support operations. See DSA spec v1.2 9.2.4 Total Read Buffers.
		It's not visible when the device does not support Read Buffer
		allocation control.

What:           /sys/bus/dsa/devices/dsa<m>/max_transfer_size
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The number of bytes to be read from the source address to
		perform the operation. The maximum transfer size is dependent on
		the workqueue the descriptor was submitted to.

What:           /sys/bus/dsa/devices/dsa<m>/max_work_queues
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The maximum work queue number that this device supports.

What:           /sys/bus/dsa/devices/dsa<m>/numa_node
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The numa node number for this device.

What:           /sys/bus/dsa/devices/dsa<m>/op_cap
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The operation capability bit mask specify the operation types
		supported by the this device.

What:		/sys/bus/dsa/devices/dsa<m>/pasid_enabled
Date:		Oct 27, 2020
KernelVersion:	5.11.0
Contact:	<EMAIL>
Description:	To indicate if user PASID (process address space identifier) is
		enabled or not for this device.

What:           /sys/bus/dsa/devices/dsa<m>/state
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The state information of this device. It can be either enabled
		or disabled.

What:           /sys/bus/dsa/devices/dsa<m>/group<m>.<n>
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The assigned group under this device.

What:           /sys/bus/dsa/devices/dsa<m>/engine<m>.<n>
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The assigned engine under this device.

What:           /sys/bus/dsa/devices/dsa<m>/wq<m>.<n>
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The assigned work queue under this device.

What:           /sys/bus/dsa/devices/dsa<m>/configurable
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    To indicate if this device is configurable or not.

What:           /sys/bus/dsa/devices/dsa<m>/read_buffer_limit
Date:           Dec 10, 2021
KernelVersion:  5.17.0
Contact:        <EMAIL>
Description:    The maximum number of read buffers that may be in use at
		one time by operations that access low bandwidth memory in the
		device. See DSA spec v1.2 9.2.8 GENCFG on Global Read Buffer Limit.
		It's not visible when the device does not support Read Buffer
		allocation control.

What:		/sys/bus/dsa/devices/dsa<m>/cmd_status
Date:		Aug 28, 2020
KernelVersion:	5.10.0
Contact:	<EMAIL>
Description:	The last executed device administrative command's status/error.
		Also last configuration error overloaded.
		Writing to it will clear the status.

What:		/sys/bus/dsa/devices/dsa<m>/iaa_cap
Date:		Sept 14, 2022
KernelVersion: 6.0.0
Contact:	<EMAIL>
Description:	IAA (IAX) capability mask. Exported to user space for application
		consumption. This attribute should only be visible on IAA devices
		that are version 2 or later.

What:		/sys/bus/dsa/devices/dsa<m>/event_log_size
Date:		Sept 14, 2022
KernelVersion: 6.4.0
Contact:	<EMAIL>
Description:	The event log size to be configured. Default is 64 entries and
		occupies 4k size if the evl entry is 64 bytes. It's visible
		only on platforms that support the capability.

What:		/sys/bus/dsa/devices/wq<m>.<n>/block_on_fault
Date:		Oct 27, 2020
KernelVersion:	5.11.0
Contact:	<EMAIL>
Description:	To indicate block on fault is allowed or not for the work queue
		to support on demand paging.

What:           /sys/bus/dsa/devices/wq<m>.<n>/group_id
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The group id that this work queue belongs to.

What:           /sys/bus/dsa/devices/wq<m>.<n>/size
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The work queue size for this work queue.

What:           /sys/bus/dsa/devices/wq<m>.<n>/type
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The type of this work queue, it can be "kernel" type for work
		queue usages in the kernel space or "user" type for work queue
		usages by applications in user space.

What:           /sys/bus/dsa/devices/wq<m>.<n>/cdev_minor
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The minor number assigned to this work queue by the character
		device driver.

What:           /sys/bus/dsa/devices/wq<m>.<n>/mode
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The work queue mode type for this work queue.

What:           /sys/bus/dsa/devices/wq<m>.<n>/priority
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The priority value of this work queue, it is a value relative to
		other work queue in the same group to control quality of service
		for dispatching work from multiple workqueues in the same group.

What:           /sys/bus/dsa/devices/wq<m>.<n>/state
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The current state of the work queue.

What:           /sys/bus/dsa/devices/wq<m>.<n>/threshold
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The number of entries in this work queue that may be filled
		via a limited portal.

What:		/sys/bus/dsa/devices/wq<m>.<n>/max_transfer_size
Date:		Aug 28, 2020
KernelVersion:	5.10.0
Contact:	<EMAIL>
Description:	The max transfer sized for this workqueue. Cannot exceed device
		max transfer size. Configurable parameter.

What:		/sys/bus/dsa/devices/wq<m>.<n>/max_batch_size
Date:		Aug 28, 2020
KernelVersion:	5.10.0
Contact:	<EMAIL>
Description:	The max batch size for this workqueue. Cannot exceed device
		max batch size. Configurable parameter.
		It's not visible when the device does not support batch.

What:		/sys/bus/dsa/devices/wq<m>.<n>/ats_disable
Date:		Nov 13, 2020
KernelVersion:	5.11.0
Contact:	<EMAIL>
Description:	Indicate whether ATS disable is turned on for the workqueue.
		0 indicates ATS is on, and 1 indicates ATS is off for the workqueue.

What:		/sys/bus/dsa/devices/wq<m>.<n>/prs_disable
Date:		Sept 14, 2022
KernelVersion: 6.4.0
Contact:	<EMAIL>
Description:	Controls whether PRS disable is turned on for the workqueue.
		0 indicates PRS is on, and 1 indicates PRS is off for the
		workqueue. This option overrides block_on_fault attribute
		if set. It's visible only on platforms that support the
		capability.

What:		/sys/bus/dsa/devices/wq<m>.<n>/occupancy
Date		May 25, 2021
KernelVersion:	5.14.0
Contact:	<EMAIL>
Description:	Show the current number of entries in this WQ if WQ Occupancy
		Support bit WQ capabilities is 1.

What:		/sys/bus/dsa/devices/wq<m>.<n>/enqcmds_retries
Date		Oct 29, 2021
KernelVersion:	5.17.0
Contact:	<EMAIL>
Description:	Indicate the number of retires for an enqcmds submission on a sharedwq.
		A max value to set attribute is capped at 64.

What:		/sys/bus/dsa/devices/wq<m>.<n>/op_config
Date:		Sept 14, 2022
KernelVersion:	6.0.0
Contact:	<EMAIL>
Description:	Shows the operation capability bits displayed in bitmap format
		presented by %*pb printk() output format specifier.
		The attribute can be configured when the WQ is disabled in
		order to configure the WQ to accept specific bits that
		correlates to the operations allowed. It's visible only
		on platforms that support the capability.

What:		/sys/bus/dsa/devices/wq<m>.<n>/driver_name
Date:		Sept 8, 2023
KernelVersion:	6.7.0
Contact:	<EMAIL>
Description:	Name of driver to be bounded to the wq.

What:           /sys/bus/dsa/devices/engine<m>.<n>/group_id
Date:           Oct 25, 2019
KernelVersion:  5.6.0
Contact:        <EMAIL>
Description:    The group that this engine belongs to.

What:		/sys/bus/dsa/devices/group<m>.<n>/use_read_buffer_limit
Date:		Dec 10, 2021
KernelVersion:	5.17.0
Contact:	<EMAIL>
Description:	Enable the use of global read buffer limit for the group. See DSA
		spec v1.2 9.2.18 GRPCFG Use Global Read Buffer Limit.
		It's not visible when the device does not support Read Buffer
		allocation control.

What:		/sys/bus/dsa/devices/group<m>.<n>/read_buffers_allowed
Date:		Dec 10, 2021
KernelVersion:	5.17.0
Contact:	<EMAIL>
Description:	Indicates max number of read buffers that may be in use at one time
		by all engines in the group. See DSA spec v1.2 9.2.18 GRPCFG Read
		Buffers Allowed.
		It's not visible when the device does not support Read Buffer
		allocation control.

What:		/sys/bus/dsa/devices/group<m>.<n>/read_buffers_reserved
Date:		Dec 10, 2021
KernelVersion:	5.17.0
Contact:	<EMAIL>
Description:	Indicates the number of Read Buffers reserved for the use of
		engines in the group. See DSA spec v1.2 9.2.18 GRPCFG Read Buffers
		Reserved.
		It's not visible when the device does not support Read Buffer
		allocation control.

What:		/sys/bus/dsa/devices/group<m>.<n>/desc_progress_limit
Date:		Sept 14, 2022
KernelVersion:	6.0.0
Contact:	<EMAIL>
Description:	Allows control of the number of work descriptors that can be
		concurrently processed by an engine in the group as a fraction
		of the Maximum Work Descriptors in Progress value specified in
		the ENGCAP register. The acceptable values are 0 (default),
		1 (1/2 of max value), 2 (1/4 of the max value), and 3 (1/8 of
		the max value). It's visible only on platforms that support
		the capability.

What:		/sys/bus/dsa/devices/group<m>.<n>/batch_progress_limit
Date:		Sept 14, 2022
KernelVersion:	6.0.0
Contact:	<EMAIL>
Description:	Allows control of the number of batch descriptors that can be
		concurrently processed by an engine in the group as a fraction
		of the Maximum Batch Descriptors in Progress value specified in
		the ENGCAP register. The acceptable values are 0 (default),
		1 (1/2 of max value), 2 (1/4 of the max value), and 3 (1/8 of
		the max value). It's visible only on platforms that support
		the capability.

What:		/sys/bus/dsa/devices/wq<m>.<n>/dsa<x>\!wq<m>.<n>/file<y>/cr_faults
Date:		Sept 14, 2022
KernelVersion:	6.4.0
Contact:	<EMAIL>
Description:	Show the number of Completion Record (CR) faults this application
		has caused.

What:		/sys/bus/dsa/devices/wq<m>.<n>/dsa<x>\!wq<m>.<n>/file<y>/cr_fault_failures
Date:		Sept 14, 2022
KernelVersion:	6.4.0
Contact:	<EMAIL>
Description:	Show the number of Completion Record (CR) faults failures that this
		application has caused. The failure counter is incremented when the
		driver cannot fault in the address for the CR. Typically this is caused
		by a bad address programmed in the submitted descriptor or a malicious
		submitter is using bad CR address on purpose.

What:		/sys/bus/dsa/devices/wq<m>.<n>/dsa<x>\!wq<m>.<n>/file<y>/pid
Date:		Sept 14, 2022
KernelVersion:	6.4.0
Contact:	<EMAIL>
Description:	Show the process id of the application that opened the file. This is
		helpful information for a monitor daemon that wants to kill the
		application that opened the file.
