What:           /sys/devices/pciXXXX:XX/0000:XX:XX.X/dma/dma<n>chan<n>/quickdata/cap
Date:           December 3, 2009
KernelVersion:  2.6.32
Contact:        <EMAIL>
Description:	Capabilities the DMA supports.Currently there are DMA_PQ, DMA_PQ_VAL,
		DMA_XOR,DMA_XOR_VAL,DMA_INTERRUPT.

What:           /sys/devices/pciXXXX:XX/0000:XX:XX.X/dma/dma<n>chan<n>/quickdata/ring_active
Date:           December 3, 2009
KernelVersion:  2.6.32
Contact:        <EMAIL>
Description:	The number of descriptors active in the ring.

What:           /sys/devices/pciXXXX:XX/0000:XX:XX.X/dma/dma<n>chan<n>/quickdata/ring_size
Date:           December 3, 2009
KernelVersion:  2.6.32
Contact:        <EMAIL>
Description:	Descriptor ring size, total number of descriptors available.

What:           /sys/devices/pciXXXX:XX/0000:XX:XX.X/dma/dma<n>chan<n>/quickdata/version
Date:           December 3, 2009
KernelVersion:  2.6.32
Contact:        <EMAIL>
Description:	Version of ioatdma device.

What:           /sys/devices/pciXXXX:XX/0000:XX:XX.X/dma/dma<n>chan<n>/quickdata/intr_coalesce
Date:           August 8, 2017
KernelVersion:  4.14
Contact:        <EMAIL>
Description:	Tune-able interrupt delay value per channel basis.
