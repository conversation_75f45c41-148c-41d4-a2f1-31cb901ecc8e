Note:
  This documents additional properties of any device beyond what
  is documented in Documentation/admin-guide/sysfs-rules.rst

What:		/sys/devices/*/of_node
Date:		February 2015
Contact:	Device Tree mailing list <<EMAIL>>
Description:
		Any device associated with a device-tree node will have
		an of_path symlink pointing to the corresponding device
		node in /sys/firmware/devicetree/

What:		/sys/devices/*/devspec
Date:		October 2016
Contact:	Device Tree mailing list <<EMAIL>>
Description:
		If CONFIG_OF is enabled, then this file is present. When
		read, it returns full name of the device node.

What:		/sys/devices/*/obppath
Date:		October 2016
Contact:	Device Tree mailing list <<EMAIL>>
Description:
		If CONFIG_OF is enabled, then this file is present. When
		read, it returns full name of the device node.

What:		/sys/devices/*/dev
Date:		Jun 2006
Contact:	<PERSON> <<EMAIL>>
Description:
		Major and minor numbers of the character device corresponding
		to the device (in <major>:<minor> format).
