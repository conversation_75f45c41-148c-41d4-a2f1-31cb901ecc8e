What:		/sys/class/ubi/
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem <PERSON>yutskiy <<EMAIL>>
Description:
		The ubi/ class sub-directory belongs to the UBI subsystem and
		provides general UBI information, per-UBI device information
		and per-UBI volume information.

What:		/sys/class/ubi/version
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		This file contains version of the latest supported UBI on-media
		format. Currently it is 1, and there is no plan to change this.
		However, if in the future UBI needs on-flash format changes
		which cannot be done in a compatible manner, a new format
		version will be added. So this is a mechanism for possible
		future backward-compatible (but forward-incompatible)
		improvements.

What:		/sys/class/ubiX/
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		The /sys/class/ubi0, /sys/class/ubi1, etc directories describe
		UBI devices (UBI device 0, 1, etc). They contain general UBI
		device information and per UBI volume information (each UBI
		device may have many UBI volumes)

What:		/sys/class/ubi/ubiX/avail_eraseblocks
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Amount of available logical eraseblock. For example, one may
		create a new UBI volume which has this amount of logical
		eraseblocks.

What:		/sys/class/ubi/ubiX/bad_peb_count
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Count of bad physical eraseblocks on the underlying MTD device.

What:		/sys/class/ubi/ubiX/bgt_enabled
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Contains ASCII "0\n" if the UBI background thread is disabled,
		and ASCII "1\n" if it is enabled.

What:		/sys/class/ubi/ubiX/dev
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Major and minor numbers of the character device corresponding
		to this UBI device (in <major>:<minor> format).

What:		/sys/class/ubi/ubiX/eraseblock_size
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Maximum logical eraseblock size this UBI device may provide. UBI
		volumes may have smaller logical eraseblock size because of their
		alignment.

What:		/sys/class/ubi/ubiX/max_ec
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Maximum physical eraseblock erase counter value.

What:		/sys/class/ubi/ubiX/max_vol_count
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Maximum number of volumes which this UBI device may have.

What:		/sys/class/ubi/ubiX/min_io_size
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Minimum input/output unit size. All the I/O may only be done
		in fractions of the contained number.

What:		/sys/class/ubi/ubiX/mtd_num
Date:		January 2008
KernelVersion:	2.6.25
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Number of the underlying MTD device.

What:		/sys/class/ubi/ubiX/reserved_for_bad
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Number of physical eraseblocks reserved for bad block handling.

What:		/sys/class/ubi/ubiX/ro_mode
Date:		April 2016
KernelVersion:	4.7
Contact:	<EMAIL>
Description:
		Contains ASCII "1\n" if the read-only flag is set on this
		device, and "0\n" if it is cleared. UBI devices mark themselves
		as read-only when they detect an unrecoverable error.

What:		/sys/class/ubi/ubiX/total_eraseblocks
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Total number of good (not marked as bad) physical eraseblocks on
		the underlying MTD device.

What:		/sys/class/ubi/ubiX/volumes_count
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Count of volumes on this UBI device.

What:		/sys/class/ubi/ubiX/ubiX_Y/
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		The /sys/class/ubi/ubiX/ubiX_0/, /sys/class/ubi/ubiX/ubiX_1/,
		etc directories describe UBI volumes on UBI device X (volumes
		0, 1, etc).

What:		/sys/class/ubi/ubiX/ubiX_Y/alignment
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Volume alignment - the value the logical eraseblock size of
		this volume has to be aligned on. For example, 2048 means that
		logical eraseblock size is multiple of 2048. In other words,
		volume logical eraseblock size is UBI device logical eraseblock
		size aligned to the alignment value.

What:		/sys/class/ubi/ubiX/ubiX_Y/corrupted
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Contains ASCII "0\n" if the UBI volume is OK, and ASCII "1\n"
		if it is corrupted (e.g., due to an interrupted volume update).

What:		/sys/class/ubi/ubiX/ubiX_Y/data_bytes
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		The amount of data this volume contains. This value makes sense
		only for static volumes, and for dynamic volume it equivalent
		to the total volume size in bytes.

What:		/sys/class/ubi/ubiX/ubiX_Y/dev
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Major and minor numbers of the character device corresponding
		to this UBI volume (in <major>:<minor> format).

What:		/sys/class/ubi/ubiX/ubiX_Y/name
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Volume name.

What:		/sys/class/ubi/ubiX/ubiX_Y/reserved_ebs
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Count of physical eraseblock reserved for this volume.
		Equivalent to the volume size in logical eraseblocks.

What:		/sys/class/ubi/ubiX/ubiX_Y/type
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Volume type. Contains ASCII "dynamic\n" for dynamic volumes and
		"static\n" for static volumes.

What:		/sys/class/ubi/ubiX/ubiX_Y/upd_marker
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Contains ASCII "0\n" if the update marker is not set for this
		volume, and "1\n" if it is set. The update marker is set when
		volume update starts, and cleaned when it ends. So the presence
		of the update marker indicates that the volume is being updated
		at the moment of the update was interrupted. The later may be
		checked using the "corrupted" sysfs file.

What:		/sys/class/ubi/ubiX/ubiX_Y/usable_eb_size
Date:		July 2006
KernelVersion:	2.6.22
Contact:	Artem Bityutskiy <<EMAIL>>
Description:
		Logical eraseblock size of this volume. Equivalent to logical
		eraseblock size of the device aligned on the volume alignment
		value.
