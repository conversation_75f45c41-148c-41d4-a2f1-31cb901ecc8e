What:		dv1394 (a.k.a. "OHCI-DV I/O support" for FireWire)
Date:		May 2010 (scheduled), finally removed in kernel v2.6.37
Contact:	<EMAIL>
Description:
	/dev/dv1394/* were character device files, one for each FireWire
	controller and for NTSC and PAL respectively, from which DV data
	could be received by read() or transmitted by write().  A few
	ioctl()s allowed limited control.
	This special-purpose interface has been superseded by libraw1394 +
	libiec61883 which are functionally equivalent, support HDV, and
	transparently work on top of the newer firewire kernel drivers.

Users:
	ffmpeg/libavformat (if configured for DV1394)
