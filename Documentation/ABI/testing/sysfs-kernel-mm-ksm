What:		/sys/kernel/mm/ksm
Date:		September 2009
KernelVersion:	2.6.32
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Interface for Kernel Samepage Merging (KSM)

What:		/sys/kernel/mm/ksm/full_scans
What:		/sys/kernel/mm/ksm/pages_shared
What:		/sys/kernel/mm/ksm/pages_sharing
What:		/sys/kernel/mm/ksm/pages_to_scan
What:		/sys/kernel/mm/ksm/pages_unshared
What:		/sys/kernel/mm/ksm/pages_volatile
What:		/sys/kernel/mm/ksm/run
What:		/sys/kernel/mm/ksm/sleep_millisecs
Date:		September 2009
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Kernel Samepage Merging daemon sysfs interface

		full_scans: how many times all mergeable areas have been
		scanned.

		pages_shared: how many shared pages are being used.

		pages_sharing: how many more sites are sharing them i.e. how
		much saved.

		pages_to_scan: how many present pages to scan before ksmd goes
		to sleep.

		pages_unshared: how many pages unique but repeatedly checked
		for merging.

		pages_volatile: how many pages changing too fast to be placed
		in a tree.

		run: write 0 to disable ksm, read 0 while ksm is disabled.

			- write 1 to run ksm, read 1 while ksm is running.
			- write 2 to disable ksm and unmerge all its pages.

		sleep_millisecs: how many milliseconds ksm should sleep between
		scans.

		See Documentation/mm/ksm.rst for more information.

What:		/sys/kernel/mm/ksm/merge_across_nodes
Date:		January 2013
KernelVersion:	3.9
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Control merging pages across different NUMA nodes.

		When it is set to 0 only pages from the same node are merged,
		otherwise pages from all nodes can be merged together (default).

What:		/sys/kernel/mm/ksm/general_profit
Date:		April 2023
KernelVersion:  6.4
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Measure how effective KSM is.
		general_profit: how effective is KSM. The formula for the
		calculation is in Documentation/admin-guide/mm/ksm.rst.
