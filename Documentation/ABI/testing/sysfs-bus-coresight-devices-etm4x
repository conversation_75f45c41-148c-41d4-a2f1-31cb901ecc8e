What:		/sys/bus/coresight/devices/etm<N>/enable_source
Date:		April 2015
KernelVersion:  4.01
Contact:        <PERSON><PERSON> <<EMAIL>>
Description:	(RW) Enable/disable tracing on this specific trace entiry.
		Enabling a source implies the source has been configured
		properly and a sink has been identidifed for it.  The path
		of coresight components linking the source to the sink is
		configured and managed automatically by the coresight framework.

What:		/sys/bus/coresight/devices/etm<N>/cpu
Date:		April 2015
KernelVersion:	4.01
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(Read) The CPU this tracing entity is associated with.

What:		/sys/bus/coresight/devices/etm<N>/nr_pe_cmp
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu <PERSON>irier <<EMAIL>>
Description:	(Read) Indicates the number of PE comparator inputs that are
		available for tracing.

What:		/sys/bus/coresight/devices/etm<N>/nr_addr_cmp
Date:		April 2015
KernelVersion:	4.01
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(Read) Indicates the number of address comparator pairs that are
		available for tracing.

What:		/sys/bus/coresight/devices/etm<N>/nr_cntr
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Indicates the number of counters that are available for
		tracing.

What:		/sys/bus/coresight/devices/etm<N>/nr_ext_inp
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Indicates how many external inputs are implemented.

What:		/sys/bus/coresight/devices/etm<N>/numcidc
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Indicates the number of Context ID comparators that are
		available for tracing.

What:		/sys/bus/coresight/devices/etm<N>/numvmidc
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Indicates the number of VMID comparators that are available
		for tracing.

What:		/sys/bus/coresight/devices/etm<N>/nrseqstate
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Indicates the number of sequencer states that are
		implemented.

What:		/sys/bus/coresight/devices/etm<N>/nr_resource
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Indicates the number of resource selection pairs that are
		available for tracing.

What:		/sys/bus/coresight/devices/etm<N>/nr_ss_cmp
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Indicates the number of single-shot comparator controls that
		are available for tracing.

What:		/sys/bus/coresight/devices/etm<N>/reset
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(Write) Cancels all configuration on a trace unit and set it back
		to its boot configuration.

What:		/sys/bus/coresight/devices/etm<N>/mode
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls various modes supported by this ETM, for example
		P0 instruction tracing, branch broadcast, cycle counting and
		context ID tracing.

What:		/sys/bus/coresight/devices/etm<N>/pe
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls which PE to trace.

What:		/sys/bus/coresight/devices/etm<N>/event
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls the tracing of arbitrary events from bank 0 to 3.

What:		/sys/bus/coresight/devices/etm<N>/event_instren
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls the behavior of the events in bank 0 to 3.

What:		/sys/bus/coresight/devices/etm<N>/event_ts
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls the insertion of global timestamps in the trace
		streams.

What:		/sys/bus/coresight/devices/etm<N>/syncfreq
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls how often trace synchronization requests occur.

What:		/sys/bus/coresight/devices/etm<N>/cyc_threshold
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Sets the threshold value for cycle counting.

What:		/sys/bus/coresight/devices/etm<N>/bb_ctrl
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls which regions in the memory map are enabled to
		use branch broadcasting.

What:		/sys/bus/coresight/devices/etm<N>/event_vinst
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls instruction trace filtering.

What:		/sys/bus/coresight/devices/etm<N>/s_exlevel_vinst
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) In Secure state, each bit controls whether instruction
		tracing is enabled for the corresponding exception level.

What:		/sys/bus/coresight/devices/etm<N>/ns_exlevel_vinst
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) In non-secure state, each bit controls whether instruction
		tracing is enabled for the corresponding exception level.

What:		/sys/bus/coresight/devices/etm<N>/addr_idx
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Select which address comparator or pair (of comparators) to
		work with.

What:		/sys/bus/coresight/devices/etm<N>/addr_instdatatype
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls what type of comparison the trace unit performs.

What:		/sys/bus/coresight/devices/etm<N>/addr_single
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Used to setup single address comparator values.

What:		/sys/bus/coresight/devices/etm<N>/addr_range
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Used to setup address range comparator values.

What:		/sys/bus/coresight/devices/etm<N>/seq_idx
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Select which sequensor.

What:		/sys/bus/coresight/devices/etm<N>/seq_state
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Use this to set, or read, the sequencer state.

What:		/sys/bus/coresight/devices/etm<N>/seq_event
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Moves the sequencer state to a specific state.

What:		/sys/bus/coresight/devices/etm<N>/seq_reset_event
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Moves the sequencer to state 0 when a programmed event
		occurs.

What:		/sys/bus/coresight/devices/etm<N>/cntr_idx
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Select which counter unit to work with.

What:		/sys/bus/coresight/devices/etm<N>/cntrldvr
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) This sets or returns the reload count value of the
		specific counter.

What:		/sys/bus/coresight/devices/etm<N>/cntr_val
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) This sets or returns the current count value of the
                specific counter.

What:		/sys/bus/coresight/devices/etm<N>/cntr_ctrl
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls the operation of the selected counter.

What:		/sys/bus/coresight/devices/etm<N>/res_idx
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Select which resource selection unit to work with.

What:		/sys/bus/coresight/devices/etm<N>/res_ctrl
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Controls the selection of the resources in the trace unit.

What:		/sys/bus/coresight/devices/etm<N>/ctxid_idx
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Select which context ID comparator to work with.

What:		/sys/bus/coresight/devices/etm<N>/ctxid_pid
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Get/Set the context ID comparator value to trigger on.

What:		/sys/bus/coresight/devices/etm<N>/ctxid_masks
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Mask for all 8 context ID comparator value
		registers (if implemented).

What:		/sys/bus/coresight/devices/etm<N>/vmid_idx
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Select which virtual machine ID comparator to work with.

What:		/sys/bus/coresight/devices/etm<N>/vmid_val
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Get/Set the virtual machine ID comparator value to
		trigger on.

What:		/sys/bus/coresight/devices/etm<N>/vmid_masks
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Mask for all 8 virtual machine ID comparator value
		registers (if implemented).

What:		/sys/bus/coresight/devices/etm<N>/addr_exlevel_s_ns
Date:		December 2019
KernelVersion:	5.5
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Set the Exception Level matching bits for secure and
		non-secure exception levels.

What:		/sys/bus/coresight/devices/etm<N>/vinst_pe_cmp_start_stop
Date:		December 2019
KernelVersion:	5.5
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Access the start stop control register for PE input
		comparators.

What:		/sys/bus/coresight/devices/etm<N>/addr_cmp_view
Date:		December 2019
KernelVersion:	5.5
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the current settings for the selected address
		comparator.

What:		/sys/bus/coresight/devices/etm<N>/sshot_idx
Date:		December 2019
KernelVersion:	5.5
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Select the single shot control register to access.

What:		/sys/bus/coresight/devices/etm<N>/sshot_ctrl
Date:		December 2019
KernelVersion:	5.5
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Access the selected single shot control register.

What:		/sys/bus/coresight/devices/etm<N>/sshot_status
Date:		December 2019
KernelVersion:	5.5
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the current value of the selected single shot
		status register.

What:		/sys/bus/coresight/devices/etm<N>/sshot_pe_ctrl
Date:		December 2019
KernelVersion:	5.5
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Access the selected single show PE comparator control
		register.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcoslsr
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the OS Lock Status Register (0x304).
		The value it taken directly  from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcpdcr
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Power Down Control Register
		(0x310).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcpdsr
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Power Down Status Register
		(0x314).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trclsr
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the SW Lock Status Register
		(0xFB4).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcauthstatus
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Authentication Status Register
		(0xFB8).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcdevid
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Device ID Register
		(0xFC8).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcdevarch
Date:		January 2021
KernelVersion:	5.12
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Device Architecture Register
		(offset 0xFBC).  The value is taken directly read
		from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcdevtype
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Device Type Register
		(0xFCC).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcpidr0
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Peripheral ID0 Register
		(0xFE0).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcpidr1
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Peripheral ID1 Register
		(0xFE4).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcpidr2
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Peripheral ID2 Register
		(0xFE8).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcpidr3
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the Peripheral ID3 Register
		(0xFEC).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trcconfig
Date:		February 2016
KernelVersion:	4.07
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the trace configuration register
		(0x010) as currently set by SW.

What:		/sys/bus/coresight/devices/etm<N>/mgmt/trctraceid
Date:		February 2016
KernelVersion:	4.07
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Print the content of the trace ID register (0x040).

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr0
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the tracing capabilities of the trace unit (0x1E0).
		The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr1
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the tracing capabilities of the trace unit (0x1E4).
		The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr2
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the maximum size of the data value, data address,
		VMID, context ID and instruction address in the trace unit
		(0x1E8).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr3
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the value associated with various resources
		available to the trace unit.  See the Trace Macrocell
		architecture specification for more details (0x1E8).
		The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr4
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns how many resources the trace unit supports (0x1F0).
		The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr5
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns how many resources the trace unit supports (0x1F4).
		The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr8
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the maximum speculation depth of the instruction
		trace stream. (0x180).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr9
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the number of P0 right-hand keys that the trace unit
		can use (0x184).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr10
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the number of P1 right-hand keys that the trace unit
		can use (0x188).  The value is taken directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr11
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the number of special P1 right-hand keys that the
		trace unit can use (0x18C).  The value is taken directly from
		the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr12
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the number of conditional P1 right-hand keys that
		the trace unit can use (0x190).  The value is taken directly
		from the HW.

What:		/sys/bus/coresight/devices/etm<N>/trcidr/trcidr13
Date:		April 2015
KernelVersion:	4.01
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Returns the number of special conditional P1 right-hand keys
		that the trace unit can use (0x194).  The value is taken
		directly from the HW.

What:		/sys/bus/coresight/devices/etm<N>/ts_source
Date:		October 2022
KernelVersion:	6.1
Contact:	Mathieu Poirier <<EMAIL>> or Suzuki K Poulose <<EMAIL>>
Description:	(Read) When FEAT_TRF is implemented, value of TRFCR_ELx.TS used for
		trace session. Otherwise -1 indicates an unknown time source. Check
		trcidr0.tssize to see if a global timestamp is available.
