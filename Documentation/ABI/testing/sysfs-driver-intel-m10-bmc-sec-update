What:		/sys/bus/platform/drivers/intel-m10bmc-sec-update/.../security/sr_root_entry_hash
Date:		Sep 2022
KernelVersion:	5.20
Contact:	<PERSON> <<EMAIL>>
Description:	Read only. Returns the root entry hash for the static
		region if one is programmed, else it returns the
		string: "hash not programmed".  This file is only
		visible if the underlying device supports it.
		Format: string.

What:		/sys/bus/platform/drivers/intel-m10bmc-sec-update/.../security/pr_root_entry_hash
Date:		Sep 2022
KernelVersion:	5.20
Contact:	<PERSON> <<EMAIL>>
Description:	Read only. Returns the root entry hash for the partial
		reconfiguration region if one is programmed, else it
		returns the string: "hash not programmed".  This file
		is only visible if the underlying device supports it.
		Format: string.

What:		/sys/bus/platform/drivers/intel-m10bmc-sec-update/.../security/bmc_root_entry_hash
Date:		Sep 2022
KernelVersion:	5.20
Contact:	<PERSON> <<EMAIL>>
Description:	Read only. Returns the root entry hash for the BMC image
		if one is programmed, else it returns the string:
		"hash not programmed".  This file is only visible if the
		underlying device supports it.
		Format: string.

What:		/sys/bus/platform/drivers/intel-m10bmc-sec-update/.../security/sr_canceled_csks
Date:		Sep 2022
KernelVersion:	5.20
Contact:	Peter Colberg <<EMAIL>>
Description:	Read only. Returns a list of indices for canceled code
		signing keys for the static region. The standard bitmap
		list format is used (e.g. "1,2-6,9").

What:		/sys/bus/platform/drivers/intel-m10bmc-sec-update/.../security/pr_canceled_csks
Date:		Sep 2022
KernelVersion:	5.20
Contact:	Peter Colberg <<EMAIL>>
Description:	Read only. Returns a list of indices for canceled code
		signing keys for the partial reconfiguration region. The
		standard bitmap list format is used (e.g. "1,2-6,9").

What:		/sys/bus/platform/drivers/intel-m10bmc-sec-update/.../security/bmc_canceled_csks
Date:		Sep 2022
KernelVersion:	5.20
Contact:	Peter Colberg <<EMAIL>>
Description:	Read only. Returns a list of indices for canceled code
		signing keys for the BMC.  The standard bitmap list format
		is used (e.g. "1,2-6,9").

What:		/sys/bus/platform/drivers/intel-m10bmc-sec-update/.../security/flash_count
Date:		Sep 2022
KernelVersion:	5.20
Contact:	Peter Colberg <<EMAIL>>
Description:	Read only. Returns number of times the secure update
		staging area has been flashed.
		Format: "%u".
