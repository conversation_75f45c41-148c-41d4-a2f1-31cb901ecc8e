What:		/sys/devices/virtual/memory_tiering/
Date:		August 2022
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	A collection of all the memory tiers allocated.

		Individual memory tier details are contained in subdirectories
		named by the abstract distance of the memory tier.

		/sys/devices/virtual/memory_tiering/memory_tierN/


What:		/sys/devices/virtual/memory_tiering/memory_tierN/
		/sys/devices/virtual/memory_tiering/memory_tierN/nodelist
Date:		August 2022
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Directory with details of a specific memory tier

		This is the directory containing information about a particular
		memory tier, memtierN, where N is derived based on abstract distance.

		A smaller value of N implies a higher (faster) memory tier in the
		hierarchy.

		nodelist: NUMA nodes that are part of this memory tier.

