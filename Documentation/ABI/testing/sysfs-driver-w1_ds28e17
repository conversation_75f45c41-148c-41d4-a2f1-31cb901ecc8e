What:		/sys/bus/w1/devices/19-<id>/speed
Date:		Sep 2017
KernelVersion:	4.14
Contact:	<PERSON> <<EMAIL>>
Description:	When written, this file sets the I2C speed on the connected
		DS28E17 chip. When read, it reads the current setting from
		the DS28E17 chip.

		Valid values: 100, 400, 900 [kBaud].

		Default 100, can be set by w1_ds28e17.speed= module parameter.
Users:		w1_ds28e17 driver

What:		/sys/bus/w1/devices/19-<id>/stretch
Date:		Sep 2017
KernelVersion:	4.14
Contact:	<PERSON> <<EMAIL>>
Description:	When written, this file sets the multiplier used to calculate
		the busy timeout for I2C operations on the connected DS28E17
		chip. When read, returns the current setting.
		Valid values: 1 to 9.

		Default 1, can be set by w1_ds28e17.stretch= module parameter.
Users:		w1_ds28e17 driver
