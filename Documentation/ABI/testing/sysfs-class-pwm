What:		/sys/class/pwm/
Date:		May 2013
KernelVersion:	3.11
Contact:	<PERSON> <PERSON> <<EMAIL>>
Description:
		The pwm/ class sub-directory belongs to the Generic PWM
		Framework and provides a sysfs interface for using PWM
		channels.

What:		/sys/class/pwm/pwmchip<N>/
Date:		May 2013
KernelVersion:	3.11
Contact:	H <PERSON> <<EMAIL>>
Description:
		A /sys/class/pwm/pwmchipN directory is created for each
		probed PWM controller/chip where N is the base of the
		PWM chip.

What:		/sys/class/pwm/pwmchip<N>/npwm
Date:		May 2013
KernelVersion:	3.11
Contact:	H <PERSON> <<EMAIL>>
Description:
		The number of PWM channels supported by the PWM chip.

What:		/sys/class/pwm/pwmchip<N>/export
Date:		May 2013
KernelVersion:	3.11
Contact:	H <PERSON> <<EMAIL>>
Description:
		Exports a PWM channel from the PWM chip for sysfs control.
		Value is between 0 and /sys/class/pwm/pwmchipN/npwm - 1.

What:		/sys/class/pwm/pwmchip<N>/unexport
Date:		May 2013
KernelVersion:	3.11
Contact:	H Hartley Sweeten <<EMAIL>>
Description:
		Unexports a PWM channel.

What:		/sys/class/pwm/pwmchip<N>/pwmX
Date:		May 2013
KernelVersion:	3.11
Contact:	H Hartley Sweeten <<EMAIL>>
Description:
		A /sys/class/pwm/pwmchipN/pwmX directory is created for
		each exported PWM channel where X is the exported PWM
		channel number.

What:		/sys/class/pwm/pwmchip<N>/pwmX/period
Date:		May 2013
KernelVersion:	3.11
Contact:	H Hartley Sweeten <<EMAIL>>
Description:
		Sets the PWM signal period in nanoseconds.

What:		/sys/class/pwm/pwmchip<N>/pwmX/duty_cycle
Date:		May 2013
KernelVersion:	3.11
Contact:	H Hartley Sweeten <<EMAIL>>
Description:
		Sets the PWM signal duty cycle in nanoseconds.

What:		/sys/class/pwm/pwmchip<N>/pwmX/polarity
Date:		May 2013
KernelVersion:	3.11
Contact:	H Hartley Sweeten <<EMAIL>>
Description:
		Sets the output polarity of the PWM signal to "normal" or
		"inversed".

What:		/sys/class/pwm/pwmchip<N>/pwmX/enable
Date:		May 2013
KernelVersion:	3.11
Contact:	H Hartley Sweeten <<EMAIL>>
Description:
		Enable/disable the PWM signal.
		0 is disabled
		1 is enabled

What:		/sys/class/pwm/pwmchip<N>/pwmX/capture
Date:		June 2016
KernelVersion:	4.8
Contact:	Lee Jones <<EMAIL>>
Description:
		Capture information about a PWM signal. The output format is a
		pair unsigned integers (period and duty cycle), separated by a
		single space.
