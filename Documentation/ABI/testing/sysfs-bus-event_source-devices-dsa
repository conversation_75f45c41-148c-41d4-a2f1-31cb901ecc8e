What:		/sys/bus/event_source/devices/dsa*/format
Date:		April 2021
KernelVersion:  5.13
Contact:	<PERSON> <<EMAIL>>
Description:	Read-only.  Attribute group to describe the magic bits
		that go into perf_event_attr.config or
		perf_event_attr.config1 for the IDXD DSA pmu.  (See also
		ABI/testing/sysfs-bus-event_source-devices-format).

		Each attribute in this group defines a bit range in
		perf_event_attr.config or perf_event_attr.config1.
		All supported attributes are listed below (See the
		IDXD DSA Spec for possible attribute values)::

		    event_category = "config:0-3"    - event category
		    event          = "config:4-31"   - event ID

		    filter_wq      = "config1:0-31"  - workqueue filter
		    filter_tc      = "config1:32-39" - traffic class filter
		    filter_pgsz    = "config1:40-43" - page size filter
		    filter_sz      = "config1:44-51" - transfer size filter
		    filter_eng     = "config1:52-59" - engine filter

What:		/sys/bus/event_source/devices/dsa*/cpumask
Date:		April 2021
KernelVersion:  5.13
Contact:	<PERSON> <<EMAIL>>
Description:    Read-only.  This file always returns the cpu to which the
                IDXD DSA pmu is bound for access to all dsa pmu
		performance monitoring events.
