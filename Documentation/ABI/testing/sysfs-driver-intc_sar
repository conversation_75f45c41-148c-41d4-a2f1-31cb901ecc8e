What:		/sys/bus/platform/devices/INTC1092:00/intc_reg
Date:		August 2021
KernelVersion:	5.15
Contact:	Shravan S <<EMAIL>>,
		An Sudhakar <<EMAIL>>
Description:
		Specific Absorption Rate (SAR) regulatory mode is typically
		derived based on information like mcc (Mobile Country Code) and
		mnc (Mobile Network Code) that is available for the currently
		attached LTE network. A userspace application is required to set
		the current SAR regulatory mode on the Dynamic SAR driver using
		this sysfs node. Such an application can also read back using
		this sysfs node, the currently configured regulatory mode value
		from the Dynamic SAR driver.

		Acceptable regulatory modes are:
			==	====
			0	FCC
			1	CE
			2	ISED
			==	====

		- The regulatory mode value has one of the above values.
		- The default regulatory mode used in the driver is 0.

What:		/sys/bus/platform/devices/INTC1092:00/intc_data
Date:		August 2021
KernelVersion:	5.15
Contact:	Shravan S <<EMAIL>>,
		An Sudhakar <<EMAIL>>
Description:
		This sysfs entry is used to retrieve Dynamic SAR information
		emitted/maintained by a BIOS that supports Dynamic SAR.

		The retrieved information is in the order given below:
		- device_mode
		- bandtable_index
		- antennatable_index
		- sartable_index

		The above information is sent as integer values separated
		by a single space. This information can then be pushed to a
		WWAN modem that uses this to control the transmit signal
		level using the Band/Antenna/SAR table index information.
		These parameters are derived/decided by aggregating
		device-mode like laptop/tablet/clamshell etc. and the
		proximity-sensor data available to the embedded controller on
		given host. The regulatory mode configured on Dynamic SAR
		driver also influences these values.

		The userspace applications can poll for changes to this file
		using POLLPRI event on file-descriptor (fd) obtained by opening
		this sysfs entry. Application can then read this information from
		the sysfs node and consume the given information.
