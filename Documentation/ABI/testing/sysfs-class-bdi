What:		/sys/class/bdi/<bdi>/
Date:		January 2008
Contact:	<PERSON> <a.p.zij<PERSON><PERSON>@chello.nl>
Description:

Provide a place in sysfs for the backing_dev_info object.  This allows
setting and retrieving various BDI specific variables.

The <bdi> identifier can be either of the following:

MAJOR:MINOR

	Device number for block devices, or value of st_dev on
	non-block filesystems which provide their own BDI, such as NFS
	and FUSE.

MAJOR:MINOR-fuseblk

	Value of st_dev on fuseblk filesystems.

default

	The default backing dev, used for non-block device backed
	filesystems which do not provide their own BDI.

What:		/sys/class/bdi/<bdi>/read_ahead_kb
Date:		January 2008
Contact:	<PERSON> <<EMAIL>>
Description:
	Size of the read-ahead window in kilobytes

	(read-write)
What:		/sys/class/bdi/<bdi>/min_ratio
Date:		January 2008
Contact:	<PERSON> <a.p.zij<PERSON><EMAIL>>
Description:
	Under normal circumstances each device is given a part of the
	total write-back cache that relates to its current average
	writeout speed in relation to the other devices.

	The 'min_ratio' parameter allows assigning a minimum
	percentage of the write-back cache to a particular device.
	For example, this is useful for providing a minimum QoS.

	(read-write)

What:		/sys/class/bdi/<bdi>/min_ratio_fine
Date:		November 2022
Contact:	Stefan Roesch <<EMAIL>>
Description:
	Under normal circumstances each device is given a part of the
	total write-back cache that relates to its current average
	writeout speed in relation to the other devices.

	The 'min_ratio_fine' parameter allows assigning a minimum reserve
	of the write-back cache to a particular device. The value is
	expressed as part of 1 million. For example, this is useful for
	providing a minimum QoS.

	(read-write)

What:		/sys/class/bdi/<bdi>/max_ratio
Date:		January 2008
Contact:	Peter Zijlstra <<EMAIL>>
Description:
	Allows limiting a particular device to use not more than the
	given percentage of the write-back cache.  This is useful in
	situations where we want to avoid one device taking all or
	most of the write-back cache.  For example in case of an NFS
	mount that is prone to get stuck, or a FUSE mount which cannot
	be trusted to play fair.

	(read-write)

What:		/sys/class/bdi/<bdi>/max_ratio_fine
Date:		November 2022
Contact:	Stefan Roesch <<EMAIL>>
Description:
	Allows limiting a particular device to use not more than the
	given value of the write-back cache.  The value is given as part
	of 1 million. This is useful in situations where we want to avoid
	one device taking all or most of the write-back cache.  For example
	in case of an NFS mount that is prone to get stuck, or a FUSE mount
	which cannot be trusted to play fair.

	(read-write)

What:		/sys/class/bdi/<bdi>/min_bytes
Date:		October 2022
Contact:	Stefan Roesch <<EMAIL>>
Description:
	Under normal circumstances each device is given a part of the
	total write-back cache that relates to its current average
	writeout speed in relation to the other devices.

	The 'min_bytes' parameter allows assigning a minimum
	percentage of the write-back cache to a particular device
	expressed in bytes.
	For example, this is useful for providing a minimum QoS.

	(read-write)

What:		/sys/class/bdi/<bdi>/max_bytes
Date:		October 2022
Contact:	Stefan Roesch <<EMAIL>>
Description:
	Allows limiting a particular device to use not more than the
	given 'max_bytes' of the write-back cache.  This is useful in
	situations where we want to avoid one device taking all or
	most of the write-back cache.  For example in case of an NFS
	mount that is prone to get stuck, a FUSE mount which cannot be
	trusted to play fair, or a nbd device.

	(read-write)

What:		/sys/class/bdi/<bdi>/strict_limit
Date:		October 2022
Contact:	Stefan Roesch <<EMAIL>>
Description:
	Forces per-BDI checks for the share of given device in the write-back
	cache even before the global background dirty limit is reached. This
	is useful in situations where the global limit is much higher than
	affordable for given relatively slow (or untrusted) device. Turning
	strictlimit on has no visible effect if max_ratio is equal to 100%.

	(read-write)
What:		/sys/class/bdi/<bdi>/stable_pages_required
Date:		January 2008
Contact:	Peter Zijlstra <<EMAIL>>
Description:
	If set, the backing device requires that all pages comprising a write
	request must not be changed until writeout is complete.

	(read-only)
