What: 		/sys/class/firmware/.../data
Date:		July 2022
KernelVersion:	5.19
Contact:	<PERSON> Weight <<EMAIL>>
Description:	The data sysfs file is used for firmware-fallback and for
		firmware uploads. Cat a firmware image to this sysfs file
		after you echo 1 to the loading sysfs file. When the firmware
		image write is complete, echo 0 to the loading sysfs file. This
		sequence will signal the completion of the firmware write and
		signal the lower-level driver that the firmware data is
		available.

What: 		/sys/class/firmware/.../cancel
Date:		July 2022
KernelVersion:	5.19
Contact:	Russ Weight <<EMAIL>>
Description:	Write-only. For firmware uploads, write a "1" to this file to
		request that the transfer of firmware data to the lower-level
		device be canceled. This request will be rejected (EBUSY) if
		the update cannot be canceled (e.g. a FLASH write is in
		progress) or (ENODEV) if there is no firmware update in progress.

What: 		/sys/class/firmware/.../error
Date:		July 2022
KernelVersion:	5.19
Contact:	<PERSON> Weight <<EMAIL>>
Description:	Read-only. Returns a string describing a failed firmware
		upload. This string will be in the form of <STATUS>:<ERROR>,
		where <STATUS> will be one of the status strings described
		for the status sysfs file and <ERROR> will be one of the
		following: "hw-error", "timeout", "user-abort", "device-busy",
		"invalid-file-size", "read-write-error", "flash-wearout". The
		error sysfs file is only meaningful when the current firmware
		upload status is "idle". If this file is read while a firmware
		transfer is in progress, then the read will fail with EBUSY.

What: 		/sys/class/firmware/.../loading
Date:		July 2022
KernelVersion:	5.19
Contact:	Russ Weight <<EMAIL>>
Description:	The loading sysfs file is used for both firmware-fallback and
		for firmware uploads. Echo 1 onto the loading file to indicate
		you are writing a firmware file to the data sysfs node. Echo
		-1 onto this file to abort the data write or echo 0 onto this
		file to indicate that the write is complete. For firmware
		uploads, the zero value also triggers the transfer of the
		firmware data to the lower-level device driver.

What: 		/sys/class/firmware/.../remaining_size
Date:		July 2022
KernelVersion:	5.19
Contact:	Russ Weight <<EMAIL>>
Description:	Read-only. For firmware upload, this file contains the size
		of the firmware data that remains to be transferred to the
		lower-level device driver. The size value is initialized to
		the full size of the firmware image that was previously
		written to the data sysfs file. This value is periodically
		updated during the "transferring" phase of the firmware
		upload.
		Format: "%u".

What: 		/sys/class/firmware/.../status
Date:		July 2022
KernelVersion:	5.19
Contact:	Russ Weight <<EMAIL>>
Description:	Read-only. Returns a string describing the current status of
		a firmware upload. The string will be one of the following:
		idle, "receiving", "preparing", "transferring", "programming".

What: 		/sys/class/firmware/.../timeout
Date:		July 2022
KernelVersion:	5.19
Contact:	Russ Weight <<EMAIL>>
Description:	This file supports the timeout mechanism for firmware
		fallback.  This file has no affect on firmware uploads. For
		more information on timeouts please see the documentation
		for firmware fallback.
