What:		/sys/class/vduse/
Date:		Oct 2021
KernelVersion:	5.15
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The vduse/ class sub-directory belongs to the VDUSE
		framework and provides a sysfs interface for configuring
		VDUSE devices.

What:		/sys/class/vduse/control/
Date:		Oct 2021
KernelVersion:	5.15
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		This directory entry is created for the control device
		of VDUSE framework.

What:		/sys/class/vduse/<device-name>/
Date:		Oct 2021
KernelVersion:	5.15
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		This directory entry is created when a VDUSE device is
		created via the control device.

What:		/sys/class/vduse/<device-name>/msg_timeout
Date:		Oct 2021
KernelVersion:	5.15
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		(RW) The timeout (in seconds) for waiting for the control
		message's response from userspace. Default value is 30s.
		Writing a '0' to the file means to disable the timeout.
