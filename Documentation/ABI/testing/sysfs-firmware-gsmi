What:		/sys/firmware/gsmi
Date:		March 2011
Contact:	<PERSON> <<EMAIL>>
Description:
		Some servers used internally at Google have firmware
		that provides callback functionality via explicit SMI
		triggers.  Some of the callbacks are similar to those
		provided by the EFI runtime services page, but due to
		historical reasons this different entry-point has been
		used.

		The gsmi driver implements the kernel's abstraction for
		these firmware callbacks.  Currently, this functionality
		is limited to handling the system event log and getting
		access to EFI-style variables stored in nvram.

		Layout:

		/sys/firmware/gsmi/vars:

			This directory has the same layout (and
			underlying implementation as /sys/firmware/efi/vars.
			See `Documentation/ABI/*/sysfs-firmware-efi-vars`
			for more information on how to interact with
			this structure.

		/sys/firmware/gsmi/append_to_eventlog - write-only:

			This file takes a binary blob and passes it onto
			the firmware to be timestamped and appended to
			the system eventlog.  The binary format is
			interpreted by the firmware and may change from
			platform to platform.  The only kernel-enforced
			requirement is that the blob be prefixed with a
			32bit host-endian type used as part of the
			firmware call.

		/sys/firmware/gsmi/clear_config - write-only:

			Writing any value to this file will cause the
			entire firmware configuration to be reset to
			"factory defaults".  Callers should assume that
			a reboot is required for the configuration to be
			cleared.

		/sys/firmware/gsmi/clear_eventlog - write-only:

			This file is used to clear out a portion/the
			whole of the system event log.  Values written
			should be values between 1 and 100 inclusive (in
			ASCII) representing the fraction of the log to
			clear.  Not all platforms support fractional
			clearing though, and this writes to this file
			will error out if the firmware doesn't like your
			submitted fraction.

			Callers should assume that a reboot is needed
			for this operation to complete.
