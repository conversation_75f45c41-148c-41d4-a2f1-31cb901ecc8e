What:		/sys/bus/soundwire/devices/sdw-master-<N>/revision
		/sys/bus/soundwire/devices/sdw-master-<N>/clk_stop_modes
		/sys/bus/soundwire/devices/sdw-master-<N>/clk_freq
		/sys/bus/soundwire/devices/sdw-master-<N>/clk_gears
		/sys/bus/soundwire/devices/sdw-master-<N>/default_col
		/sys/bus/soundwire/devices/sdw-master-<N>/default_frame_rate
		/sys/bus/soundwire/devices/sdw-master-<N>/default_row
		/sys/bus/soundwire/devices/sdw-master-<N>/dynamic_shape
		/sys/bus/soundwire/devices/sdw-master-<N>/err_threshold
		/sys/bus/soundwire/devices/sdw-master-<N>/max_clk_freq

Date:		April 2020

Contact:	<PERSON>-<PERSON> <<EMAIL>>
		Bard Liao <<EMAIL>>
		Vinod Koul <<EMAIL>>

Description:	SoundWire Master-N DisCo properties.
		These properties are defined by MIPI DisCo Specification
		for SoundWire. They define various properties of the Master
		and are used by the bus to configure the Master. clk_stop_modes
		is a bitmask for simplifications and combines the
		clock-stop-mode0 and clock-stop-mode1 properties.
