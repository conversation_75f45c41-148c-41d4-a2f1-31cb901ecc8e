What:		/sys/class/tpm/tpmX/ppi/
Date:		August 2012
KernelVersion:	3.6
Contact:	<EMAIL>
Description:
		This folder includes the attributes related with PPI (Physical
		Presence Interface). Only if TPM is supported by BIOS, this
		folder makes sense. The folder path can be got by command
		'find /sys/ -name 'pcrs''. For the detail information of PPI,
		please refer to the PPI specification from

		http://www.trustedcomputinggroup.org/

		In Linux 4.2 ppi was moved to the character device directory.
		A symlink from tpmX/device/ppi to tpmX/ppi to provide backwards
		compatibility.

What:		/sys/class/tpm/tpmX/ppi/version
Date:		August 2012
Contact:	<EMAIL>
Description:
		This attribute shows the version of the PPI supported by the
		platform.
		This file is readonly.

What:		/sys/class/tpm/tpmX/ppi/request
Date:		August 2012
Contact:	<EMAIL>
Description:
		This attribute shows the request for an operation to be
		executed in the pre-OS environment. It is the only input from
		the OS to the pre-OS environment. The request should be an
		integer value range from 1 to 160, and 0 means no request.
		This file can be read and written.

What:		/sys/class/tpm/tpmX/ppi/response
Date:		August 2012
Contact:	<EMAIL>
Description:
		This attribute shows the response to the most recent operation
		request it acted upon. The format is "<request> <response num>
		: <response description>".
		This file is readonly.

What:		/sys/class/tpm/tpmX/ppi/transition_action
Date:		August 2012
Contact:	<EMAIL>
Description:
		This attribute shows the platform-specific action that should
		take place in order to transition to the BIOS for execution of
		a requested operation. The format is "<action num>: <action
		description>".
		This file is readonly.

What:		/sys/class/tpm/tpmX/ppi/tcg_operations
Date:		August 2012
Contact:	<EMAIL>
Description:
		This attribute shows whether it is allowed to request an
		operation to be executed in the pre-OS environment by the BIOS
		for the requests defined by TCG, i.e. requests from 1 to 22.
		The format is "<request> <status num>: <status description>".
		This attribute is only supported by PPI version 1.2+.
		This file is readonly.

What:		/sys/class/tpm/tpmX/ppi/vs_operations
Date:		August 2012
Contact:	<EMAIL>
Description:
		This attribute shows whether it is allowed to request an
		operation to be executed in the pre-OS environment by the BIOS
		for the verdor specific requests, i.e. requests from 128 to
		255. The format is same with tcg_operations. This attribute
		is also only supported by PPI version 1.2+.
		This file is readonly.
