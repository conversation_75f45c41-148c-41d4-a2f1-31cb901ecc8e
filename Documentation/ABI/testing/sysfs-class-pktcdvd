sysfs interface
---------------
The pktcdvd module (packet writing driver) creates the following files in the
sysfs: (<devid> is in the format major:minor)

What:		/sys/class/pktcdvd/add
What:		/sys/class/pktcdvd/remove
What:		/sys/class/pktcdvd/device_map
Date:		Oct. 2006
KernelVersion:	2.6.20
Contact:	<PERSON> <<EMAIL>>
Description:

		==========	==============================================
		add		(WO) Write a block device id (major:minor) to
				create a new pktcdvd device and map it to the
				block device.

		remove		(WO) Write the pktcdvd device id (major:minor)
				to remove the pktcdvd device.

		device_map	(RO) Shows the device mapping in format:
				pktcdvd[0-7] <pktdevid> <blkdevid>
		==========	==============================================


What:		/sys/class/pktcdvd/pktcdvd[0-7]/dev
What:		/sys/class/pktcdvd/pktcdvd[0-7]/uevent
Date:		Oct. 2006
KernelVersion:	2.6.20
Contact:	Thomas Maier <<EMAIL>>
Description:
		dev:	(RO) Device id

		uevent:	(WO) To send a uevent


What:		/sys/class/pktcdvd/pktcdvd[0-7]/stat/packets_started
What:		/sys/class/pktcdvd/pktcdvd[0-7]/stat/packets_finished
What:		/sys/class/pktcdvd/pktcdvd[0-7]/stat/kb_written
What:		/sys/class/pktcdvd/pktcdvd[0-7]/stat/kb_read
What:		/sys/class/pktcdvd/pktcdvd[0-7]/stat/kb_read_gather
What:		/sys/class/pktcdvd/pktcdvd[0-7]/stat/reset
Date:		Oct. 2006
KernelVersion:	2.6.20
Contact:	Thomas Maier <<EMAIL>>
Description:
		packets_started:	(RO) Number of started packets.

		packets_finished:	(RO) Number of finished packets.

		kb_written:		(RO) kBytes written.

		kb_read:		(RO) kBytes read.

		kb_read_gather:		(RO) kBytes read to fill write packets.

		reset:			(WO) Write any value to it to reset
					pktcdvd device statistic values, like
					bytes read/written.


What:		/sys/class/pktcdvd/pktcdvd[0-7]/write_queue/size
What:		/sys/class/pktcdvd/pktcdvd[0-7]/write_queue/congestion_off
What:		/sys/class/pktcdvd/pktcdvd[0-7]/write_queue/congestion_on
Date:		Oct. 2006
KernelVersion:	2.6.20
Contact:	Thomas Maier <<EMAIL>>
Description:
		==============	================================================
		size		(RO) Contains the size of the bio write queue.

		congestion_off	(RW) If bio write queue size is below this mark,
				accept new bio requests from the block layer.

		congestion_on	(RW) If bio write queue size is higher as this
				mark, do no longer accept bio write requests
				from the block layer and wait till the pktcdvd
				device has processed enough bio's so that bio
				write queue size is below congestion off mark.
				A value of <= 0 disables congestion control.
		==============	================================================


Example:
--------
To use the pktcdvd sysfs interface directly, you can do::

    # create a new pktcdvd device mapped to /dev/hdc
    echo "22:0" >/sys/class/pktcdvd/add
    cat /sys/class/pktcdvd/device_map
    # assuming device pktcdvd0 was created, look at stat's
    cat /sys/class/pktcdvd/pktcdvd0/stat/kb_written
    # print the device id of the mapped block device
    fgrep pktcdvd0 /sys/class/pktcdvd/device_map
    # remove device, using pktcdvd0 device id   253:0
    echo "253:0" >/sys/class/pktcdvd/remove
