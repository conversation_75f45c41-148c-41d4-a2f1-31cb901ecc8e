What:		/sys/devices/platform/silicom-platform/uc_version
Date:		November 2023
KernelVersion:	6.7
Contact:	<PERSON> <<EMAIL>>
Description:
		This file allows to read microcontroller firmware
		version of current platform.

What:		/sys/devices/platform/silicom-platform/power_cycle
Date:		November 2023
KernelVersion:	6.7
Contact:	<PERSON> <<EMAIL>>
Description:
		This file allow user to power cycle the platform.
		Default value is 0; when set to 1, it powers down
		the platform, waits 5 seconds, then powers on the
		device. It returns to default value after power cycle.

		0 - default value.

What:		/sys/devices/platform/silicom-platform/efuse_status
Date:		November 2023
KernelVersion:	6.7
Contact:	<PERSON> <<EMAIL>>
Description:
		This file is read only. It returns the current
		OTP status:

		0 - not programmed.
		1 - programmed.
