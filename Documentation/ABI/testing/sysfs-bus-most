What:		/sys/bus/most/devices/<dev>/description
Date:		March 2017
KernelVersion:	4.15
Contact:	<PERSON> <<EMAIL>>
Description:
		Provides information about the physical location of the
		device. Hardware attached via USB, for instance,
		might return <1-1.1:1.0>
Users:

What:		/sys/bus/most/devices/<dev>/interface
Date:		March 2017
KernelVersion:	4.15
Contact:	<PERSON> <<EMAIL>>
Description:
		Indicates the type of peripheral interface the device uses.
Users:

What:		/sys/bus/most/devices/<dev>/dci
Date:		June 2016
KernelVersion:	4.15
Contact:	<PERSON> <<EMAIL>>
Description:
		If the network interface controller is attached via USB, a dci
		directory is created that allows applications to read and
		write the controller's DCI registers.
Users:

What:		/sys/bus/most/devices/<dev>/dci/arb_address
Date:		June 2016
KernelVersion:	4.15
Contact:	<PERSON> <<EMAIL>>
Description:
		This is used to set an arbitrary DCI register address an
		application wants to read from or write to.
Users:

What:		/sys/bus/most/devices/<dev>/dci/arb_value
Date:		June 2016
KernelVersion:	4.15
Contact:	<PERSON> <<EMAIL>>
Description:
		This is used to read and write the DCI register whose address
		is stored in arb_address.
Users:

What:		/sys/bus/most/devices/<dev>/dci/mep_eui48_hi
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to check and configure the MAC address.
Users:

What:		/sys/bus/most/devices/<dev>/dci/mep_eui48_lo
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to check and configure the MAC address.
Users:

What:		/sys/bus/most/devices/<dev>/dci/mep_eui48_mi
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to check and configure the MAC address.
Users:

What:		/sys/bus/most/devices/<dev>/dci/mep_filter
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to check and configure the MEP filter address.
Users:

What:		/sys/bus/most/devices/<dev>/dci/mep_hash0
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to check and configure the MEP hash table.
Users:

What:		/sys/bus/most/devices/<dev>/dci/mep_hash1
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to check and configure the MEP hash table.
Users:

What:		/sys/bus/most/devices/<dev>/dci/mep_hash2
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to check and configure the MEP hash table.
Users:

What:		/sys/bus/most/devices/<dev>/dci/mep_hash3
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to check and configure the MEP hash table.
Users:

What:		/sys/bus/most/devices/<dev>/dci/ni_state
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the current network interface state.
Users:

What:		/sys/bus/most/devices/<dev>/dci/node_address
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the current node address.
Users:

What:		/sys/bus/most/devices/<dev>/dci/node_position
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the current node position.
Users:

What:		/sys/bus/most/devices/<dev>/dci/packet_bandwidth
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the configured packet bandwidth.
Users:

What:		/sys/bus/most/devices/<dev>/dci/sync_ep
Date:		June 2016
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Triggers the controller's synchronization process for a certain
		endpoint.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		For every channel of the device a directory is created, whose
		name is dictated by the HDM. This enables an application to
		collect information about the channel's capabilities and
		configure it.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/available_datatypes
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the data types the channel can transport.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/available_directions
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the directions the channel is capable of.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/number_of_packet_buffers
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the number of packet buffers the channel can
		handle.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/number_of_stream_buffers
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the number of streaming buffers the channel can
		handle.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/size_of_packet_buffer
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the size of a packet buffer the channel can
		handle.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/size_of_stream_buffer
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates the size of a streaming buffer the channel can
		handle.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/set_number_of_buffers
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is to read back the configured number of buffers of
		the channel.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/set_buffer_size
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is to read back the configured buffer size of the channel.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/set_direction
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is to read back the configured direction of the channel.
		The following strings will be accepted::

			'tx',
			'rx'
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/set_datatype
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is to read back the configured data type of the channel.
		The following strings will be accepted::

			'control',
			'async',
			'sync',
			'isoc_avp'
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/set_subbuffer_size
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is to read back the configured subbuffer size of
		the channel.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/set_packets_per_xact
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is to read back the configured number of packets per
		transaction of the channel. This is only applicable when
		connected via USB.
Users:

What:		/sys/bus/most/devices/<dev>/<channel>/channel_starving
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		Indicates whether channel ran out of buffers.
Users:

What:		/sys/bus/most/drivers/most_core/components
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to retrieve a list of registered components.
Users:

What:		/sys/bus/most/drivers/most_core/links
Date:		March 2017
KernelVersion:	4.15
Contact:	Christian Gromm <<EMAIL>>
Description:
		This is used to retrieve a list of established links.
Users:
