What:		/sys/devices/*/xenbus/event_channels
Date:		February 2021
Contact:	Xen Developers mailing list <<EMAIL>>
Description:
		Number of Xen event channels associated with a kernel based
		paravirtualized device frontend or backend.

What:		/sys/devices/*/xenbus/events
Date:		February 2021
Contact:	Xen Developers mailing list <<EMAIL>>
Description:
		Total number of Xen events received for a Xen pv device
		frontend or backend.

What:		/sys/devices/*/xenbus/jiffies_eoi_delayed
Date:		February 2021
Contact:	Xen Developers mailing list <<EMAIL>>
Description:
		Summed up time in jiffies the EOI of an interrupt for a Xen
		pv device has been delayed in order to avoid stalls due to
		event storms. This value rising is a first sign for a rogue
		other end of the pv device.

What:		/sys/devices/*/xenbus/spurious_events
Date:		February 2021
Contact:	Xen Developers mailing list <<EMAIL>>
Description:
		Number of events received for a Xen pv device which did not
		require any action. Too many spurious events in a row will
		trigger delayed EOI processing.

What:		/sys/devices/*/xenbus/spurious_threshold
Date:		February 2021
Contact:	Xen Developers mailing list <<EMAIL>>
Description:
		Controls the tolerated number of subsequent spurious events
		before delayed EOI processing is triggered for a Xen pv
		device. Default is 1. This can be modified in case the other
		end of the pv device is issuing spurious events on a regular
		basis and is known not to be malicious on purpose. Raising
		the value for such cases can improve pv device performance.
