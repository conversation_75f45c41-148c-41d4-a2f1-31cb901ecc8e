What:		/sys/class/leds/<tty_led>/ttyname
Date:		Dec 2020
KernelVersion:	5.10
Contact:	<EMAIL>
Description:
		Specifies the tty device name of the triggering tty

What:		/sys/class/leds/<tty_led>/rx
Date:		February 2024
KernelVersion:	6.8
Description:
		Signal reception (rx) of data on the named tty device.
		If set to 0, the LED will not blink on reception.
		If set to 1 (default), the LED will blink on reception.

What:		/sys/class/leds/<tty_led>/tx
Date:		February 2024
KernelVersion:	6.8
Description:
		Signal transmission (tx) of data on the named tty device.
		If set to 0, the LED will not blink on transmission.
		If set to 1 (default), the LED will blink on transmission.

What:		/sys/class/leds/<tty_led>/cts
Date:		February 2024
KernelVersion:	6.8
Description:
		CTS = Clear To Send
		DCE is ready to accept data from the DTE.
		If the line state is detected, the LED is switched on.
		If set to 0 (default), the LED will not evaluate CTS.
		If set to 1, the LED will evaluate CTS.

What:		/sys/class/leds/<tty_led>/dsr
Date:		February 2024
KernelVersion:	6.8
Description:
		DSR = Data Set Ready
		DCE is ready to receive and send data.
		If the line state is detected, the LED is switched on.
		If set to 0 (default), the LED will not evaluate DSR.
		If set to 1, the LED will evaluate DSR.

What:		/sys/class/leds/<tty_led>/dcd
Date:		February 2024
KernelVersion:	6.8
Description:
		DCD = Data Carrier Detect
		DTE is receiving a carrier from the DCE.
		If the line state is detected, the LED is switched on.
		If set to 0 (default), the LED will not evaluate CAR (DCD).
		If set to 1, the LED will evaluate CAR (DCD).

What:		/sys/class/leds/<tty_led>/rng
Date:		February 2024
KernelVersion:	6.8
Description:
		RNG = Ring Indicator
		DCE has detected an incoming ring signal on the telephone
		line. If the line state is detected, the LED is switched on.
		If set to 0 (default), the LED will not evaluate RNG.
		If set to 1, the LED will evaluate RNG.
