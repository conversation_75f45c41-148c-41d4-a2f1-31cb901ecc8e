What:		/sys/class/net/<iface>/termination
Date:		May 2010
KernelVersion:	2.6.35
Contact:	<PERSON> <<EMAIL>>
Description:
		Value representing the can bus termination

		Default: 1 (termination active)
		Reading: get actual termination state
		Writing: set actual termination state (0=no termination, 1=termination active)

What:		/sys/class/net/<iface>/fwinfo
Date:		May 2015
KernelVersion:	3.19
Contact:	<PERSON> <<EMAIL>>
Description:
		Firmware stamp of ican3 module
		Read-only: 32 byte string identification of the ICAN3 module
		(known values: "JANZ-ICAN3 ICANOS 1.xx", "JANZ-ICAN3 CAL/CANopen 1.xx")
