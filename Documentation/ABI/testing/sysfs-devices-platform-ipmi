What:		/sys/devices/platform/ipmi_bmc.*/firmware_revision
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) The major and minor revision of the firmware.


What:		/sys/devices/platform/ipmi_bmc.*/aux_firmware_revision
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) Holds additional information about the firmware revision,
		such as boot block or internal data structure version numbers.
		The meanings of the numbers are specific to the vendor
		identified by Manufacturer ID.


What:		/sys/devices/platform/ipmi_bmc.*/revision
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) Device revision. Useful for identifying if significant
		hardware changes have been made to the implementation of the
		management controller.


What:		/sys/devices/platform/ipmi_bmc.*/provides_device_sdrs
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) Indicates whether device provides device sensor data
		records (1) or not (0).


What:		/sys/devices/platform/ipmi_bmc.*/device_id
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) Device id is specified by the manufacturer identified by
		the Manufacturer ID field. This field allows controller specific
		software to identify the unique application command, OEM
		fields, and functionality that are provided by the controller


What:		/sys/devices/platform/ipmi_bmc.*/additional_device_support
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) Lists the IPMI ‘logical device’ commands and functions
		that the controller supports that are in addition to the
		mandatory IPM and Application commands.


What:		/sys/devices/platform/ipmi_bmc.*/ipmi_version
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) Displays the IPMI Command Specification Version.


What:		/sys/devices/platform/ipmi_bmc.*/manufacturer_id
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) Identifies the manufacturer responsible for the
		specification of functionality of the vendor (OEM)-specific
		commands, codes, and interfaces used in the controller.


What:		/sys/devices/platform/ipmi_bmc.*/product_id
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) Displays a number that identifies a particular system,
		module, add-in card, or board set. The number is specified
		according to the manufacturer given by Manufacturer ID.

For detailed definitions of the above attributes, refer to section 20.1 'Get
Device ID Command' of the IPMI specification v2.0.


What:		/sys/devices/platform/ipmi_bmc.*/guid
Date:		Mar, 2006
KernelVersion:	v2.6.17
Contact:	<EMAIL>
Description:
		(RO) A GUID (Globally Unique ID), also referred to as a UUID
		(Universally Unique Identifier), for the management controller,
		as described in section 20.8 'Get Device GUID Command' of the
		IPMI specification v2.0.


What:		/sys/devices/platform/ipmi_si.*/type
Date:		Sep, 2017
KernelVersion:	v4.15
Contact:	<EMAIL>
Description:
		(RO) The device interface for IPMI "kcs", "smic", "bt" or
		"invalid"

What:		/sys/devices/platform/ipmi_si.*/idles
What:		/sys/devices/platform/ipmi_si.*/watchdog_pretimeouts
What:		/sys/devices/platform/ipmi_si.*/complete_transactions
What:		/sys/devices/platform/ipmi_si.*/events
What:		/sys/devices/platform/ipmi_si.*/interrupts
What:		/sys/devices/platform/ipmi_si.*/hosed_count
What:		/sys/devices/platform/ipmi_si.*/long_timeouts
What:		/sys/devices/platform/ipmi_si.*/flag_fetches
What:		/sys/devices/platform/ipmi_si.*/attentions
What:		/sys/devices/platform/ipmi_si.*/incoming_messages
What:		/sys/devices/platform/ipmi_si.*/short_timeouts
Date:		Sep, 2017
KernelVersion:	v4.15
Contact:	<EMAIL>
Description:

		======================	========================================
		idles			(RO) Number of times the interface was
					idle while being polled.

		watchdog_pretimeouts	(RO) Number of watchdog pretimeouts.

		complete_transactions	(RO) Number of completed messages.

		events			(RO) Number of IPMI events received from
					the hardware.

		interrupts		(RO) Number of interrupts the driver
					handled.

		hosed_count		(RO) Number of times the hardware didn't
					follow the state machine.

		long_timeouts		(RO) Number of times the driver
					requested a timer while nothing was in
					progress.

		flag_fetches		(RO) Number of times the driver
					requested flags from the hardware.

		attentions		(RO) Number of time the driver got an
					ATTN from the hardware.

		incoming_messages	(RO) Number of asynchronous messages
					received.

		short_timeouts		(RO) Number of times the driver
					requested a timer while an operation was
					in progress.
		======================	========================================


What:		/sys/devices/platform/ipmi_si.*/interrupts_enabled
Date:		Sep, 2017
KernelVersion:	v4.15
Contact:	<EMAIL>
Description:
		(RO) Indicates whether interrupts are enabled or not. The driver
		disables interrupts when it gets into a situation where it
		cannot handle messages due to lack of memory. Once that
		situation clears up, it will re-enable interrupts.


What:		/sys/devices/platform/ipmi_si.*/params
Date:		Sep, 2017
KernelVersion:	v4.15
Contact:	<EMAIL>
Description:
		[to be documented]


What:		/sys/devices/platform/dmi-ipmi-ssif.*/type
Date:		Sep, 2017
KernelVersion:	v4.15
Contact:	<EMAIL>
Description:
		(RO) Shows the IMPI device interface type - "ssif" here.


What:		/sys/devices/platform/dmi-ipmi-ssif.*/hosed
What:		/sys/devices/platform/dmi-ipmi-ssif.*/alerts
What:		/sys/devices/platform/dmi-ipmi-ssif.*/sent_messages
What:		/sys/devices/platform/dmi-ipmi-ssif.*/sent_messages_parts
What:		/sys/devices/platform/dmi-ipmi-ssif.*/received_messages
What:		/sys/devices/platform/dmi-ipmi-ssif.*/received_message_parts
What:		/sys/devices/platform/dmi-ipmi-ssif.*/events
What:		/sys/devices/platform/dmi-ipmi-ssif.*/watchdog_pretimeouts
What:		/sys/devices/platform/dmi-ipmi-ssif.*/flag_fetches
What:		/sys/devices/platform/dmi-ipmi-ssif.*/send_retries
What:		/sys/devices/platform/dmi-ipmi-ssif.*/receive_retries
What:		/sys/devices/platform/dmi-ipmi-ssif.*/send_errors
What:		/sys/devices/platform/dmi-ipmi-ssif.*/receive_errors
Date:		Sep, 2017
KernelVersion:	v4.15
Contact:	<EMAIL>
Description:
		======================	========================================
		hosed			(RO) Number of times the hardware didn't
					follow the state machine.

		alerts			(RO) Number of alerts received.

		sent_messages		(RO) Number of total messages sent.

		sent_message_parts	(RO) Number of message parts sent.
					Messages may be broken into parts if
					they are long.

		received_messages	(RO) Number of message responses
					received.

		received_message_parts	(RO) Number of message fragments
					received.

		events			(RO) Number of received events.

		watchdog_pretimeouts	(RO) Number of watchdog pretimeouts.

		flag_fetches		(RO) Number of times a flag fetch was
					requested.

		send_retries		(RO) Number of time a message was
					retried.

		receive_retries		(RO) Number of times the receive of a
					message was retried.

		send_errors		(RO) Number of times the send of a
					message failed.

		receive_errors		(RO) Number of errors in receiving
					messages.
		======================	========================================
