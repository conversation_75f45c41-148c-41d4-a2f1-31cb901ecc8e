What:		/sys/bus/iio/devices/iio:deviceX/in_altvoltageY_invert
Date:		October 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:
		The DAC is used to find the peak level of an alternating
		voltage input signal by a binary search using the output
		of a comparator wired to an interrupt pin. Like so::

		                           _
		                          | \
		     input +------>-------|+ \
		                          |   \
		            .-------.     |    }---.
		            |       |     |   /    |
		            |    dac|-->--|- /     |
		            |       |     |_/      |
		            |       |              |
		            |       |              |
		            |    irq|------<-------'
		            |       |
		            '-------'

		The boolean invert attribute (0/1) should be set when the
		input signal is centered around the maximum value of the
		dac instead of zero. The envelope detector will search
		from below in this case and will also invert the result.

		The edge/level of the interrupt is also switched to its
		opposite value.

What:		/sys/bus/iio/devices/iio:deviceX/in_altvoltageY_compare_interval
Date:		October 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:
		Number of milliseconds to wait for the comparator in each
		step of the binary search for the input peak level. Needs
		to relate to the frequency of the input signal.
