What:           /sys/.../uevent
Date:           May 2017
KernelVersion:  4.13
Contact:        Linux kernel mailing list <<EMAIL>>
Description:
                Enable passing additional variables for synthetic uevents that
                are generated by writing /sys/.../uevent file.

                Recognized extended format is::

			ACTION [UUID [KEY=VALUE ...]

                The ACTION is compulsory - it is the name of the uevent
                action (``add``, ``change``, ``remove``). There is no change
                compared to previous functionality here. The rest of the
                extended format is optional.

                You need to pass UUID first before any KEY=VALUE pairs.
                The UUID must be in ``xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx``
                format where 'x' is a hex digit. The UUID is considered to be
                a transaction identifier so it's possible to use the same UUID
                value for one or more synthetic uevents in which case we
                logically group these uevents together for any userspace
                listeners. The UUID value appears in uevent as
                ``SYNTH_UUID=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`` environment
                variable.

                If UUID is not passed in, the generated synthetic uevent gains
                ``SYNTH_UUID=0`` environment variable automatically.

                The KEY=VALUE pairs can contain alphanumeric characters only.

                It's possible to define zero or more pairs - each pair is then
                delimited by a space character ' '. Each pair appears in
                synthetic uevent as ``SYNTH_ARG_KEY=VALUE``. That means the KEY
                name gains ``SYNTH_ARG_`` prefix to avoid possible collisions
                with existing variables.

                Example of valid sequence written to the uevent file::

                    add fe4d7c9d-b8c6-4a70-9ef1-3d8a58d18eed A=1 B=abc

                This generates synthetic uevent including these variables::

                    ACTION=add
                    SYNTH_ARG_A=1
                    SYNTH_ARG_B=abc
                    SYNTH_UUID=fe4d7c9d-b8c6-4a70-9ef1-3d8a58d18eed

Users:
                udev, userspace tools generating synthetic uevents
