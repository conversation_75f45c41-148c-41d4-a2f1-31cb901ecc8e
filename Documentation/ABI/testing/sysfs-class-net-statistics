What:		/sys/class/net/<iface>/statistics/collisions
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of collisions seen by this network device.
		This value might not be relevant with all MAC layers.

What:		/sys/class/net/<iface>/statistics/multicast
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of multicast packets received by this
		network device.

What:		/sys/class/net/<iface>/statistics/rx_bytes
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of bytes received by this network device.
		See the network driver for the exact meaning of when this
		value is incremented.

What:		/sys/class/net/<iface>/statistics/rx_compressed
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of compressed packets received by this
		network device. This value might only be relevant for interfaces
		that support packet compression (e.g: PPP).

What:		/sys/class/net/<iface>/statistics/rx_crc_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets received with a CRC (FCS) error
		by this network device. Note that the specific meaning might
		depend on the MAC layer used by the interface.

What:		/sys/class/net/<iface>/statistics/rx_dropped
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets received by the network device
		but dropped, that are not forwarded to the upper layers for
		packet processing. See the network driver for the exact
		meaning of this value.

What:		/sys/class/net/<iface>/statistics/rx_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of receive errors on this network device.
		See the network driver for the exact meaning of this value.

What:		/sys/class/net/<iface>/statistics/rx_fifo_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of receive FIFO errors seen by this
		network device. See the network driver for the exact
		meaning of this value.

What:		/sys/class/net/<iface>/statistics/rx_frame_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of received frames with error, such as
		alignment errors. Note that the specific meaning depends on
		on the MAC layer protocol used. See the network driver for
		the exact meaning of this value.

What:		/sys/class/net/<iface>/statistics/rx_length_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of received error packet with a length
		error, oversized or undersized. See the network driver for the
		exact meaning of this value.

What:		/sys/class/net/<iface>/statistics/rx_missed_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of received packets that have been missed
		due to lack of capacity in the receive side. See the network
		driver for the exact meaning of this value.

What:		/sys/class/net/<iface>/statistics/rx_nohandler
Date:		February 2016
KernelVersion:	4.6
Contact:	<EMAIL>
Description:
		Indicates the number of received packets that were dropped on
		an inactive device by the network core.

What:		/sys/class/net/<iface>/statistics/rx_over_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of received packets that are oversized
		compared to what the network device is configured to accept
		(e.g: larger than MTU). See the network driver for the exact
		meaning of this value.

What:		/sys/class/net/<iface>/statistics/rx_packets
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the total number of good packets received by this
		network device.

What:		/sys/class/net/<iface>/statistics/tx_aborted_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets that have been aborted
		during transmission by a network device (e.g: because of
		a medium collision). See the network driver for the exact
		meaning of this value.

What:		/sys/class/net/<iface>/statistics/tx_bytes
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of bytes transmitted by a network
		device. See the network driver for the exact meaning of this
		value, in particular whether this accounts for all successfully
		transmitted packets or all packets that have been queued for
		transmission.

What:		/sys/class/net/<iface>/statistics/tx_carrier_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets that could not be transmitted
		because of carrier errors (e.g: physical link down). See the
		network driver for the exact meaning of this value.

What:		/sys/class/net/<iface>/statistics/tx_compressed
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of transmitted compressed packets. Note
		this might only be relevant for devices that support
		compression (e.g: PPP).

What:		/sys/class/net/<iface>/statistics/tx_dropped
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets dropped during transmission.
		See the driver for the exact reasons as to why the packets were
		dropped.

What:		/sys/class/net/<iface>/statistics/tx_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets in error during transmission by
		a network device. See the driver for the exact reasons as to
		why the packets were dropped.

What:		/sys/class/net/<iface>/statistics/tx_fifo_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets having caused a transmit
		FIFO error. See the driver for the exact reasons as to why the
		packets were dropped.

What:		/sys/class/net/<iface>/statistics/tx_heartbeat_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets transmitted that have been
		reported as heartbeat errors. See the driver for the exact
		reasons as to why the packets were dropped.

What:		/sys/class/net/<iface>/statistics/tx_packets
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets transmitted by a network
		device. See the driver for whether this reports the number of all
		attempted or successful transmissions.

What:		/sys/class/net/<iface>/statistics/tx_window_errors
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the number of packets not successfully transmitted
		due to a window collision. The specific meaning depends on the
		MAC layer used.  On Ethernet this is usually used to report
		late collisions errors.
