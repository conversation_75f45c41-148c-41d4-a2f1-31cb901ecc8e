What:		/sys/bus/coresight/devices/ultra_smb<N>/enable_sink
Date:		January 2023
KernelVersion:	6.3
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) Add/remove a SMB device from a trace path. There can be
		multiple sources for a single SMB device.

What:		/sys/bus/coresight/devices/ultra_smb<N>/mgmt/buf_size
Date:		January 2023
KernelVersion:	6.3
Contact:	<PERSON><PERSON> He <<EMAIL>>
Description:	(RO) Shows the buffer size of each UltraSoc SMB device.

What:		/sys/bus/coresight/devices/ultra_smb<N>/mgmt/buf_status
Date:		January 2023
KernelVersion:	6.3
Contact:	<PERSON><PERSON> He <<EMAIL>>
Description:	(RO) Shows the value of UltraSoc SMB status register.
		BIT(0) is zero means buffer is empty.

What:		/sys/bus/coresight/devices/ultra_smb<N>/mgmt/read_pos
Date:		January 2023
KernelVersion:	6.3
Contact:	<PERSON><PERSON> He <<EMAIL>>
Description:	(RO) Shows the value of UltraSoc SMB Read Pointer register.

What:		/sys/bus/coresight/devices/ultra_smb<N>/mgmt/write_pos
Date:		January 2023
KernelVersion:	6.3
Contact:	Junhao He <<EMAIL>>
Description:	(RO) Shows the value of UltraSoc SMB Write Pointer register.
