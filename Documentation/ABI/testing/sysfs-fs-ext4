What:		/sys/fs/ext4/<disk>/mb_stats
Date:		March 2008
Contact:	"<PERSON> Ts'o" <<EMAIL>>
Description:
		 Controls whether the multiblock allocator should
		 collect statistics, which are shown during the unmount.
		 1 means to collect statistics, 0 means not to collect
		 statistics

What:		/sys/fs/ext4/<disk>/mb_group_prealloc
Date:		March 2008
Contact:	"<PERSON> Ts'o" <<EMAIL>>
Description:
		The multiblock allocator will round up allocation
		requests to a multiple of this tuning parameter if the
		stripe size is not set in the ext4 superblock

What:		/sys/fs/ext4/<disk>/mb_max_to_scan
Date:		March 2008
Contact:	"<PERSON> Ts'o" <<EMAIL>>
Description:
		The maximum number of extents the multiblock allocator
		will search to find the best extent

What:		/sys/fs/ext4/<disk>/mb_min_to_scan
Date:		March 2008
Contact:	"<PERSON> Ts'o" <<EMAIL>>
Description:
		The minimum number of extents the multiblock allocator
		will search to find the best extent

What:		/sys/fs/ext4/<disk>/mb_order2_req
Date:		March 2008
Contact:	"<PERSON>'o" <<EMAIL>>
Description:
		Tuning parameter which controls the minimum size for
		requests (as a power of 2) where the buddy cache is
		used

What:		/sys/fs/ext4/<disk>/mb_stream_req
Date:		March 2008
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		Files which have fewer blocks than this tunable
		parameter will have their blocks allocated out of a
		block group specific preallocation pool, so that small
		files are packed closely together.  Each large file
		will have its blocks allocated out of its own unique
		preallocation pool.

What:		/sys/fs/ext4/<disk>/inode_readahead_blks
Date:		March 2008
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		Tuning parameter which controls the maximum number of
		inode table blocks that ext4's inode table readahead
		algorithm will pre-read into the buffer cache

What:		/sys/fs/ext4/<disk>/delayed_allocation_blocks
Date:		March 2008
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		This file is read-only and shows the number of blocks
		that are dirty in the page cache, but which do not
		have their location in the filesystem allocated yet.

What:		/sys/fs/ext4/<disk>/lifetime_write_kbytes
Date:		March 2008
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		This file is read-only and shows the number of kilobytes
		of data that have been written to this filesystem since it was
		created.

What:		/sys/fs/ext4/<disk>/session_write_kbytes
Date:		March 2008
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		This file is read-only and shows the number of
		kilobytes of data that have been written to this
		filesystem since it was mounted.

What:		/sys/fs/ext4/<disk>/inode_goal
Date:		June 2008
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		Tuning parameter which (if non-zero) controls the goal
		inode used by the inode allocator in preference to
		all other allocation heuristics.  This is intended for
		debugging use only, and should be 0 on production
		systems.

What:		/sys/fs/ext4/<disk>/max_writeback_mb_bump
Date:		September 2009
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		The maximum number of megabytes the writeback code will
		try to write out before move on to another inode.

What:		/sys/fs/ext4/<disk>/extent_max_zeroout_kb
Date:		August 2012
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		The maximum number of kilobytes which will be zeroed
		out in preference to creating a new uninitialized
		extent when manipulating an inode's extent tree.  Note
		that using a larger value will increase the
		variability of time necessary to complete a random
		write operation (since a 4k random write might turn
		into a much larger write due to the zeroout
		operation).

What:		/sys/fs/ext4/<disk>/journal_task
Date:		February 2019
Contact:	"Theodore Ts'o" <<EMAIL>>
Description:
		This file is read-only and shows the pid of journal thread in
		current pid-namespace or 0 if task is unreachable.
