What:           /sys/class/uacce/<dev_name>/api
Date:           Feb 2020
KernelVersion:  5.7
Contact:        <EMAIL>
Description:    Api of the device
                Can be any string and up to userspace to parse.
                Application use the api to match the correct driver

What:           /sys/class/uacce/<dev_name>/flags
Date:           Feb 2020
KernelVersion:  5.7
Contact:        <EMAIL>
Description:    Attributes of the device, see UACCE_DEV_xxx flag defined in uacce.h

What:           /sys/class/uacce/<dev_name>/available_instances
Date:           Feb 2020
KernelVersion:  5.7
Contact:        <EMAIL>
Description:    Available instances left of the device
                Return -ENODEV if uacce_ops get_available_instances is not provided

What:           /sys/class/uacce/<dev_name>/isolate_strategy
Date:           Nov 2022
KernelVersion:  6.1
Contact:        <EMAIL>
Description:    (RW) A sysfs node that configure the error threshold for the hardware
                isolation strategy. This size is a configured integer value, which is the
                number of threshold for hardware errors occurred in one hour. The default is 0.
                0 means never isolate the device. The maximum value is 65535. You can write
                a number of threshold based on your hardware.

What:           /sys/class/uacce/<dev_name>/isolate
Date:           Nov 2022
KernelVersion:  6.1
Contact:        <EMAIL>
Description:    (R) A sysfs node that read the device isolated state. The value 1
                means the device is unavailable. The 0 means the device is
                available.

What:           /sys/class/uacce/<dev_name>/algorithms
Date:           Feb 2020
KernelVersion:  5.7
Contact:        <EMAIL>
Description:    Algorithms supported by this accelerator, separated by new line.
                Can be any string and up to userspace to parse.

What:           /sys/class/uacce/<dev_name>/region_mmio_size
Date:           Feb 2020
KernelVersion:  5.7
Contact:        <EMAIL>
Description:    Size (bytes) of mmio region queue file

What:           /sys/class/uacce/<dev_name>/region_dus_size
Date:           Feb 2020
KernelVersion:  5.7
Contact:        <EMAIL>
Description:    Size (bytes) of dus region queue file
