What:		/sys/bus/pci/drivers/i915/.../hwmon/hwmon<i>/in0_input
Date:		February 2023
KernelVersion:	6.2
Contact:	<EMAIL>
Description:	RO. Current Voltage in millivolt.

		Only supported for particular Intel i915 graphics platforms.

What:		/sys/bus/pci/drivers/i915/.../hwmon/hwmon<i>/power1_max
Date:		February 2023
KernelVersion:	6.2
Contact:	<EMAIL>
Description:	RW. Card reactive sustained  (PL1/Tau) power limit in microwatts.

		The power controller will throttle the operating frequency
		if the power averaged over a window (typically seconds)
		exceeds this limit. A read value of 0 means that the PL1
		power limit is disabled, writing 0 disables the
		limit. Writing values > 0 will enable the power limit.

		Only supported for particular Intel i915 graphics platforms.

What:		/sys/bus/pci/drivers/i915/.../hwmon/hwmon<i>/power1_rated_max
Date:		February 2023
KernelVersion:	6.2
Contact:	<EMAIL>
Description:	RO. Card default power limit (default TDP setting).

		Only supported for particular Intel i915 graphics platforms.

What:		/sys/bus/pci/drivers/i915/.../hwmon/hwmon<i>/power1_max_interval
Date:		February 2023
KernelVersion:	6.2
Contact:	<EMAIL>
Description:	RW. Sustained power limit interval (Tau in PL1/Tau) in
		milliseconds over which sustained power is averaged.

		Only supported for particular Intel i915 graphics platforms.

What:		/sys/bus/pci/drivers/i915/.../hwmon/hwmon<i>/power1_crit
Date:		February 2023
KernelVersion:	6.2
Contact:	<EMAIL>
Description:	RW. Card reactive critical (I1) power limit in microwatts.

		Card reactive critical (I1) power limit in microwatts is exposed
		for client products. The power controller will throttle the
		operating frequency if the power averaged over a window exceeds
		this limit.

		Only supported for particular Intel i915 graphics platforms.

What:		/sys/bus/pci/drivers/i915/.../hwmon/hwmon<i>/curr1_crit
Date:		February 2023
KernelVersion:	6.2
Contact:	<EMAIL>
Description:	RW. Card reactive critical (I1) power limit in milliamperes.

		Card reactive critical (I1) power limit in milliamperes is
		exposed for server products. The power controller will throttle
		the operating frequency if the power averaged over a window
		exceeds this limit.

		Only supported for particular Intel i915 graphics platforms.

What:		/sys/bus/pci/drivers/i915/.../hwmon/hwmon<i>/energy1_input
Date:		February 2023
KernelVersion:	6.2
Contact:	<EMAIL>
Description:	RO. Energy input of device or gt in microjoules.

		For i915 device level hwmon devices (name "i915") this
		reflects energy input for the entire device. For gt level
		hwmon devices (name "i915_gtN") this reflects energy input
		for the gt.

		Only supported for particular Intel i915 graphics platforms.

What:		/sys/bus/pci/drivers/i915/.../hwmon/hwmon<i>/fan1_input
Date:		November 2024
KernelVersion:	6.12
Contact:	<EMAIL>
Description:	RO. Fan speed of device in RPM.

		Only supported for particular Intel i915 graphics platforms.
