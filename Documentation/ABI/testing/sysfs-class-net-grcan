
What:		/sys/class/net/<iface>/grcan/enable0
Date:		October 2012
KernelVersion:	3.8
Contact:	<PERSON> <<EMAIL>>
Description:
		Hardware configuration of physical interface 0. This file reads
		and writes the "Enable 0" bit of the configuration register.
		Possible values: 0 or 1. See the GRCAN chapter of the GRLIB IP
		core library documentation for details. The default value is 0
		or set by the module parameter grcan.enable0 and can be read at
		/sys/module/grcan/parameters/enable0.

What:		/sys/class/net/<iface>/grcan/enable1
Date:		October 2012
KernelVersion:	3.8
Contact:	<PERSON> <<EMAIL>>
Description:
		Hardware configuration of physical interface 1. This file reads
		and writes the "Enable 1" bit of the configuration register.
		Possible values: 0 or 1. See the GRCAN chapter of the GRLIB IP
		core library documentation for details. The default value is 0
		or set by the module parameter grcan.enable1 and can be read at
		/sys/module/grcan/parameters/enable1.

What:		/sys/class/net/<iface>/grcan/select
Date:		October 2012
KernelVersion:	3.8
Contact:	<PERSON> <<EMAIL>>
Description:
		Configuration of which physical interface to be used. Possible
		values: 0 or 1. See the GRCAN chapter of the GRLIB IP core
		library documentation for details. The default value is 0 or is
		set by the module parameter grcan.select and can be read at
		/sys/module/grcan/parameters/select.
