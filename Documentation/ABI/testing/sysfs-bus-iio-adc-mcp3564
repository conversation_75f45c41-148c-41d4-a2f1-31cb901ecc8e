What:		/sys/bus/iio/devices/iio:deviceX/boost_current_gain
KernelVersion:	6.4
Contact:	<EMAIL>
Description:
		This attribute is used to set the gain of the biasing current
		circuit of the Delta-Sigma modulator. The different BOOST
		settings are applied to the entire modulator circuit, including
		the voltage reference buffers.

What:		/sys/bus/iio/devices/iio:deviceX/boost_current_gain_available
KernelVersion:	6.4
Contact:	<EMAIL>
Description:
		Reading returns a list with the possible gain values for
		the current biasing circuit of the Delta-Sigma modulator.

What:		/sys/bus/iio/devices/iio:deviceX/auto_zeroing_mux_enable
KernelVersion:	6.4
Contact:	<EMAIL>
Description:
		This attribute is used to enable the analog input multiplexer
		auto-zeroing algorithm (the input multiplexer and the ADC
		include an offset cancellation algorithm that cancels the offset
		contribution of the ADC). When the offset cancellation algorithm
		is enabled, ADC takes two conversions, one with the differential
		input as VIN+/VIN-, one with VIN+/VIN- inverted. In this case the
		conversion time is multiplied by two compared to the default
		case where the algorithm is disabled. This technique allows the
		cancellation of the ADC offset error and the achievement of
		ultra-low offset without any digital calibration. The resulting
		offset is the residue of the difference between the two
		conversions, which is on the order of magnitude of the noise
		floor. This offset is effectively canceled at every conversion,
		so the residual offset error temperature drift is extremely low.
		Write '1' to enable it, write '0' to disable it.

What:		/sys/bus/iio/devices/iio:deviceX/auto_zeroing_ref_enable
KernelVersion:	6.4
Contact:	<EMAIL>
Description:
		This attribute is used to enable the chopping algorithm for the
		internal voltage reference buffer. This setting has no effect
		when external voltage reference is selected.
		Internal voltage reference buffer injects a certain quantity of
		1/f noise into the system that can be modulated with the
		incoming input signals and can limit the SNR performance at
		higher Oversampling Ratio values (over 256). To overcome this
		limitation, the buffer includes an auto-zeroing algorithm that
		greatly reduces (cancels out) the 1/f noise and cancels the
		offset value of the reference buffer. As a result, the SNR of
		the system is not affected by this 1/f noise component of the
		reference buffer, even at maximum oversampling ratio values.
		Write '1' to enable it, write '0' to disable it.
