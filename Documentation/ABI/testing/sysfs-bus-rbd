What:		/sys/bus/rbd/add
Date:		Oct, 2010
KernelVersion:	v2.6.37
Contact:	<PERSON> <<EMAIL>>
Description:
		(WO) Add rbd block device.

		Usage: <mon ip addr> <options> <pool name> <rbd image name> [<snap name>]

		Example::

		 $ echo "*********** name=admin rbd foo" > /sys/bus/rbd/add

		The snapshot name can be "-" or omitted to map the image
		read/write. A <dev-id> will be assigned for any registered block
		device. If snapshot is used, it will be mapped read-only.


What:		/sys/bus/rbd/remove
Date:		Oct, 2010
KernelVersion:	v2.6.37
Contact:	<PERSON>l <<EMAIL>>
Description:
		(WO) Remove rbd block device.

		Usage: <dev-id> [force]

		Example::

		 $ echo 2 > /sys/bus/rbd/remove

		Optional "force" argument which when passed will wait for
		running requests and then unmap the image. Requests sent to the
		driver after initiating the removal will be failed. (August
		2016, since 4.9.)


What:		/sys/bus/rbd/add_single_major
Date:		Dec, 2013
KernelVersion:	v3.14
Contact:	<PERSON> <<EMAIL>>
Description:
		(WO) Available only if rbd module is inserted with single_major
		parameter set to true.

		Usage is the same as for /sys/bus/rbd/add. If present, this
		should be used instead of the latter: any attempts to use
		/sys/bus/rbd/add if /sys/bus/rbd/add_single_major is available
		will fail for backwards compatibility reasons.


What:		/sys/bus/rbd/remove_single_major
Date:		Dec, 2013
KernelVersion:	v3.14
Contact:	Sage Weil <<EMAIL>>
Description:
		(WO) Available only if rbd module is inserted with single_major
		parameter set to true.

		Usage is the same as for /sys/bus/rbd/remove. If present, this
		should be used instead of the latter: any attempts to use
		/sys/bus/rbd/remove if /sys/bus/rbd/remove_single_major is
		available will fail for backwards compatibility reasons.


What:		/sys/bus/rbd/supported_features
Date:		Mar, 2017
KernelVersion:	v4.11
Contact:	Sage Weil <<EMAIL>>
Description:
		(RO) Displays the features supported by the rbd module so that
		userspace can generate meaningful error messages and spell out
		unsupported features that need to be disabled.


What:		/sys/bus/rbd/devices/<dev-id>/size
What:		/sys/bus/rbd/devices/<dev-id>/major
What:		/sys/bus/rbd/devices/<dev-id>/client_id
What:		/sys/bus/rbd/devices/<dev-id>/pool
What:		/sys/bus/rbd/devices/<dev-id>/name
What:		/sys/bus/rbd/devices/<dev-id>/refresh
What:		/sys/bus/rbd/devices/<dev-id>/current_snap
Date:		Oct, 2010
KernelVersion:	v2.6.37
Contact:	Sage Weil <<EMAIL>>
Description:

		==============	================================================
		size		(RO) The size (in bytes) of the mapped block
				device.

		major		(RO) The block device major number.

		client_id	(RO) The ceph unique client id that was assigned
				for this specific session.

		pool		(RO) The name of the storage pool where this rbd
				image resides. An rbd image name is unique
				within its pool.

		name		(RO) The name of the rbd image.

		refresh		(WO) Writing to this file will reread the image
				header data and set all relevant data structures
				accordingly.

		current_snap	(RO) The current snapshot for which the device
				is mapped.
		==============	================================================


What:		/sys/bus/rbd/devices/<dev-id>/pool_id
Date:		Jul, 2012
KernelVersion:	v3.6
Contact:	Sage Weil <<EMAIL>>
Description:
		(RO) The unique identifier for the rbd image's pool. This is a
		permanent attribute of the pool. A pool's id will never change.


What:		/sys/bus/rbd/devices/<dev-id>/image_id
What:		/sys/bus/rbd/devices/<dev-id>/features
Date:		Oct, 2012
KernelVersion:	v3.7
Contact:	Sage Weil <<EMAIL>>
Description:
		=========	===============================================
		image_id	(RO) The unique id for the rbd image. (For rbd
				image format 1 this is empty.)

		features	(RO) A hexadecimal encoding of the feature bits
				for this image.
		=========	===============================================


What:		/sys/bus/rbd/devices/<dev-id>/parent
Date:		Nov, 2012
KernelVersion:	v3.8
Contact:	Sage Weil <<EMAIL>>
Description:
		(RO) Information identifying the chain of parent images in a
		layered rbd image. Entries are separated by empty lines.


What:		/sys/bus/rbd/devices/<dev-id>/minor
Date:		Dec, 2013
KernelVersion:	v3.14
Contact:	Sage Weil <<EMAIL>>
Description:
		(RO) The block device minor number.


What:		/sys/bus/rbd/devices/<dev-id>/snap_id
What:		/sys/bus/rbd/devices/<dev-id>/config_info
What:		/sys/bus/rbd/devices/<dev-id>/cluster_fsid
What:		/sys/bus/rbd/devices/<dev-id>/client_addr
Date:		Aug, 2016
KernelVersion:	v4.9
Contact:	Sage Weil <<EMAIL>>
Description:
		============	================================================
		snap_id		(RO) The current snapshot's id.

		config_info	(RO) The string written into
				/sys/bus/rbd/add{,_single_major}.

		cluster_fsid	(RO) The ceph cluster UUID.

		client_addr	(RO) The ceph unique client
				entity_addr_t (address + nonce). The format is
				<address>:<port>/<nonce>: '*******:1234/5678' or
				'[1:2:3:4:5:6:7:8]:1234/5678'.
		============	================================================
