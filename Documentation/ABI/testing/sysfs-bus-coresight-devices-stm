What:		/sys/bus/coresight/devices/<memory_map>.stm/enable_source
Date:		April 2016
KernelVersion:	4.7
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) Enable/disable tracing on this specific trace macrocell.
		Enabling the trace macrocell implies it has been configured
		properly and a sink has been identified for it.  The path
		of coresight components linking the source to the sink is
		configured and managed automatically by the coresight framework.

What:		/sys/bus/coresight/devices/<memory_map>.stm/hwevent_enable
Date:		April 2016
KernelVersion:	4.7
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) Provides access to the HW event enable register, used in
		conjunction with HW event bank select register.

What:		/sys/bus/coresight/devices/<memory_map>.stm/hwevent_select
Date:		April 2016
KernelVersion:	4.7
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) Gives access to the HW event block select register
		(STMHEBSR) in order to configure up to 256 channels.  Used in
		conjunction with "hwevent_enable" register as described above.

What:		/sys/bus/coresight/devices/<memory_map>.stm/port_enable
Date:		April 2016
KernelVersion:	4.7
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) Provides access to the stimulus port enable register
		(STMSPER).  Used in conjunction with "port_select" described
		below.

What:		/sys/bus/coresight/devices/<memory_map>.stm/port_select
Date:		April 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Used to determine which bank of stimulus port bit in
		register STMSPER (see above) apply to.

What:		/sys/bus/coresight/devices/<memory_map>.stm/status
Date:		April 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) List various control and status registers.  The specific
		layout and content is driver specific.

What:		/sys/bus/coresight/devices/<memory_map>.stm/traceid
Date:		April 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Holds the trace ID that will appear in the trace stream
		coming from this trace entity.
