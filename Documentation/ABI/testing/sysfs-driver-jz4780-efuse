What:		/sys/devices/*/<our-device>/nvmem
Date:		December 2017
Contact:	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <pras<PERSON><PERSON><PERSON><PERSON>@gmail.com>
Description:	read-only access to the efuse on the Ingenic JZ4780 SoC
		The SoC has a one time programmable 8K efuse that is
		split into segments. The driver supports read only.
		The segments are:

		===== ======== =================
		0x000   64 bit Random Number
		0x008  128 bit Ingenic Chip ID
		0x018  128 bit Customer ID
		0x028 3520 bit Reserved
		0x1E0    8 bit Protect Segment
		0x1E1 2296 bit HDMI Key
		0x300 2048 bit Security boot key
		===== ======== =================

Users:		any user space application which wants to read the Chip
		and Customer ID
