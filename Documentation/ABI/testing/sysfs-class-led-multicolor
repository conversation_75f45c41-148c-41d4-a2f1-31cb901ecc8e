
What:		/sys/class/leds/<led>/multi_index
Date:		March 2020
KernelVersion:	5.9
Contact:	<PERSON> <<EMAIL>>
Description:	read
		The multi_index array, when read, will output the LED colors
		as an array of strings as they are indexed in the
		multi_intensity file.

		For additional details please refer to
		Documentation/leds/leds-class-multicolor.rst.

What:		/sys/class/leds/<led>/multi_intensity
Date:		March 2020
KernelVersion:	5.9
Contact:	<PERSON> <<EMAIL>>
Description:	read/write
		This file contains array of integers. Order of components is
		described by the multi_index array. The maximum intensity should
		not exceed /sys/class/leds/<led>/max_brightness.

		For additional details please refer to
		Documentation/leds/leds-class-multicolor.rst.
