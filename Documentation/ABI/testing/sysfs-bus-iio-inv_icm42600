What:		/sys/bus/iio/devices/iio:deviceX/in_accel_power_mode
KernelVersion:	6.11
Contact:	<EMAIL>
Description:
		Accelerometer power mode. Setting this attribute will set the
		requested power mode to use if the ODR support it. If ODR
		support only 1 mode, power mode will be enforced.
		Reading this attribute will return the current accelerometer
		power mode if the sensor is on, or the requested value if the
		sensor is off. The value between real and requested value can
		be different for ODR supporting only 1 mode.

What:		/sys/bus/iio/devices/iio:deviceX/in_accel_power_mode_available
KernelVersion:	6.11
Contact:	<EMAIL>
Description:
		List of available accelerometer power modes that can be set in
		in_accel_power_mode attribute.
