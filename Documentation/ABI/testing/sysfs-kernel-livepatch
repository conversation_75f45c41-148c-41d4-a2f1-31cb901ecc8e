What:		/sys/kernel/livepatch
Date:		Nov 2014
KernelVersion:	3.19.0
Contact:	<EMAIL>
Description:
		Interface for kernel live patching

		The /sys/kernel/livepatch directory contains subdirectories for
		each loaded live patch module.

What:		/sys/kernel/livepatch/<patch>
Date:		Nov 2014
KernelVersion:	3.19.0
Contact:	<EMAIL>
Description:
		The patch directory contains subdirectories for each kernel
		object (vmlinux or a module) in which it patched functions.

What:		/sys/kernel/livepatch/<patch>/enabled
Date:		Nov 2014
KernelVersion:	3.19.0
Contact:	<EMAIL>
Description:
		A writable attribute that indicates whether the patched
		code is currently applied.  Writing 0 will disable the patch
		while writing 1 will re-enable the patch.

What:		/sys/kernel/livepatch/<patch>/transition
Date:		Feb 2017
KernelVersion:	4.12.0
Contact:	<EMAIL>
Description:
		An attribute which indicates whether the patch is currently in
		transition.

What:		/sys/kernel/livepatch/<patch>/force
Date:		Nov 2017
KernelVersion:	4.15.0
Contact:	<EMAIL>
Description:
		A writable attribute that allows administrator to affect the
		course of an existing transition. Writing 1 clears
		TIF_PATCH_PENDING flag of all tasks and thus forces the tasks to
		the patched or unpatched state. Administrator should not
		use this feature without a clearance from a patch
		distributor. Removal (rmmod) of patch modules is permanently
		disabled when the feature is used. See
		Documentation/livepatch/livepatch.rst for more information.

What:		/sys/kernel/livepatch/<patch>/replace
Date:		Jun 2024
KernelVersion:	6.11.0
Contact:	<EMAIL>
Description:
		An attribute which indicates whether the patch supports
		atomic-replace.

What:		/sys/kernel/livepatch/<patch>/<object>
Date:		Nov 2014
KernelVersion:	3.19.0
Contact:	<EMAIL>
Description:
		The object directory contains subdirectories for each function
		that is patched within the object.

What:		/sys/kernel/livepatch/<patch>/<object>/patched
Date:		August 2022
KernelVersion:	6.1.0
Contact:	<EMAIL>
Description:
		An attribute which indicates whether the object is currently
		patched.

What:		/sys/kernel/livepatch/<patch>/<object>/<function,sympos>
Date:		Nov 2014
KernelVersion:	3.19.0
Contact:	<EMAIL>
Description:
		The function directory contains attributes regarding the
		properties and state of the patched function.

		The directory name contains the patched function name and a
		sympos number corresponding to the nth occurrence of the symbol
		name in kallsyms for the patched object.

		There are currently no such attributes.
