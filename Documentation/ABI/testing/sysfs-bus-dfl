What:		/sys/bus/dfl/devices/dfl_dev.X/type
Date:		Aug 2020
KernelVersion:	5.10
Contact:	<PERSON> <<EMAIL>>
Description:	Read-only. It returns type of DFL FIU of the device. Now DFL
		supports 2 FIU types, 0 for FME, 1 for PORT.

		Format: 0x%x

What:		/sys/bus/dfl/devices/dfl_dev.X/feature_id
Date:		Aug 2020
KernelVersion:	5.10
Contact:	<PERSON> <<EMAIL>>
Description:	Read-only. It returns feature identifier local to its DFL FIU
		type.

		Format: 0x%x
