What:		/sys/bus/iio/devices/iio:deviceX/in_intensityY_raw
Date:		May 2016
KernelVersion:
Contact:	<PERSON> <<EMAIL>>
Description:
		Get measured values from the ADC for these stages. Y is the
		specific stage number corresponding to datasheet stage names
		as follows:

		== ==========
		1  LED2
		2  ALED2/LED3
		3  LED1
		4  ALED1/LED4
		== ==========

		Note that channels 5 and 6 represent LED2-ALED2 and LED1-ALED1
		respectively which simply helper channels containing the
		calculated difference in the value of stage 1 - 2 and 3 - 4.
		The values are expressed in 24-bit twos complement.
		The LED current for the stage is controlled via
		out_currentY_raw.

What:		/sys/bus/iio/devices/iio:deviceX/in_intensityY_offset
Date:		May 2016
KernelVersion:
Contact:	<PERSON> <<EMAIL>>
Description:
		Get and set the offset cancellation DAC setting for these
		stages. The values are expressed in 5-bit sign-magnitude.

What:		/sys/bus/iio/devices/iio:deviceX/in_intensityY_resistance
What:		/sys/bus/iio/devices/iio:deviceX/in_intensityY_capacitance
Date:		May 2016
KernelVersion:
Contact:	<PERSON> <<EMAIL>>
Description:
		Get and set the resistance and the capacitance settings for the
		Transimpedance Amplifier during the associated stage.
