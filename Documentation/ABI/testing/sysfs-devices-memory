What:		/sys/devices/system/memory
Date:		June 2008
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The /sys/devices/system/memory contains a snapshot of the
		internal state of the kernel memory blocks. Files could be
		added or removed dynamically to represent hot-add/remove
		operations.
Users:		hotplug memory add/remove tools
		http://www.ibm.com/developerworks/wikis/display/LinuxP/powerpc-utils

What:		/sys/devices/system/memory/memoryX/removable
Date:		June 2008
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/removable is a
		legacy interface used to indicated whether a memory block is
		likely to be offlineable or not.  Newer kernel versions return
		"1" if and only if the kernel supports memory offlining.
Users:		hotplug memory remove tools
		http://www.ibm.com/developerworks/wikis/display/LinuxP/powerpc-utils
		lsmem/chmem part of util-linux

What:		/sys/devices/system/memory/memoryX/phys_device
Date:		September 2008
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/phys_device
		is read-only;  it is a legacy interface only ever used on s390x
		to expose the covered storage increment.
Users:		Legacy s390-tools lsmem/chmem

What:		/sys/devices/system/memory/memoryX/phys_index
Date:		September 2008
Contact:	Badari Pulavarty <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/phys_index
		is read-only and contains the section ID in hexadecimal
		which is equivalent to decimal X contained in the
		memory section directory name.

What:		/sys/devices/system/memory/memoryX/state
Date:		September 2008
Contact:	Badari Pulavarty <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/state
		is read-write.  When read, it returns the online/offline
		state of the memory block.  When written, root can toggle
		the online/offline state of a memory block using the following
		commands::

		  # echo online > /sys/devices/system/memory/memoryX/state
		  # echo offline > /sys/devices/system/memory/memoryX/state

		On newer kernel versions, advanced states can be specified
		when onlining to select a target zone: "online_movable"
		selects the movable zone.  "online_kernel" selects the
		applicable kernel zone (DMA, DMA32, or Normal).  However,
		after successfully setting one of the advanced states,
		reading the file will return "online"; the zone information
		can be obtained via "valid_zones" instead.

		While onlining is unlikely to fail, there are no guarantees
		that offlining will succeed.  Offlining is more likely to
		succeed if "valid_zones" indicates "Movable".
Users:		hotplug memory remove tools
		http://www.ibm.com/developerworks/wikis/display/LinuxP/powerpc-utils


What:           /sys/devices/system/memory/memoryX/valid_zones
Date:           July 2014
Contact:	Zhang Zhen <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/valid_zones	is
		read-only.

		For online memory blocks, it returns in which zone memory
		provided by a memory block is managed.  If multiple zones
		apply (not applicable for hotplugged memory), "None" is returned
		and the memory block cannot be offlined.

		For offline memory blocks, it returns by which zone memory
		provided by a memory block can be managed when onlining.
		The first returned zone ("default") will be used when setting
		the state of an offline memory block to "online".  Only one of
		the kernel zones (DMA, DMA32, Normal) is applicable for a single
		memory block.

What:		/sys/devices/system/memoryX/nodeY
Date:		October 2009
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		When CONFIG_NUMA is enabled, a symbolic link that
		points to the corresponding NUMA node directory.

		For example, the following symbolic link is created for
		memory section 9 on node0:

		/sys/devices/system/memory/memory9/node0 -> ../../node/node0


What:		/sys/devices/system/node/nodeX/memoryY
Date:		September 2008
Contact:	Gary Hade <<EMAIL>>
Description:
		When CONFIG_NUMA is enabled
		/sys/devices/system/node/nodeX/memoryY is a symbolic link that
		points to the corresponding /sys/devices/system/memory/memoryY
		memory section directory.  For example, the following symbolic
		link is created for memory section 9 on node0.

		/sys/devices/system/node/node0/memory9 -> ../../memory/memory9

What:		/sys/devices/system/memory/crash_hotplug
Date:		Aug 2023
Contact:	Linux kernel mailing list <<EMAIL>>
Description:
		(RO) indicates whether or not the kernel updates relevant kexec
		segments on memory hot un/plug and/or on/offline events, avoiding the
		need to reload kdump kernel.
