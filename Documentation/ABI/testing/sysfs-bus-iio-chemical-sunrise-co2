What:		/sys/bus/iio/devices/iio:deviceX/in_concentration_co2_calibration_factory
Date:		August 2021
KernelVersion:	5.16
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		Writing '1' triggers a 'Factory' calibration cycle.

What:		/sys/bus/iio/devices/iio:deviceX/in_concentration_co2_calibration_background
Date:		August 2021
KernelVersion:	5.16
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		Writing '1' triggers a 'Background' calibration cycle.

What:		/sys/bus/iio/devices/iio:deviceX/error_status_available
Date:		August 2021
KernelVersion:	5.16
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		Reading returns the list of possible chip error status.
		Available options are:
		- 'error_fatal': Analog front-end initialization error
		- 'error_i2c': Read/write to non-existing register
		- 'error_algorithm': Corrupted parameters
		- 'error_calibration': Calibration has failed
		- 'error_self_diagnostic': Internal interface failure
		- 'error_out_of_range': Measured concentration out of scale
		- 'error_memory': Error during memory operations
		- 'error_no_measurement': Cleared at first measurement
		- 'error_low_voltage': Sensor regulated voltage too low
		- 'error_measurement_timeout': Unable to complete measurement

What:		/sys/bus/iio/devices/iio:deviceX/error_status
Date:		August 2021
KernelVersion:	5.16
Contact:	Jacopo Mondi <<EMAIL>>
Description:
		Reading returns the current chip error status.
