What:		/sys/kernel/iommu_groups/
Date:		May 2012
KernelVersion:	v3.5
Contact:	<PERSON> <<EMAIL>>
Description:	/sys/kernel/iommu_groups/ contains a number of sub-
		directories, each representing an IOMMU group.  The
		name of the sub-directory matches the iommu_group_id()
		for the group, which is an integer value.  Within each
		subdirectory is another directory named "devices" with
		links to the sysfs devices contained in this group.
		The group directory also optionally contains a "name"
		file if the IOMMU driver has chosen to register a more
		common name for the group.
Users:

What:		/sys/kernel/iommu_groups/reserved_regions
Date: 		January 2017
KernelVersion:  v4.11
Contact: 	<PERSON> <<EMAIL>>
Description:    /sys/kernel/iommu_groups/reserved_regions list IOVA
		regions that are reserved. Not necessarily all
		reserved regions are listed. This is typically used to
		output direct-mapped, MSI, non mappable regions. Each
		region is described on a single line: the 1st field is
		the base IOVA, the second is the end IOVA and the third
		field describes the type of the region.

		Since kernel 5.3, in case an RMRR is used only by graphics or
		USB devices it is now exposed as "direct-relaxable" instead
		of "direct". In device assignment use case, for instance,
		those RMRR are considered to be relaxable and safe.

What:		/sys/kernel/iommu_groups/<grp_id>/type
Date:		November 2020
KernelVersion:	v5.11
Contact:	Sai Praneeth Prakhya <<EMAIL>>
Description:	/sys/kernel/iommu_groups/<grp_id>/type shows the type of default
		domain in use by iommu for this group. See include/linux/iommu.h
		for possible read values. A privileged user could request kernel to
		change the group type by writing to this file. Valid write values:

		========  ======================================================
		DMA       All the DMA transactions from the device in this group
		          are translated by the iommu.
		DMA-FQ    As above, but using batched invalidation to lazily
		          remove translations after use. This may offer reduced
			  overhead at the cost of reduced memory protection.
		identity  All the DMA transactions from the device in this group
		          are not translated by the iommu. Maximum performance
			  but zero protection.
		auto      Change to the type the device was booted with.
		========  ======================================================

		The default domain type of a group may be modified only when

		- The device in the group is not bound to any device driver.
		  So, the users must unbind the appropriate driver before
		  changing the default domain type.

		Unbinding a device driver will take away the driver's control
		over the device and if done on devices that host root file
		system could lead to catastrophic effects (the users might
		need to reboot the machine to get it to normal state). So, it's
		expected that the users understand what they're doing.
