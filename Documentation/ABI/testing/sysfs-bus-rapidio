What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>
Description:
		For each RapidIO device, the RapidIO subsystem creates files in
		an individual subdirectory with the following name format of
		device_name "nn:d:iiii", where:

		====   ========================================================
		nn     two-digit hexadecimal ID of RapidIO network where the
		       device resides
		d      device type: 'e' - for endpoint or 's' - for switch
		iiii   four-digit device destID for endpoints, or switchID for
		       switches
		====   ========================================================

		For example, below is a list of device directories that
		represents a typical RapidIO network with one switch, one host,
		and two agent endpoints, as it is seen by the enumerating host
		(with destID = 1)::

		  /sys/bus/rapidio/devices/00:e:0000
		  /sys/bus/rapidio/devices/00:e:0002
		  /sys/bus/rapidio/devices/00:s:0001

		NOTE:
		  An enumerating or discovering endpoint does not create a
		  sysfs entry for itself, this is why an endpoint with destID=1
		  is not shown in the list.

Attributes Common for All RapidIO Devices
-----------------------------------------

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/did
Date:		Nov, 2005
KernelVersion:	v2.6.15
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns the device identifier

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/vid
Date:		Nov, 2005
KernelVersion:	v2.6.15
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns the device vendor identifier

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/device_rev
Date:		Nov, 2005
KernelVersion:	v2.6.15
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns the device revision level

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/asm_did
Date:		Nov, 2005
KernelVersion:	v2.6.15
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns identifier for the assembly containing the device

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/asm_rev
Date:		Nov, 2005
KernelVersion:	v2.6.15
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns revision level of the assembly containing the
		device

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/asm_vid
Date:		Nov, 2005
KernelVersion:	v2.6.15
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns vendor identifier of the assembly containing the
		device

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/destid
Date:		Mar, 2011
KernelVersion:	v2.6.3
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns device destination ID assigned by the enumeration
		routine

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/lprev
Date:		Mar, 2011
KernelVersion:	v2.6.39
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns name of previous device (switch) on the path to the
		device that that owns this attribute

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/modalias
Date:		Jul, 2013
KernelVersion:	v3.11
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns the device modalias

What:		/sys/bus/rapidio/devices/<nn>:<d>:<iiii>/config
Date:		Nov, 2005
KernelVersion:	v2.6.15
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RW) Binary attribute to read from and write to the device
		configuration registers using the RapidIO maintenance
		transactions. This attribute is similar in behaviour to the
		"config" attribute of PCI devices and provides an access to the
		RapidIO device registers using standard file read and write
		operations.

RapidIO Switch Device Attributes
--------------------------------

RapidIO switches have additional attributes in sysfs. RapidIO subsystem supports
common and device-specific sysfs attributes for switches. Because switches are
integrated into the RapidIO subsystem, it offers a method to create
device-specific sysfs attributes by specifying a callback function that may be
set by the switch initialization routine during enumeration or discovery
process.

What:		/sys/bus/rapidio/devices/<nn>:<s>:<iiii>/routes
Date:		Nov, 2005
KernelVersion:	v2.6.15
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) reports switch routing information in "destID port" format.
		This attribute reports only valid routing table entries, one
		line for each entry.

What:		/sys/bus/rapidio/devices/<nn>:<s>:<iiii>/destid
Date:		Mar, 2011
KernelVersion:	v2.6.3
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) device destination ID of the associated device that defines
		a route to the switch

What:		/sys/bus/rapidio/devices/<nn>:<s>:<iiii>/hopcount
Date:		Mar, 2011
KernelVersion:	v2.6.39
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) number of hops on the path to the switch

What:		/sys/bus/rapidio/devices/<nn>:<s>:<iiii>/lnext
Date:		Mar, 2011
KernelVersion:	v2.6.39
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) returns names of devices linked to the switch except one of
		a device linked to the ingress port (reported as "lprev"). This
		is an array names with number of lines equal to number of ports
		in switch. If a switch port has no attached device, returns
		"null" instead of a device name.

Device-specific Switch Attributes
---------------------------------

IDT_GEN2-

What:		/sys/bus/rapidio/devices/<nn>:<s>:<iiii>/errlog
Date:		Oct, 2010
KernelVersion:	v2.6.37
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(RO) reads contents of device error log until it is empty.

RapidIO Bus Attributes
----------------------

What:		/sys/bus/rapidio/scan
Date:		May, 2013
KernelVersion:	v3.11
Contact:	Matt Porter <<EMAIL>>,
		Alexandre Bounine <<EMAIL>>
Description:
		(WO) Allows to trigger enumeration discovery process from user
		space. To initiate an enumeration or discovery process on
		specific mport device, a user needs to write mport_ID (not
		RapidIO destination ID) into this file. The mport_ID is a
		sequential number (0 ...  RIO_MAX_MPORTS) assigned to the mport
		device. For example, for a machine with a single RapidIO
		controller, mport_ID for that controller always will be 0. To
		initiate RapidIO enumeration/discovery on all available mports a
		user must write '-1' (or RIO_MPORT_ANY) into this attribute
		file.
