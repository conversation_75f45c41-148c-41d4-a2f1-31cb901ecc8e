What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/enable_source
Date:		November 2014
KernelVersion:	3.19
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) Enable/disable tracing on this specific trace entiry.
		Enabling a source implies the source has been configured
		properly and a sink has been identidifed for it.  The path
		of coresight components linking the source to the sink is
		configured and managed automatically by the coresight framework.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/addr_idx
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description:	Select which address comparator or pair (of comparators) to
		work with.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/addr_acctype
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu <PERSON>irier <<EMAIL>>
Description:	(RW) Used in conjunction with @addr_idx.  Specifies
		characteristics about the address comparator being configure,
		for example the access type, the kind of instruction to trace,
		processor context ID to trigger on, etc.  Individual fields in
		the access type register may vary on the version of the trace
		entity.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/addr_range
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Used in conjunction with @addr_idx.  Specifies the range of
		addresses to trigger on.  Inclusion or exclusion is specified
		in the corresponding access type register.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/addr_single
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Used in conjunction with @addr_idx.  Specifies the single
		address to trigger on, highly influenced by the configuration
		options of the corresponding access type register.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/addr_start
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Used in conjunction with @addr_idx.  Specifies the single
		address to start tracing on, highly influenced by the
		configuration options of the corresponding access type register.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/addr_stop
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Used in conjunction with @addr_idx.  Specifies the single
		address to stop tracing on, highly influenced by the
		configuration options of the corresponding access type register.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/cntr_idx
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RW) Specifies the counter to work on.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/cntr_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Used in conjunction with cntr_idx, give access to the
		counter event register.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/cntr_val
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Used in conjunction with cntr_idx, give access to the
		counter value register.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/cntr_rld_val
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Used in conjunction with cntr_idx, give access to the
		counter reload value register.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/cntr_rld_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Used in conjunction with cntr_idx, give access to the
		counter reload event register.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/ctxid_idx
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Specifies the index of the context ID register to be
		selected.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/ctxid_mask
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Mask to apply to all the context ID comparator.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/ctxid_pid
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Used with the ctxid_idx, specify with context ID to trigger
		on.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/enable_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Defines which event triggers a trace.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/etmsr
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Gives access to the ETM status register, which holds
		programming information and status on certains events.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/fifofull_level
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Number of byte left in the fifo before considering it full.
		Depending on the tracer's version, can also hold threshold for
		data suppression.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mode
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Interface with the driver's 'mode' field, controlling
		various aspect of the trace entity such as time stamping,
		context ID size and cycle accurate tracing.  Driver specific
		and bound to change depending on the driver.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/nr_addr_cmp
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(Read) Provides the number of address comparators pairs accessible
		on a trace unit, as specified by bit 3:0 of register ETMCCR.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/nr_cntr
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(Read) Provides the number of counters accessible on a trace unit,
		as specified by bit 15:13 of register ETMCCR.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/nr_ctxid_cmp
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(Read) Provides the number of context ID comparator available on a
		trace unit, as specified by bit 25:24 of register ETMCCR.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/reset
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(Write) Cancels all configuration on a trace unit and set it back
		to its boot configuration.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/seq_12_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Defines the event that causes the sequencer to transition
		from state 1 to state 2.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/seq_13_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Defines the event that causes the sequencer to transition
		from state 1 to state 3.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/seq_21_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Defines the event that causes the sequencer to transition
		from state 2 to state 1.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/seq_23_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Defines the event that causes the sequencer to transition
		from state 2 to state 3.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/seq_31_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Defines the event that causes the sequencer to transition
		from state 3 to state 1.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/seq_32_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Defines the event that causes the sequencer to transition
		from state 3 to state 2.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/curr_seq_state
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(Read) Holds the current state of the sequencer.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/sync_freq
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Holds the trace synchronization frequency value - must be
		programmed with the various implementation behavior in mind.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/timestamp_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Defines an event that requests the insertion of a timestamp
		into the trace stream.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/traceid
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Holds the trace ID that will appear in the trace stream
		coming from this trace entity.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/trigger_event
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RW) Define the event that controls the trigger.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/cpu
Date:		October 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(RO) Holds the cpu number this tracer is affined to.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmccr
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM Configuration Code register
		(0x004).  The value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmccer
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM Configuration Code Extension
		register (0x1e8).  The value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmscr
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM System Configuration
		register (0x014).  The value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmidr
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM ID register (0x1e4).  The
		value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmcr
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM Main Control register (0x000).
		The value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmtraceidr
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM Trace ID register (0x200).
		The value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmteevr
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM Trace Enable Event register
		(0x020). The value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmtsscr
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM Trace Start/Stop Control
		register (0x018). The value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmtecr1
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM Enable Control #1
		register (0x024). The value is read directly from the HW.

What:		/sys/bus/coresight/devices/<memory_map>.[etm|ptm]/mgmt/etmtecr2
Date:		September 2015
KernelVersion:	4.4
Contact:	Mathieu Poirier <<EMAIL>>
Description: 	(RO) Print the content of the ETM Enable Control #2
		register (0x01c). The value is read directly from the HW.
