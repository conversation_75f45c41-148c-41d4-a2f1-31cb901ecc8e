What:		/sys/bus/platform/devices/GOOG000C\:00/boot_on_ac
Date:		April 2019
KernelVersion:	5.3
Description:
		Boot on AC is a policy which makes the device boot from S5
		when AC power is connected. This is useful for users who
		want to run their device headless or with a dock.

		Input should be parseable by kstrtou8() to 0 or 1.

What:          /sys/bus/platform/devices/GOOG000C\:00/build_date
Date:          May 2019
KernelVersion: 5.3
Description:
               Display Wilco Embedded Controller firmware build date.
               Output will a MM/DD/YY string.

What:          /sys/bus/platform/devices/GOOG000C\:00/build_revision
Date:          May 2019
KernelVersion: 5.3
Description:
               Display Wilco Embedded Controller build revision.
               Output will a version string be similar to the example below:
               d2592cae0

What:          /sys/bus/platform/devices/GOOG000C\:00/model_number
Date:          May 2019
KernelVersion: 5.3
Description:
               Display Wilco Embedded Controller model number.
               Output will a version string be similar to the example below:
               08B6

What:          /sys/bus/platform/devices/GOOG000C\:00/usb_charge
Date:          October 2019
KernelVersion: 5.5
Description:
               Control the USB PowerShare Policy. USB PowerShare is a policy
               which affects charging via the special USB PowerShare port
               (marked with a small lightning bolt or battery icon) when in
               low power states:

               - In S0, the port will always provide power.
               - In S0ix, if usb_charge is enabled, then power will be
                 supplied to the port when on AC or if battery is > 50%.
                 Else no power is supplied.
               - In S5, if usb_charge is enabled, then power will be supplied
                 to the port when on AC. Else no power is supplied.

               Input should be either "0" or "1".

What:          /sys/bus/platform/devices/GOOG000C\:00/version
Date:          May 2019
KernelVersion: 5.3
Description:
               Display Wilco Embedded Controller firmware version.
               The format of the string is x.y.z. Where x is major, y is minor
               and z is the build number. For example: 95.00.06
