What:		/sys/bus/counter/devices/counterX/cascade_counts_enable
KernelVersion:	6.4
Contact:	<EMAIL>
Description:
		Indicates the cascading of Counts on Counter X.

		Valid attribute values are boolean.

What:		/sys/bus/counter/devices/counterX/external_input_phase_clock_select
KernelVersion:	6.4
Contact:	<EMAIL>
Description:
		Selects the external clock pin for phase counting mode of
		Counter X.

		MTCLKA-MTCLKB:
			MTCLKA and MTCLKB pins are selected for the external
			phase clock.

		MTCLKC-MTCLKD:
			MTCLKC and MTCLKD pins are selected for the external
			phase clock.

What:		/sys/bus/counter/devices/counterX/external_input_phase_clock_select_available
KernelVersion:	6.4
Contact:	<EMAIL>
Description:
		Discrete set of available values for the respective device
		configuration are listed in this file.

What:		/sys/bus/counter/devices/counterX/countY/count
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Count data of Count Y represented as a string.

What:		/sys/bus/counter/devices/counterX/countY/capture
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Historical capture of the Count Y count data.

What:		/sys/bus/counter/devices/counterX/countY/ceiling
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Count value ceiling for Count Y. This is the upper limit for the
		respective counter.

What:		/sys/bus/counter/devices/counterX/countY/floor
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Count value floor for Count Y. This is the lower limit for the
		respective counter.

What:		/sys/bus/counter/devices/counterX/countY/count_mode
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Count mode for channel Y. The ceiling and floor values for
		Count Y are used by the count mode where required. The following
		count modes are available:

		normal:
			Counting is continuous in either direction.

		range limit:
			An upper or lower limit is set, mimicking limit switches
			in the mechanical counterpart. The upper limit is set to
			the Count Y ceiling value, while the lower limit is set
			to the Count Y floor value. The counter freezes at
			count = ceiling when counting up, and at count = floor
			when counting down. At either of these limits, the
			counting is resumed only when the count direction is
			reversed.

		non-recycle:
			The counter is disabled whenever a counter overflow or
			underflow takes place. The counter is re-enabled when a
			new count value is loaded to the counter via a preset
			operation or direct write.

		modulo-n:
			A count value boundary is set between the Count Y floor
			value and the Count Y ceiling value. The counter is
			reset to the Count Y floor value at count = ceiling when
			counting up, while the counter is set to the Count Y
			ceiling value at count = floor when counting down; the
			counter does not freeze at the boundary points, but
			counts continuously throughout.

		interrupt on terminal count:
			The output signal is initially low, and will remain low
			until the counter reaches zero. The output signal then
			goes high and remains high until a new preset value is
			set.

		hardware retriggerable one-shot:
			The output signal is initially high. The output signal
			will go low by a trigger input signal, and will remain
			low until the counter reaches zero. The output will then
			go high and remain high until the next trigger. A
			trigger results in loading the counter to the preset
			value and setting the output signal low, thus starting
			the one-shot pulse.

		rate generator:
			The output signal is initially high. When the counter
			has decremented to 1, the output signal goes low for one
			clock pulse. The output signal then goes high again, the
			counter is reloaded to the preset value, and the process
			repeats in a periodic manner as such.

		square wave mode:
			The output signal is initially high.

			If the initial count is even, the counter is decremented
			by two on succeeding clock pulses. When the count
			expires, the output signal changes value and the
			counter is reloaded to the preset value. The process
			repeats in periodic manner as such.

			If the initial count is odd, the initial count minus one
			(an even number) is loaded and then is decremented by
			two on succeeding clock pulses. One clock pulse after
			the count expires, the output signal goes low and the
			counter is reloaded to the preset value minus one.
			Succeeding clock pulses decrement the count by two. When
			the count expires, the output goes high again and the
			counter is reloaded to the preset value minus one. The
			process repeats in a periodic manner as such.

		software triggered strobe:
			The output signal is initially high. When the count
			expires, the output will go low for one clock pulse and
			then go high again. The counting sequence is "triggered"
			by setting the preset value.

		hardware triggered strobe:
			The output signal is initially high. Counting is started
			by a trigger input signal. When the count expires, the
			output signal will go low for one clock pulse and then
			go high again. A trigger results in loading the counter
			to the preset value.

What:		/sys/bus/counter/devices/counterX/countY/count_mode_available
What:		/sys/bus/counter/devices/counterX/countY/error_noise_available
What:		/sys/bus/counter/devices/counterX/countY/function_available
What:		/sys/bus/counter/devices/counterX/countY/prescaler_available
What:		/sys/bus/counter/devices/counterX/countY/signalZ_action_available
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Discrete set of available values for the respective Count Y
		configuration are listed in this file. Values are delimited by
		newline characters.

What:		/sys/bus/counter/devices/counterX/countY/direction
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates the count direction of Count
		Y. Two count directions are available: forward and backward.

		Some counter devices are able to determine the direction of
		their counting. For example, quadrature encoding counters can
		determine the direction of movement by evaluating the leading
		phase of the respective A and B quadrature encoding signals.
		This attribute exposes such count directions.

What:		/sys/bus/counter/devices/counterX/countY/enable
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Whether channel Y counter is enabled. Valid attribute values are
		boolean.

		This attribute is intended to serve as a pause/unpause mechanism
		for Count Y. Suppose a counter device is used to count the total
		movement of a conveyor belt: this attribute allows an operator
		to temporarily pause the counter, service the conveyor belt,
		and then finally unpause the counter to continue where it had
		left off.

What:		/sys/bus/counter/devices/counterX/countY/error_noise
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates whether excessive noise is
		present at the channel Y counter inputs.

What:		/sys/bus/counter/devices/counterX/countY/function
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Count function mode of Count Y; count function evaluation is
		triggered by conditions specified by the Count Y signalZ_action
		attributes. The following count functions are available:

		increase:
			Accumulated count is incremented.

		decrease:
			Accumulated count is decremented.

		pulse-direction:
			Rising edges on signal A updates the respective count.
			The input level of signal B determines direction.

		quadrature x1 a:
			If direction is forward, rising edges on quadrature pair
			signal A updates the respective count; if the direction
			is backward, falling edges on quadrature pair signal A
			updates the respective count. Quadrature encoding
			determines the direction.

		quadrature x1 b:
			If direction is forward, rising edges on quadrature pair
			signal B updates the respective count; if the direction
			is backward, falling edges on quadrature pair signal B
			updates the respective count. Quadrature encoding
			determines the direction.

		quadrature x2 a:
			Any state transition on quadrature pair signal A updates
			the respective count. Quadrature encoding determines the
			direction.

		quadrature x2 b:
			Any state transition on quadrature pair signal B updates
			the respective count. Quadrature encoding determines the
			direction.

		quadrature x4:
			Any state transition on either quadrature pair signals
			updates	the respective count. Quadrature encoding
			determines the direction.

What:		/sys/bus/counter/devices/counterX/countY/name
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates the device-specific name of
		Count Y. If possible, this should match the name of the
		respective channel as it appears in the device datasheet.

What:		/sys/bus/counter/devices/counterX/countY/prescaler
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Configure the prescaler value associated with Count Y.
		On the FlexTimer, the counter clock source passes through a
		prescaler (i.e. a counter). This acts like a clock
		divider.

What:		/sys/bus/counter/devices/counterX/countY/preset
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		If the counter device supports preset registers -- registers
		used to load counter channels to a set count upon device-defined
		preset operation trigger events -- the preset count for channel
		Y is provided by this attribute.

What:		/sys/bus/counter/devices/counterX/countY/preset_enable
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Whether channel Y counter preset operation is enabled. Valid
		attribute values are boolean.

What:		/sys/bus/counter/devices/counterX/countY/signalZ_action
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Action mode of Count Y for Signal Z. This attribute indicates
		the condition of Signal Z that triggers the count function
		evaluation for Count Y. The following action modes are
		available:

		none:
			Signal does not trigger the count function. In
			Pulse-Direction count function mode, this Signal is
			evaluated as Direction.

		rising edge:
			Low state transitions to high state.

		falling edge:
			High state transitions to low state.

		both edges:
			Any state transition.

What:		/sys/bus/counter/devices/counterX/countY/num_overflows
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		This attribute indicates the number of overflows of count Y.

What:		/sys/bus/counter/devices/counterX/cascade_counts_enable_component_id
What:		/sys/bus/counter/devices/counterX/external_input_phase_clock_select_component_id
What:		/sys/bus/counter/devices/counterX/countY/capture_component_id
What:		/sys/bus/counter/devices/counterX/countY/ceiling_component_id
What:		/sys/bus/counter/devices/counterX/countY/floor_component_id
What:		/sys/bus/counter/devices/counterX/countY/count_mode_component_id
What:		/sys/bus/counter/devices/counterX/countY/direction_component_id
What:		/sys/bus/counter/devices/counterX/countY/enable_component_id
What:		/sys/bus/counter/devices/counterX/countY/error_noise_component_id
What:		/sys/bus/counter/devices/counterX/countY/prescaler_component_id
What:		/sys/bus/counter/devices/counterX/countY/preset_component_id
What:		/sys/bus/counter/devices/counterX/countY/preset_enable_component_id
What:		/sys/bus/counter/devices/counterX/countY/signalZ_action_component_id
What:		/sys/bus/counter/devices/counterX/countY/num_overflows_component_id
What:		/sys/bus/counter/devices/counterX/signalY/cable_fault_component_id
What:		/sys/bus/counter/devices/counterX/signalY/cable_fault_enable_component_id
What:		/sys/bus/counter/devices/counterX/signalY/filter_clock_prescaler_component_id
What:		/sys/bus/counter/devices/counterX/signalY/index_polarity_component_id
What:		/sys/bus/counter/devices/counterX/signalY/polarity_component_id
What:		/sys/bus/counter/devices/counterX/signalY/synchronous_mode_component_id
What:		/sys/bus/counter/devices/counterX/signalY/frequency_component_id
KernelVersion:	5.16
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates the component ID of the
		respective extension or Synapse.

What:		/sys/bus/counter/devices/counterX/countY/spike_filter_ns
KernelVersion:	5.14
Contact:	<EMAIL>
Description:
		If the counter device supports programmable spike filter this
		attribute indicates the value in nanoseconds where noise pulses
		shorter or equal to configured value are ignored. Value 0 means
		filter is disabled.

What:		/sys/bus/counter/devices/counterX/events_queue_size
KernelVersion:	5.16
Contact:	<EMAIL>
Description:
		Size of the Counter events queue in number of struct
		counter_event data structures. The number of elements will be
		rounded-up to a power of 2.

What:		/sys/bus/counter/devices/counterX/name
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates the device-specific name of
		the Counter. This should match the name of the device as it
		appears in its respective datasheet.

What:		/sys/bus/counter/devices/counterX/num_counts
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates the total number of Counts
		belonging to the Counter.

What:		/sys/bus/counter/devices/counterX/num_signals
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates the total number of Signals
		belonging to the Counter.

What:		/sys/bus/counter/devices/counterX/signalY/cable_fault
KernelVersion:	5.7
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates whether a differential
		encoder cable fault (not connected or loose wires) is detected
		for the respective channel of Signal Y. Valid attribute values
		are boolean. Detection must first be enabled via the
		corresponding cable_fault_enable attribute.

What:		/sys/bus/counter/devices/counterX/signalY/cable_fault_enable
KernelVersion:	5.7
Contact:	<EMAIL>
Description:
		Whether detection of differential encoder cable faults for the
		respective channel of Signal Y is enabled. Valid attribute
		values are boolean.

What:		/sys/bus/counter/devices/counterX/signalY/filter_clock_prescaler
KernelVersion:	5.7
Contact:	<EMAIL>
Description:
		Filter clock factor for input Signal Y. This prescaler value
		affects the inputs of both quadrature pair signals.

What:		/sys/bus/counter/devices/counterX/signalY/index_polarity
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Active level of index input Signal Y; irrelevant in
		non-synchronous load mode.

What:		/sys/bus/counter/devices/counterX/signalY/index_polarity_available
What:		/sys/bus/counter/devices/counterX/signalY/synchronous_mode_available
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Discrete set of available values for the respective Signal Y
		configuration are listed in this file.

What:		/sys/bus/counter/devices/counterX/signalY/polarity
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Active level of Signal Y. The following polarity values are
		available:

		positive:
			Signal high state considered active level (rising edge).

		negative:
			Signal low state considered active level (falling edge).

What:		/sys/bus/counter/devices/counterX/signalY/name
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates the device-specific name of
		Signal Y. If possible, this should match the name of the
		respective signal as it appears in the device datasheet.

What:		/sys/bus/counter/devices/counterX/signalY/signal
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Signal level state of Signal Y. The following signal level
		states are available:

		low:
			Low level state.

		high:
			High level state.

What:		/sys/bus/counter/devices/counterX/signalY/synchronous_mode
KernelVersion:	5.2
Contact:	<EMAIL>
Description:
		Configure the counter associated with Signal Y for
		non-synchronous or synchronous load mode. Synchronous load mode
		cannot be selected in non-quadrature (Pulse-Direction) clock
		mode.

		non-synchronous:
			A logic low level is the active level at this index
			input. The index function (as enabled via preset_enable)
			is performed directly on the active level of the index
			input.

		synchronous:
			Intended for interfacing with encoder Index output in
			quadrature clock mode. The active level is configured
			via index_polarity. The index function (as enabled via
			preset_enable) is performed synchronously with the
			quadrature clock on the active level of the index input.

What:		/sys/bus/counter/devices/counterX/signalY/frequency
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Read-only attribute that indicates the signal Y frequency, in Hz.
