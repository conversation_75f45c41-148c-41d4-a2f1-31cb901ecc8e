What:		/sys/devices/platform/dock.<N>/docked
Date:		Dec, 2006
KernelVersion:	2.6.19
Contact:	<EMAIL>
Description:
		(RO) Value 1 or 0 indicates whether the software believes the
		laptop is docked in a docking station.

What:		/sys/devices/platform/dock.<N>/undock
Date:		Dec, 2006
KernelVersion:	2.6.19
Contact:	<EMAIL>
Description:
		(WO) Writing to this file causes the software to initiate an
		undock request to the firmware.

What:		/sys/devices/platform/dock.<N>/uid
Date:		Feb, 2007
KernelVersion:	v2.6.21
Contact:	<EMAIL>
Description:
		(RO) Displays the docking station the laptop is docked to.

What:		/sys/devices/platform/dock.<N>/flags
Date:		May, 2007
KernelVersion:	v2.6.21
Contact:	<EMAIL>
Description:
		(RO) Show dock station flags, useful for checking if undock
		request has been made by the user (from the immediate_undock
		option).

What:		/sys/devices/platform/dock.<N>/type
Date:		Aug, 2008
KernelVersion:	v2.6.27
Contact:	<EMAIL>
Description:
		(RO) Display the dock station type- dock_station, ata_bay or
		battery_bay.
