What:		/sys/bus/i2c/devices/xxx/fw_version
Date:		Aug 2020
Contact:	<EMAIL>
Description:    Reports the firmware version provided by the touchscreen, for example "00_T6" on a EXC80H60

		Access: Read

		Valid values: Represented as string

What:		/sys/bus/i2c/devices/xxx/model
Date:		Aug 2020
Contact:	<EMAIL>
Description:    Reports the model identification provided by the touchscreen, for example "Orion_1320" on a EXC80H60

		Access: Read

		Valid values: Represented as string

What:		/sys/bus/i2c/devices/xxx/type
Date:		Jan 2021
Contact:	<EMAIL>
Description:	Reports the type identification provided by the touchscreen, for example "PCAP82H80 Series"

		Access: Read

		Valid values: Represented as string
