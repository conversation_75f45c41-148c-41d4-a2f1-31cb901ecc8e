What:		/sys/bus/tee/devices/optee-ta-<uuid>/
Date:           May 2020
KernelVersion   5.8
Contact:        <EMAIL>
Description:
		OP-TEE bus provides reference to registered drivers under this directory. The <uuid>
		matches Trusted Application (TA) driver and corresponding TA in secure OS. Drivers
		are free to create needed API under optee-ta-<uuid> directory.

What:		/sys/bus/tee/devices/optee-ta-<uuid>/need_supplicant
Date:		November 2023
KernelVersion:	6.7
Contact:	<EMAIL>
Description:
		Allows to distinguish whether an OP-TEE based TA/device requires user-space
		tee-supplicant to function properly or not. This attribute will be present for
		devices which depend on tee-supplicant to be running.
