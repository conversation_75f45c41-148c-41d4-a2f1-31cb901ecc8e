What:		/sys/firmware/opal/psr
Date:		August 2017
Contact:	Linux for PowerPC mailing list <<EMAIL>>
Description:	Power-Shift-Ratio directory for Powernv P9 servers

		Power-Shift-Ratio allows to provide hints the firmware
		to shift/throttle power between different entities in
		the system. Each attribute in this directory indicates
		a settable PSR.

What:		/sys/firmware/opal/psr/cpu_to_gpu_X
Date:		August 2017
Contact:	Linux for PowerPC mailing list <<EMAIL>>
Description:	PSR sysfs attributes for Powernv P9 servers

		Power-Shift-Ratio between CPU and GPU for a given chip
		with chip-id X. This file gives the ratio (0-100)
		which is used by OCC for power-capping.
