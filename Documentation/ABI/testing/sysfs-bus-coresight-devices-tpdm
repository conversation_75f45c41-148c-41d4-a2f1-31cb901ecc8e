What:		/sys/bus/coresight/devices/<tpdm-name>/integration_test
Date:		January 2023
KernelVersion	6.2
Contact:	<PERSON><PERSON> (QUIC) <quic_jinlma<PERSON>@quicinc.com>, <PERSON> (QUIC) <<EMAIL>>
Description:
		(Write) Run integration test for tpdm. Integration test
		will generate test data for tpdm. It can help to make
		sure that the trace path is enabled and the link configurations
		are fine.

		Accepts only one of the 2 values -  1 or 2.
		1 : Generate 64 bits data
		2 : Generate 32 bits data

What:		/sys/bus/coresight/devices/<tpdm-name>/reset_dataset
Date:		March 2023
KernelVersion	6.7
Contact:	<PERSON><PERSON> (QUIC) <<EMAIL>>, <PERSON> (QUIC) <<EMAIL>>
Description:
		(Write) Reset the dataset of the tpdm.

		Accepts only one value -  1.
		1 : Reset the dataset of the tpdm

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_trig_type
Date:		March 2023
KernelVersion	6.7
Contact:	<PERSON><PERSON> (QUIC) <<EMAIL>>, <PERSON> (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the trigger type of the DSB for tpdm.

		Accepts only one of the 2 values -  0 or 1.
		0 : Set the DSB trigger type to false
		1 : Set the DSB trigger type to true

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_trig_ts
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the trigger timestamp of the DSB for tpdm.

		Accepts only one of the 2 values -  0 or 1.
		0 : Set the DSB trigger type to false
		1 : Set the DSB trigger type to true

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_mode
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the programming mode of the DSB for tpdm.

		Accepts the value needs to be greater than 0. What data
		bits do is listed below.
		Bit[0:1] : Test mode control bit for choosing the inputs.
		Bit[3] : Set to 0 for low performance mode. Set to 1 for high
		performance mode.
		Bit[4:8] : Select byte lane for high performance mode.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_edge/ctrl_idx
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the index number of the edge detection for the DSB
		subunit TPDM. Since there are at most 256 edge detections, this
		value ranges from 0 to 255.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_edge/ctrl_val
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		Write a data to control the edge detection corresponding to
		the index number. Before writing data to this sysfs file,
		"ctrl_idx" should be written first to configure the index
		number of the edge detection which needs to be controlled.

		Accepts only one of the following values.
		0 - Rising edge detection
		1 - Falling edge detection
		2 - Rising and falling edge detection (toggle detection)


What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_edge/ctrl_mask
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		Write a data to mask the edge detection corresponding to the index
		number. Before writing data to this sysfs file, "ctrl_idx" should
		be written first to configure the index number of the edge detection
		which needs to be masked.

		Accepts only one of the 2 values -  0 or 1.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_edge/edcr[0:15]
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		Read a set of the edge control value of the DSB in TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_edge/edcmr[0:7]
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		Read a set of the edge control mask of the DSB in TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_trig_patt/xpr[0:7]
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the value of the trigger pattern for the DSB
		subunit TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_trig_patt/xpmr[0:7]
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the mask of the trigger pattern for the DSB
		subunit TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_patt/tpr[0:7]
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the value of the pattern for the DSB subunit TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_patt/tpmr[0:7]
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the mask of the pattern for the DSB subunit TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_patt/enable_ts
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(Write) Set the pattern timestamp of DSB tpdm. Read
		the pattern timestamp of DSB tpdm.

		Accepts only one of the 2 values -  0 or 1.
		0 : Disable DSB pattern timestamp.
		1 : Enable DSB pattern timestamp.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_patt/set_type
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(Write) Set the pattern type of DSB tpdm. Read
		the pattern type of DSB tpdm.

		Accepts only one of the 2 values -  0 or 1.
		0 : Set the DSB pattern type to value.
		1 : Set the DSB pattern type to toggle.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_msr/msr[0:31]
Date:		March 2023
KernelVersion	6.7
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the MSR(mux select register) for the DSB subunit
		TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/cmb_mode
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:	(Write) Set the data collection mode of CMB tpdm. Continuous
		change creates CMB data set elements on every CMBCLK edge.
		Trace-on-change creates CMB data set elements only when a new
		data set element differs in value from the previous element
		in a CMB data set.

		Accepts only one of the 2 values -  0 or 1.
		0 : Continuous CMB collection mode.
		1 : Trace-on-change CMB collection mode.

What:		/sys/bus/coresight/devices/<tpdm-name>/cmb_trig_patt/xpr[0:1]
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the value of the trigger pattern for the CMB
		subunit TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/cmb_trig_patt/xpmr[0:1]
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the mask of the trigger pattern for the CMB
		subunit TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_patt/tpr[0:1]
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the value of the pattern for the CMB subunit TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/dsb_patt/tpmr[0:1]
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the mask of the pattern for the CMB subunit TPDM.

What:		/sys/bus/coresight/devices/<tpdm-name>/cmb_patt/enable_ts
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(Write) Set the pattern timestamp of CMB tpdm. Read
		the pattern timestamp of CMB tpdm.

		Accepts only one of the 2 values -  0 or 1.
		0 : Disable CMB pattern timestamp.
		1 : Enable CMB pattern timestamp.

What:		/sys/bus/coresight/devices/<tpdm-name>/cmb_trig_ts
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the trigger timestamp of the CMB for tpdm.

		Accepts only one of the 2 values -  0 or 1.
		0 : Set the CMB trigger type to false
		1 : Set the CMB trigger type to true

What:		/sys/bus/coresight/devices/<tpdm-name>/cmb_ts_all
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Read or write the status of timestamp upon all interface.
		Only value 0 and 1  can be written to this node. Set this node to 1 to request
		timestamp to all trace packet.
		Accepts only one of the 2 values -  0 or 1.
		0 : Disable the timestamp of all trace packets.
		1 : Enable the timestamp of all trace packets.

What:		/sys/bus/coresight/devices/<tpdm-name>/cmb_msr/msr[0:31]
Date:		January 2024
KernelVersion	6.9
Contact:	Jinlong Mao (QUIC) <<EMAIL>>, Tao Zhang (QUIC) <<EMAIL>>
Description:
		(RW) Set/Get the MSR(mux select register) for the CMB subunit
		TPDM.
