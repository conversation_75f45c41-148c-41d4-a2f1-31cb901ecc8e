What:		/sys/hypervisor/guest_type
Date:		June 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	If running under Xen:
		Type of guest:
		"Xen": standard guest type on arm
		"HVM": fully virtualized guest (x86)
		"PV": paravirtualized guest (x86)
		"PVH": fully virtualized guest without legacy emulation (x86)

What:		/sys/hypervisor/pmu/pmu_mode
Date:		August 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	If running under Xen:
		Describes mode that <PERSON>en's performance-monitoring unit (PMU)
		uses. Accepted values are:

			======    ============================================
			"off"     PMU is disabled
			"self"    The guest can profile itself
			"hv"      The guest can profile itself and, if it is
				  privileged (e.g. dom0), the hypervisor
			"all"     The guest can profile itself, the hypervisor
				  and all other guests. Only available to
				  privileged guests.
			======    ============================================

What:           /sys/hypervisor/pmu/pmu_features
Date:           August 2015
KernelVersion:  4.3
Contact:        Boris Ostrovsky <<EMAIL>>
Description:	If running under Xen:
		Describes Xen PMU features (as an integer). A set bit indicates
		that the corresponding feature is enabled. See
		include/xen/interface/xenpmu.h for available features

What:		/sys/hypervisor/properties/buildid
Date:		June 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	If running under Xen:
		Build id of the hypervisor, needed for hypervisor live patching.
		Might return "<denied>" in case of special security settings
		in the hypervisor.
