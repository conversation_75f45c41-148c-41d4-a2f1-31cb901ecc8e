What:		/sys/devices/.../waiting_for_supplier
Date:		May 2020
Contact:	<PERSON><PERSON> <sara<PERSON><PERSON>@google.com>
Description:
		The /sys/devices/.../waiting_for_supplier attribute is only
		present when fw_devlink kernel command line option is enabled
		and is set to something stricter than "permissive".  It is
		removed once a device probes successfully (because the
		information is no longer relevant). The number read from it (0
		or 1) reflects whether the device is waiting for one or more
		suppliers to be added and then linked to using device links
		before the device can probe.

		A value of 0 means the device is not waiting for any suppliers
		to be added before it can probe.  A value of 1 means the device
		is waiting for one or more suppliers to be added before it can
		probe.
