What:		/sys/class/usb_power_delivery
Date:		May 2022
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		Directory for USB Power Delivery devices.

What:		/sys/class/usb_power_delivery/.../revision
Date:		May 2022
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		File showing the USB Power Delivery Specification Revision used
		in communication.

What:		/sys/class/usb_power_delivery/.../version
Date:		May 2022
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		This is an optional attribute file showing the version of the
		specific revision of the USB Power Delivery Specification. In
		most cases the specification version is not known and the file
		is not available.

What:		/sys/class/usb_power_delivery/.../source-capabilities
Date:		May 2022
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		The source capabilities message "Source_Capabilities" contains a
		set of Power Data Objects (PDO), each representing a type of
		power supply. The order of the PDO objects is defined in the USB
		Power Delivery Specification. Each PDO - power supply - will
		have its own device, and the PDO device name will start with the
		object position number as the first character followed by the
		power supply type name (":" as delimiter).

			/sys/class/usb_power_delivery/.../source_capabilities/<position>:<type>

What:		/sys/class/usb_power_delivery/.../sink-capabilities
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The sink capability message "Sink_Capabilities" contains a set
		of Power Data Objects (PDO) just like with source capabilities,
		but instead of describing the power capabilities, these objects
		describe the power requirements.

		The order of the objects in the sink capability message is the
		same as with the source capabilities message.

Fixed Supplies

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:fixed_supply
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Devices containing the attributes (the bit fields) defined for
		Fixed Supplies.

		The device "1:fixed_supply" is special. USB Power Delivery
		Specification dictates that the first PDO (at object position
		1), and the only mandatory PDO, is always the vSafe5V Fixed
		Supply Object. vSafe5V Object has additional fields defined for
		it that the other Fixed Supply Objects do not have and that are
		related to the USB capabilities rather than power capabilities.

What:		/sys/class/usb_power_delivery/.../<capability>/1:fixed_supply/dual_role_power
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This file contains boolean value that tells does the device
		support both source and sink power roles.

What:		/sys/class/usb_power_delivery/.../source-capabilities/1:fixed_supply/usb_suspend_supported
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This file shows the value of the USB Suspend Supported bit in
		vSafe5V Fixed Supply Object. If the bit is set then the device
		will follow the USB 2.0 and USB 3.2 rules for suspend and
		resume.

What:		/sys/class/usb_power_delivery/.../sink-capabilities/1:fixed_supply/higher_capability
Date:		February 2023
Contact:	Saranya Gopal <<EMAIL>>
Description:
		This file shows the value of the Higher capability bit in
		vsafe5V Fixed Supply Object. If the bit is set, then the sink
		needs more than vsafe5V(eg. 12 V) to provide full functionality.
		Valid values: 0, 1

What:		/sys/class/usb_power_delivery/.../<capability>/1:fixed_supply/unconstrained_power
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This file shows the value of the Unconstrained Power bit in
		vSafe5V Fixed Supply Object. The bit is set when an external
		source of power, powerful enough to power the entire system on
		its own, is available for the device.

What:		/sys/class/usb_power_delivery/.../<capability>/1:fixed_supply/usb_communication_capable
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This file shows the value of the USB Communication Capable bit in
		vSafe5V Fixed Supply Object.

What:		/sys/class/usb_power_delivery/.../<capability>/1:fixed_supply/dual_role_data
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This file shows the value of the Dual-Role Data bit in vSafe5V
		Fixed Supply Object. Dual role data means ability act as both
		USB host and USB device.

What:		/sys/class/usb_power_delivery/.../<capability>/1:fixed_supply/unchunked_extended_messages_supported
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This file shows the value of the Unchunked Extended Messages
		Supported bit in vSafe5V Fixed Supply Object.

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:fixed_supply/voltage
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The voltage the supply supports in millivolts.

What:		/sys/class/usb_power_delivery/.../source-capabilities/<position>:fixed_supply/peak_current
Date:		October 2023
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This file shows the value of the Fixed Power Source Peak Current
		Capability field.

What:		/sys/class/usb_power_delivery/.../source-capabilities/<position>:fixed_supply/maximum_current
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Maximum current of the fixed source supply in milliamperes.

What:		/sys/class/usb_power_delivery/.../sink-capabilities/<position>:fixed_supply/operational_current
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Operational current of the sink in milliamperes.

What:		/sys/class/usb_power_delivery/.../sink-capabilities/<position>:fixed_supply/fast_role_swap_current
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This file contains the value of the "Fast Role Swap USB Type-C
		Current" field that tells the current level the sink requires
		after a Fast Role Swap.
		0 - Fast Swap not supported"
		1 - Default USB Power"
		2 - 1.5A@5V"
		3 - 3.0A@5V"

Variable Supplies

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:variable_supply
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Variable Power Supply PDO.

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:variable_supply/maximum_voltage
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Maximum Voltage in millivolts.

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:variable_supply/minimum_voltage
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Minimum Voltage in millivolts.

What:		/sys/class/usb_power_delivery/.../source-capabilities/<position>:variable_supply/maximum_current
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The maximum current in milliamperes that the source can supply
		at the given Voltage range.

What:		/sys/class/usb_power_delivery/.../sink-capabilities/<position>:variable_supply/operational_current
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The operational current in milliamperes that the sink requires
		at the given Voltage range.

Battery Supplies

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:battery
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Battery PDO.

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:battery/maximum_voltage
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Maximum Voltage in millivolts.

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:battery/minimum_voltage
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Minimum Voltage in millivolts.

What:		/sys/class/usb_power_delivery/.../source-capabilities/<position>:battery/maximum_power
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Maximum allowable Power in milliwatts.

What:		/sys/class/usb_power_delivery/.../sink-capabilities/<position>:battery/operational_power
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The operational power that the sink requires at the given
		voltage range.

Standard Power Range (SPR) Programmable Power Supplies

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:programmable_supply
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Programmable Power Supply (PPS) Augmented PDO (APDO).

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:programmable_supply/maximum_voltage
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Maximum Voltage in millivolts.

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:programmable_supply/minimum_voltage
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Minimum Voltage in millivolts.

What:		/sys/class/usb_power_delivery/.../<capability>/<position>:programmable_supply/maximum_current
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Maximum Current in milliamperes.

What:		/sys/class/usb_power_delivery/.../source-capabilities/<position>:programmable_supply/pps_power_limited
Date:		May 2022
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The PPS Power Limited bit indicates whether or not the source
		supply will exceed the rated output power if requested.
