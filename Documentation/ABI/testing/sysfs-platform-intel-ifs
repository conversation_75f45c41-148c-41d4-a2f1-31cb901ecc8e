Device instance to test mapping
intel_ifs_0  ->  Scan Test
intel_ifs_1  ->  Array BIST test

What:		/sys/devices/virtual/misc/intel_ifs_<N>/run_test
Date:		Nov 16 2022
KernelVersion:	6.2
Contact:	"Jith<PERSON>" <<EMAIL>>
Description:	Write <cpu#> to trigger IFS test for one online core.
		Note that the test is per core. The cpu# can be
		for any thread on the core. Running on one thread
		completes the test for the core containing that thread.
		Example: to test the core containing cpu5: echo 5 >
		/sys/devices/virtual/misc/intel_ifs_<N>/run_test
Devices:	all

What:		/sys/devices/virtual/misc/intel_ifs_<N>/status
Date:		Nov 16 2022
KernelVersion:	6.2
Contact:	"Jithu Joseph" <<EMAIL>>
Description:	The status of the last test. It can be one of "pass", "fail"
		or "untested".
Devices:	all

What:		/sys/devices/virtual/misc/intel_ifs_<N>/details
Date:		Nov 16 2022
KernelVersion:	6.2
Contact:	"Ji<PERSON><PERSON>" <<EMAIL>>
Description:	Additional information regarding the last test. The details file reports
		the hex value of the STATUS MSR for this test. Note that the error_code field
		may contain driver defined software code not defined in the Intel SDM.
Devices:	all

What:		/sys/devices/virtual/misc/intel_ifs_<N>/image_version
Date:		Nov 16 2022
KernelVersion:	6.2
Contact:	"Jithu Joseph" <<EMAIL>>
Description:	Version (hexadecimal) of loaded IFS test image. If no test image
		is loaded reports "none". Only present for device instances where a test image
		is applicable.
Devices:	intel_ifs_0

What:		/sys/devices/virtual/misc/intel_ifs_<N>/current_batch
Date:		Nov 16 2022
KernelVersion:	6.2
Contact:	"Jithu Joseph" <<EMAIL>>
Description:	Write a number less than or equal to 0xff to load an IFS test image.
		The number written treated as the 2 digit suffix in the following file name:
		/lib/firmware/intel/ifs_<N>/ff-mm-ss-02x.scan
		Reading the file will provide the suffix of the currently loaded IFS test image.
		This file is present only for device instances where a test image is applicable.
Devices:	intel_ifs_0
