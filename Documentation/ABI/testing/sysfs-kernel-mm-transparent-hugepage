What:		/sys/kernel/mm/transparent_hugepage/
Date:		April 2024
Contact:	Linux memory management mailing list <<EMAIL>>
Description:
		/sys/kernel/mm/transparent_hugepage/ contains a number of files and
		subdirectories,

			- defrag
			- enabled
			- hpage_pmd_size
			- khugepaged
			- shmem_enabled
			- use_zero_page
			- subdirectories of the form hugepages-<size>kB, where <size>
			  is the page size of the hugepages supported by the kernel/CPU
			  combination.

		See Documentation/admin-guide/mm/transhuge.rst for details.
