What:		/sys/class/leds/<led>/ports/<port>
Date:		September 2016
KernelVersion:	4.9
Contact:	<EMAIL>
		<EMAIL>
Description:
		Every dir entry represents a single USB port that can be
		selected for the USB port trigger. Selecting ports makes trigger
		observing them for any connected devices and lighting on LED if
		there are any.

		Echoing "1" value selects USB port. Echoing "0" unselects it.
		Current state can be also read.
