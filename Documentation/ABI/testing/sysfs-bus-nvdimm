What:          nvdimm
Date:          July 2020
KernelVersion: 5.8
Contact:       <PERSON> <<EMAIL>>
Description:

The libnvdimm sub-system implements a common sysfs interface for
platform nvdimm resources. See Documentation/driver-api/nvdimm/.

What:           /sys/bus/event_source/devices/nmemX/format
Date:           February 2022
KernelVersion:  5.18
Contact:        <PERSON><PERSON><PERSON> <<EMAIL>>
Description:	(RO) Attribute group to describe the magic bits
		that go into perf_event_attr.config for a particular pmu.
		(See ABI/testing/sysfs-bus-event_source-devices-format).

		Each attribute under this group defines a bit range of the
		perf_event_attr.config. Supported attribute is listed
		below::

		  event  = "config:0-4"  - event ID

		For example::

		  ctl_res_cnt = "event=0x1"

What:           /sys/bus/event_source/devices/nmemX/events
Date:           February 2022
KernelVersion:  5.18
Contact:        <PERSON><PERSON><PERSON> <<EMAIL>>
Description:	(RO) Attribute group to describe performance monitoring events
                for the nvdimm memory device. Each attribute in this group
                describes a single performance monitoring event supported by
                this nvdimm pmu.  The name of the file is the name of the event.
                (See ABI/testing/sysfs-bus-event_source-devices-events). A
                listing of the events supported by a given nvdimm provider type
                can be found in Documentation/driver-api/nvdimm/$provider.

What:          /sys/bus/event_source/devices/nmemX/cpumask
Date:          February 2022
KernelVersion:  5.18
Contact:        Kajol Jain <<EMAIL>>
Description:	(RO) This sysfs file exposes the cpumask which is designated to
		to retrieve nvdimm pmu event counter data.

What:		/sys/bus/nd/devices/nmemX/cxl/id
Date:		November 2022
KernelVersion:	6.2
Contact:	Dave Jiang <<EMAIL>>
Description:	(RO) Show the id (serial) of the device. This is CXL specific.

What:		/sys/bus/nd/devices/nmemX/cxl/provider
Date:		November 2022
KernelVersion:	6.2
Contact:	Dave Jiang <<EMAIL>>
Description:	(RO) Shows the CXL bridge device that ties to a CXL memory device
		to this NVDIMM device. I.e. the parent of the device returned is
		a /sys/bus/cxl/devices/memX instance.
