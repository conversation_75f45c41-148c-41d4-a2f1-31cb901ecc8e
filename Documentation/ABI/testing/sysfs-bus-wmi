What:		/sys/bus/wmi/devices/.../driver_override
Date:		February 2024
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		This file allows the driver for a device to be specified which
		will override standard ID table matching.
		When specified, only a driver with a name matching the value
		written to driver_override will have an opportunity to bind
		to the device.
		The override is specified by writing a string to the
		driver_override file (echo wmi-event-dummy > driver_override).
		The override may be cleared with an empty string (echo > \
		driver_override) which returns the device to standard matching
		rules binding.
		Writing to driver_override does not automatically unbind the
		device from its current driver or make any attempt to automatically
		load the specified driver. If no driver with a matching name is
		currently loaded in the kernel, the device will not bind to any
		driver.
		This also allows devices to opt-out of driver binding using a
		driver_override name such as "none". Only a single driver may be
		specified in the override, there is no support for parsing delimiters.

What:		/sys/bus/wmi/devices/.../modalias
Date:		November 20:15
Contact:	<PERSON> <<EMAIL>>
Description:
		This file contains the MODALIAS value emitted by uevent for a
		given WMI device.

		Format: wmi:XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX.

What:		/sys/bus/wmi/devices/.../guid
Date:		November 2015
Contact:	Andy Lutomirski <<EMAIL>>
Description:
		This file contains the GUID used to match WMI devices to
		compatible WMI drivers. This GUID is not necessarily unique
		inside a given machine, it is solely used to identify the
		interface exposed by a given WMI device.

What:		/sys/bus/wmi/devices/.../object_id
Date:		November 2015
Contact:	Andy Lutomirski <<EMAIL>>
Description:
		This file contains the WMI object ID used internally to construct
		the ACPI method names used by non-event WMI devices. It contains
		two ASCII letters.

What:		/sys/bus/wmi/devices/.../notify_id
Date:		November 2015
Contact:	Andy Lutomirski <<EMAIL>>
Description:
		This file contains the WMI notify ID used internally to map ACPI
		events to WMI event devices. It contains two ASCII letters.

What:		/sys/bus/wmi/devices/.../instance_count
Date:		November 2015
Contact:	Andy Lutomirski <<EMAIL>>
Description:
		This file contains the number of WMI object instances being
		present on a given WMI device. It contains a non-negative
		number.

What:		/sys/bus/wmi/devices/.../expensive
Date:		November 2015
Contact:	Andy Lutomirski <<EMAIL>>
Description:
		This file contains a boolean flag signaling if interacting with
		the given WMI device will consume significant CPU resources.
		The WMI driver core will take care of enabling/disabling such
		WMI devices.

What:		/sys/bus/wmi/devices/.../setable
Date:		May 2017
Contact:	Darren Hart (VMware) <<EMAIL>>
Description:
		This file contains a boolean flags signaling the data block
		aassociated with the given WMI device is writable. If the
		given WMI device is not associated with a data block, then
		this file will not exist.
