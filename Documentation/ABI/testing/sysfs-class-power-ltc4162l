What:		/sys/class/power_supply/ltc4162-l/charge_status
Date:		Januari 2021
KernelVersion:	5.11
Description:
		Detailed charge status information as reported by the chip.

		Access: Read

		Valid values:
			ilim_reg_active
			thermal_reg_active
			vin_uvcl_active
			iin_limit_active
			constant_current
			constant_voltage
			charger_off

What:		/sys/class/power_supply/ltc4162-l/ibat
Date:		Januari 2021
KernelVersion:	5.11
Description:
		Battery input current as measured by the charger. Negative value
		means that the battery is discharging.

		Access: Read

		Valid values: Signed value in microamps

What:		/sys/class/power_supply/ltc4162-l/vbat
Date:		Januari 2021
KernelVersion:	5.11
Description:
		Battery voltage as measured by the charger.

		Access: Read

		Valid values: In microvolts

What:		/sys/class/power_supply/ltc4162-l/vbat_avg
Date:		Januari 2021
KernelVersion:	5.11
Description:
		Battery voltage, averaged over time, as measured by the charger.

		Access: Read

		Valid values: In microvolts

What:		/sys/class/power_supply/ltc4162-l/force_telemetry
Date:		Januari 2021
KernelVersion:	5.11
Description:
		To save battery current, the measurement system is disabled if
		the battery is the only source of power. This affects all
		voltage, current and temperature measurements.
		Write a "1" to this to keep performing telemetry once every few
		seconds, even when running on battery (as reported by the online
		property, which is "1" when external power is available and "0"
		when the system runs on battery).

		Access: Read, Write

		Valid values: 0 (disabled) or 1 (enabled)

What:		/sys/class/power_supply/ltc4162-l/arm_ship_mode
Date:		Januari 2021
KernelVersion:	5.11
Description:
		The charger will normally drain the battery while inactive,
		typically drawing about 54 microamps. Write a "1" to this
		property to arm a special "ship" mode that extends shelf life
		by reducing the leakage to about 2.8 microamps. The chip will
		remain in this mode (and no longer respond to I2C commands)
		until some external power-supply is attached raising the input
		voltage above 1V. It will then automatically revert to "0".
		Writing a "0" to the property cancels the "ship" mode request.
		The ship mode, when armed, activates once the input voltage
		drops below 1V.

		Access: Read, Write

		Valid values: 0 (disable) or 1 (enable)
