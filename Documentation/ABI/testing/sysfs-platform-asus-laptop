What:		/sys/devices/platform/asus_laptop/display
Date:		January 2007
KernelVersion:	2.6.20
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		This file allows display switching. The value
		is composed by 4 bits and defined as follow::

		  4321
		  |||`- LCD
		  ||`-- CRT
		  |`--- TV
		  `---- DVI

		Ex:
		    - 0 (0000b) means no display
		    - 3 (0011b) CRT+LCD.

What:		/sys/devices/platform/asus_laptop/gps
Date:		January 2007
KernelVersion:	2.6.20
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the gps device. 1 means on, 0 means off.
Users:		Lapsus

What:		/sys/devices/platform/asus_laptop/ledd
Date:		January 2007
KernelVersion:	2.6.20
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Some models like the W1N have a LED display that can be
		used to display several items of information.
		To control the LED display, use the following::

		    echo 0x0T000DDD > /sys/devices/platform/asus_laptop/

		where T control the 3 letters display, and DDD the 3 digits display.
		The DDD table can be found in Documentation/admin-guide/laptops/asus-laptop.rst

What:		/sys/devices/platform/asus_laptop/bluetooth
Date:		January 2007
KernelVersion:	2.6.20
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the bluetooth device. 1 means on, 0 means off.
		This may control the led, the device or both.
Users:		Lapsus

What:		/sys/devices/platform/asus_laptop/wlan
Date:		January 2007
KernelVersion:	2.6.20
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the wlan device. 1 means on, 0 means off.
		This may control the led, the device or both.
Users:		Lapsus

What:		/sys/devices/platform/asus_laptop/wimax
Date:		October 2010
KernelVersion:	2.6.37
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the wimax device. 1 means on, 0 means off.

What:		/sys/devices/platform/asus_laptop/wwan
Date:		October 2010
KernelVersion:	2.6.37
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the wwan (3G) device. 1 means on, 0 means off.
