What:		/sys/devices/.../online
Date:		April 2013
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/devices/.../online attribute is only present for
		devices whose bus types provide .online() and .offline()
		callbacks.  The number read from it (0 or 1) reflects the value
		of the device's 'offline' field.  If that number is 1 and '0'
		(or 'n', or 'N') is written to this file, the device bus type's
		.offline() callback is executed for the device and (if
		successful) its 'offline' field is updated accordingly.  In
		turn, if that number is 0 and '1' (or 'y', or 'Y') is written to
		this file, the device bus type's .online() callback is executed
		for the device and (if successful) its 'offline' field is
		updated as appropriate.

		After a successful execution of the bus type's .offline()
		callback the device cannot be used for any purpose until either
		it is removed (i.e. device_del() is called for it), or its bus
		type's .online() is executed successfully.
