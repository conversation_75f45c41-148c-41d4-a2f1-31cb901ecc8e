What:		/sys/bus/event_source/devices/dmar*/format
Date:		Jan 2023
KernelVersion:  6.3
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	Read-only.  Attribute group to describe the magic bits
		that go into perf_event_attr.config,
		perf_event_attr.config1 or perf_event_attr.config2 for
		the IOMMU pmu.  (See also
		ABI/testing/sysfs-bus-event_source-devices-format).

		Each attribute in this group defines a bit range in
		perf_event_attr.config, perf_event_attr.config1,
		or perf_event_attr.config2. All supported attributes
		are listed below (See the VT-d Spec 4.0 for possible
		attribute values)::

		    event		= "config:0-27"   - event ID
		    event_group		= "config:28-31"  - event group ID

		    filter_requester_en	= "config1:0"     - Enable Requester ID filter
		    filter_domain_en	= "config1:1"     - Enable Domain ID filter
		    filter_pasid_en	= "config1:2"     - Enable PASID filter
		    filter_ats_en	= "config1:3"     - Enable Address Type filter
		    filter_page_table_en= "config1:4"     - Enable Page Table Level filter
		    filter_requester_id	= "config1:16-31" - Requester ID filter
		    filter_domain	= "config1:32-47" - Domain ID filter
		    filter_pasid	= "config2:0-21"  - PASID filter
		    filter_ats		= "config2:24-28" - Address Type filter
		    filter_page_table	= "config2:32-36" - Page Table Level filter

What:		/sys/bus/event_source/devices/dmar*/cpumask
Date:		Jan 2023
KernelVersion:	6.3
Contact:	Kan Liang <<EMAIL>>
Description:	Read-only. This file always returns the CPU to which the
		IOMMU pmu is bound for access to all IOMMU pmu performance
		monitoring events.
