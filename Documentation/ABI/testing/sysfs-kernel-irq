What:		/sys/kernel/irq
Date:		September 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:	Directory containing information about the system's IRQs.
		Specifically, data from the associated struct irq_desc.
		The information here is similar to that in /proc/interrupts
		but in a more machine-friendly format.  This directory contains
		one subdirectory for each Linux IRQ number.

What:		/sys/kernel/irq/<irq>/actions
Date:		September 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:	The IRQ action chain.  A comma-separated list of zero or more
		device names associated with this interrupt.

What:		/sys/kernel/irq/<irq>/chip_name
Date:		September 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:	Human-readable chip name supplied by the associated device
		driver.

What:		/sys/kernel/irq/<irq>/hwirq
Date:		September 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:	When interrupt translation domains are used, this file contains
		the underlying hardware IRQ number used for this Linux IRQ.

What:		/sys/kernel/irq/<irq>/name
Date:		September 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:	Human-readable flow handler name as defined by the irq chip
		driver.

What:		/sys/kernel/irq/<irq>/per_cpu_count
Date:		September 2016
KernelVersion:	4.9
Contact:	Craig Gallek <<EMAIL>>
Description:	The number of times the interrupt has fired since boot.  This
		is a comma-separated list of counters; one per CPU in CPU id
		order.  NOTE: This file consistently shows counters for all
		CPU ids.  This differs from the behavior of /proc/interrupts
		which only shows counters for online CPUs.

What:		/sys/kernel/irq/<irq>/type
Date:		September 2016
KernelVersion:	4.9
Contact:	Craig Gallek <<EMAIL>>
Description:	The type of the interrupt.  Either the string 'level' or 'edge'.

What:		/sys/kernel/irq/<irq>/wakeup
Date:		March 2018
KernelVersion:	4.17
Contact:	Andy Shevchenko <<EMAIL>>
Description:	The wakeup state of the interrupt. Either the string
		'enabled' or 'disabled'.
