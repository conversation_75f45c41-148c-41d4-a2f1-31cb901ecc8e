What:		/sys/bus/platform/devices/occ-hwmon.X/ffdc
KernelVersion:	5.15
Contact:	<EMAIL>
Description:
		Contains the First Failure Data Capture from the SBEFIFO
		hardware, if there is any from a previous transfer. Otherwise,
		the file is empty. The data is cleared when it's been
		completely read by a user. As the name suggests, only the data
		from the first error is saved, until it's cleared upon read. The OCC hwmon driver, running on
		a Baseboard Management Controller (BMC), communicates with
		POWER9 and up processors over the Self-Boot Engine (SBE) FIFO.
		In many error conditions, the SBEFIFO will return error data
		indicating the type of error and system state, etc.
