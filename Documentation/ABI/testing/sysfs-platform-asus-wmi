What:		/sys/devices/platform/<platform>/cpufv
Date:		Oct 2010
KernelVersion:	2.6.37
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Change CPU clock configuration (write-only).
		There are three available clock configuration:

		    * 0 -> Super Performance Mode
		    * 1 -> High Performance Mode
		    * 2 -> Power Saving Mode

What:		/sys/devices/platform/<platform>/camera
Date:		Jan 2010
KernelVersion:	2.6.39
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the camera. 1 means on, 0 means off.

What:		/sys/devices/platform/<platform>/cardr
Date:		Jan 2010
KernelVersion:	2.6.39
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the card reader. 1 means on, 0 means off.

What:		/sys/devices/platform/<platform>/touchpad
Date:		Jan 2010
KernelVersion:	2.6.39
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the card touchpad. 1 means on, 0 means off.

What:		/sys/devices/platform/<platform>/lid_resume
Date:		May 2012
KernelVersion:	3.5
Contact:	"<PERSON><PERSON><PERSON>" <<EMAIL>>
Description:
		Resume on lid open. 1 means on, 0 means off.

What:		/sys/devices/platform/<platform>/fan_boost_mode
Date:		Sep 2019
KernelVersion:	5.3
Contact:	"Yurii Pavlovskyi" <<EMAIL>>
Description:
		Fan boost mode:
			* 0 - normal,
			* 1 - overboost,
			* 2 - silent

What:		/sys/devices/platform/<platform>/throttle_thermal_policy
Date:		Dec 2019
KernelVersion:	5.6
Contact:	"Leonid Maksymchuk" <<EMAIL>>
Description:
		Throttle thermal policy mode:
			* 0 - default,
			* 1 - overboost,
			* 2 - silent

What:		/sys/devices/platform/<platform>/gpu_mux_mode
Date:		Aug 2022
KernelVersion:	6.1
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Switch the GPU hardware MUX mode. Laptops with this feature can
		can be toggled to boot with only the dGPU (discrete mode) or in
		standard Optimus/Hybrid mode. On switch a reboot is required:

			* 0 - Discrete GPU,
			* 1 - Optimus/Hybrid,

What:		/sys/devices/platform/<platform>/dgpu_disable
Date:		Aug 2022
KernelVersion:	5.17
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Disable discrete GPU:
			* 0 - Enable dGPU,
			* 1 - Disable dGPU

What:		/sys/devices/platform/<platform>/egpu_enable
Date:		Aug 2022
KernelVersion:	5.17
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Enable the external GPU paired with ROG X-Flow laptops.
		Toggling this setting will also trigger ACPI to disable the dGPU:

			* 0 - Disable,
			* 1 - Enable

What:		/sys/devices/platform/<platform>/panel_od
Date:		Aug 2022
KernelVersion:	5.17
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Enable an LCD response-time boost to reduce or remove ghosting:
			* 0 - Disable,
			* 1 - Enable

What:		/sys/devices/platform/<platform>/charge_mode
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Get the current charging mode being used:
			* 1 - Barrel connected charger,
			* 2 - USB-C charging
			* 3 - Both connected, barrel used for charging

What:		/sys/devices/platform/<platform>/egpu_connected
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Show if the egpu (XG Mobile) is correctly connected:
			* 0 - False,
			* 1 - True

What:		/sys/devices/platform/<platform>/mini_led_mode
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Change the mini-LED mode:
			* 0 - Single-zone,
			* 1 - Multi-zone
			* 2 - Multi-zone strong (available on newer generation mini-led)

What:		/sys/devices/platform/<platform>/available_mini_led_mode
Date:		Apr 2024
KernelVersion:	6.10
Contact:	"Luke Jones" <<EMAIL>>
Description:
		List the available mini-led modes.

What:		/sys/devices/platform/<platform>/ppt_pl1_spl
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set the Package Power Target total of CPU: PL1 on Intel, SPL on AMD.
		Shown on Intel+Nvidia or AMD+Nvidia based systems:

			* min=5, max=250

What:		/sys/devices/platform/<platform>/ppt_pl2_sppt
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set the Slow Package Power Tracking Limit of CPU: PL2 on Intel, SPPT,
		on AMD. Shown on Intel+Nvidia or AMD+Nvidia based systems:

			* min=5, max=250

What:		/sys/devices/platform/<platform>/ppt_fppt
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set the Fast Package Power Tracking Limit of CPU. AMD+Nvidia only:
			* min=5, max=250

What:		/sys/devices/platform/<platform>/ppt_apu_sppt
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set the APU SPPT limit. Shown on full AMD systems only:
			* min=5, max=130

What:		/sys/devices/platform/<platform>/ppt_platform_sppt
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set the platform SPPT limit. Shown on full AMD systems only:
			* min=5, max=130

What:		/sys/devices/platform/<platform>/nv_dynamic_boost
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set the dynamic boost limit of the Nvidia dGPU:
			* min=5, max=25

What:		/sys/devices/platform/<platform>/nv_temp_target
Date:		Jun 2023
KernelVersion:	6.5
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set the target temperature limit of the Nvidia dGPU:
			* min=75, max=87

What:		/sys/devices/platform/<platform>/boot_sound
Date:		Apr 2024
KernelVersion:	6.10
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set if the BIOS POST sound is played on boot.
			* 0 - False,
			* 1 - True

What:		/sys/devices/platform/<platform>/mcu_powersave
Date:		Apr 2024
KernelVersion:	6.10
Contact:	"Luke Jones" <<EMAIL>>
Description:
		Set if the MCU can go in to low-power mode on system sleep
			* 0 - False,
			* 1 - True
