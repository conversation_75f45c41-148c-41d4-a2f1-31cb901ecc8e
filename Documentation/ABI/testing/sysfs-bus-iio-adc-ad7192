What:		/sys/bus/iio/devices/iio:deviceX/ac_excitation_en
KernelVersion:
Contact:	<EMAIL>
Description:
		This attribute, if available, is used to enable the AC
		excitation mode found on some converters. In ac excitation mode,
		the polarity of the excitation voltage is reversed on
		alternate cycles, to eliminate DC errors.

What:		/sys/bus/iio/devices/iio:deviceX/bridge_switch_en
KernelVersion:
Contact:	<EMAIL>
Description:
		This attribute, if available, is used to close or open the
		bridge power down switch found on some converters.
		In bridge applications, such as strain gauges and load cells,
		the bridge itself consumes the majority of the current in the
		system. To minimize the current consumption of the system,
		the bridge can be disconnected (when it is not being used
		using the bridge_switch_en attribute.

What:		/sys/bus/iio/devices/iio:deviceX/in_voltagex_sys_calibration
KernelVersion:
Contact:	<EMAIL>
Description:
		Initiates the system calibration procedure. This is done on a
		single channel at a time. Write '1' to start the calibration.

What:		/sys/bus/iio/devices/iio:deviceX/in_voltage2-voltage2_shorted_raw
KernelVersion:
Contact:	<EMAIL>
Description:
		Measure voltage from AIN2 pin connected to AIN(+)
		and AIN(-) shorted.

What:		/sys/bus/iio/devices/iio:deviceX/in_voltagex_sys_calibration_mode_available
KernelVersion:
Contact:	<EMAIL>
Description:
		Reading returns a list with the possible calibration modes.
		There are two available options:
		"zero_scale" - calibrate to zero scale
		"full_scale" - calibrate to full scale

What:		/sys/bus/iio/devices/iio:deviceX/in_voltagex_sys_calibration_mode
KernelVersion:
Contact:	<EMAIL>
Description:
		Sets up the calibration mode used in the system calibration
		procedure. Reading returns the current calibration mode.
		Writing sets the system calibration mode.
