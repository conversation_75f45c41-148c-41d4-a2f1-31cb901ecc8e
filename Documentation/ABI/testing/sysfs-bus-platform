What:		/sys/bus/platform/devices/.../driver_override
Date:		April 2014
Contact:	<PERSON> <<EMAIL>>
Description:
		This file allows the driver for a device to be specified which
		will override standard OF, ACPI, ID table, and name matching.
		When specified, only a driver with a name matching the value
		written to driver_override will have an opportunity to bind
		to the device.  The override is specified by writing a string
		to the driver_override file (echo vfio-platform > \
		driver_override) and may be cleared with an empty string
		(echo > driver_override).  This returns the device to standard
		matching rules binding.  Writing to driver_override does not
		automatically unbind the device from its current driver or make
		any attempt to automatically load the specified driver.  If no
		driver with a matching name is currently loaded in the kernel,
		the device will not bind to any driver.  This also allows
		devices to opt-out of driver binding using a driver_override
		name such as "none".  Only a single driver may be specified in
		the override, there is no support for parsing delimiters.

What:		/sys/bus/platform/devices/.../numa_node
Date:		June 2020
Contact:	Barry <PERSON> <<EMAIL>>
Description:
		This file contains the NUMA node to which the platform device
		is attached. It won't be visible if the node is unknown. The
		value comes from an ACPI _PXM method or a similar firmware
		source. Initial users for this file would be devices like
		arm smmu which are populated by arm64 acpi_iort.

What:		/sys/bus/platform/devices/.../msi_irqs/
Date:		August 2021
Contact:	Barry Song <<EMAIL>>
Description:
		The /sys/devices/.../msi_irqs directory contains a variable set
		of files, with each file being named after a corresponding msi
		irq vector allocated to that device.

What:		/sys/bus/platform/devices/.../msi_irqs/<N>
Date:		August 2021
Contact:	Barry Song <<EMAIL>>
Description:
		This attribute will show "msi" if <N> is a valid msi irq

What:		/sys/bus/platform/devices/.../modalias
Description:
		Same as MODALIAS in the uevent at device creation.

		A platform device that it is exposed via devicetree uses:

			- of:N`of node name`T`type`

		Other platform devices use, instead:

			- platform:`driver name`
