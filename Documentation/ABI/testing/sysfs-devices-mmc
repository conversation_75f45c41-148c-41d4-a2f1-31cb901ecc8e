What:		/sys/devices/.../mmc_host/mmcX/mmcX:XXXX/enhanced_area_offset
Date:		January 2011
Contact:	Chuanxiao Dong <<EMAIL>>
Description:
		Enhanced area is a new feature defined in eMMC4.4 standard.
		eMMC4.4 or later card can support such feature. This kind of
		area can help to improve the card performance. If the feature
		is enabled, this attribute will indicate the start address of
		enhanced data area. If not, this attribute will be -EINVAL.
		Unit Byte. Format decimal.

What:		/sys/devices/.../mmc_host/mmcX/mmcX:XXXX/enhanced_area_size
Date:		January 2011
Contact:	Chuanxiao Dong <<EMAIL>>
Description:
		Enhanced area is a new feature defined in eMMC4.4 standard.
		eMMC4.4 or later card can support such feature. This kind of
		area can help to improve the card performance. If the feature
		is enabled, this attribute will indicate the size of enhanced
		data area. If not, this attribute will be -EINVAL.
		Unit KByte. Format decimal.
