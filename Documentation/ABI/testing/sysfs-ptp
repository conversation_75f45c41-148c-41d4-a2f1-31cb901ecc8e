What:		/sys/class/ptp/
Date:		September 2010
Contact:	<PERSON> <<EMAIL>>
Description:
		This directory contains files and directories
		providing a standardized interface to the ancillary
		features of PTP hardware clocks.

What:		/sys/class/ptp/ptp<N>/
Date:		September 2010
Contact:	<PERSON> <richardco<PERSON><EMAIL>>
Description:
		This directory contains the attributes of the Nth PTP
		hardware clock registered into the PTP class driver
		subsystem.

What:		/sys/class/ptp/ptp<N>/clock_name
Date:		September 2010
Contact:	<PERSON> <<EMAIL>>
Description:
		This file contains the name of the PTP hardware clock
		as a human readable string. The purpose of this
		attribute is to provide the user with a "friendly
		name" and to help distinguish PHY based devices from
		MAC based ones. The string does not necessarily have
		to be any kind of unique id.

What:		/sys/class/ptp/ptp<N>/max_adjustment
Date:		September 2010
Contact:	<PERSON> <richard<PERSON><EMAIL>>
Description:
		This file contains the PTP hardware clock's maximum
		frequency adjustment value (a positive integer) in
		parts per billion.

What:		/sys/class/ptp/ptp<N>/max_vclocks
Date:		May 2021
Contact:	<PERSON><PERSON> Lu <<EMAIL>>
Description:
		This file contains the maximum number of ptp vclocks.
		Write integer to re-configure it.

What:		/sys/class/ptp/ptp<N>/n_alarms
Date:		September 2010
Contact:	Richard Cochran <<EMAIL>>
Description:
		This file contains the number of periodic or one shot
		alarms offer by the PTP hardware clock.

What:		/sys/class/ptp/ptp<N>/n_external_timestamps
Date:		September 2010
Contact:	Richard Cochran <<EMAIL>>
Description:
		This file contains the number of external timestamp
		channels offered by the PTP hardware clock.

What:		/sys/class/ptp/ptp<N>/n_periodic_outputs
Date:		September 2010
Contact:	Richard Cochran <<EMAIL>>
Description:
		This file contains the number of programmable periodic
		output channels offered by the PTP hardware clock.

What:		/sys/class/ptp/ptp<N>/n_pins
Date:		March 2014
Contact:	Richard Cochran <<EMAIL>>
Description:
		This file contains the number of programmable pins
		offered by the PTP hardware clock.

What:		/sys/class/ptp/ptp<N>/n_vclocks
Date:		May 2021
Contact:	Yangbo Lu <<EMAIL>>
Description:
		This file contains the number of virtual PTP clocks in
		use.  By default, the value is 0 meaning that only the
		physical clock is in use.  Setting the value creates
		the corresponding number of virtual clocks and causes
		the physical clock to become free running.  Setting the
		value back to 0 deletes the virtual clocks and
		switches the physical clock back to normal, adjustable
		operation.

What:		/sys/class/ptp/ptp<N>/pins
Date:		March 2014
Contact:	Richard Cochran <<EMAIL>>
Description:
		This directory contains one file for each programmable
		pin offered by the PTP hardware clock. The file name
		is the hardware dependent pin name. Reading from this
		file produces two numbers, the assigned function (see
		the `PTP_PF_` enumeration values in linux/ptp_clock.h)
		and the channel number. The function and channel
		assignment may be changed by two writing numbers into
		the file.

What:		/sys/class/ptp/ptp<N>/pps_available
Date:		September 2010
Contact:	Richard Cochran <<EMAIL>>
Description:
		This file indicates whether the PTP hardware clock
		supports a Pulse Per Second to the host CPU. Reading
		"1" means that the PPS is supported, while "0" means
		not supported.

What:		/sys/class/ptp/ptp<N>/extts_enable
Date:		September 2010
Contact:	Richard Cochran <<EMAIL>>
Description:
		This write-only file enables or disables external
		timestamps. To enable external timestamps, write the
		channel index followed by a "1" into the file.
		To disable external timestamps, write the channel
		index followed by a "0" into the file.

What:		/sys/class/ptp/ptp<N>/fifo
Date:		September 2010
Contact:	Richard Cochran <<EMAIL>>
Description:
		This file provides timestamps on external events, in
		the form of three integers: channel index, seconds,
		and nanoseconds.

What:		/sys/class/ptp/ptp<N>/period
Date:		September 2010
Contact:	Richard Cochran <<EMAIL>>
Description:
		This write-only file enables or disables periodic
		outputs. To enable a periodic output, write five
		integers into the file: channel index, start time
		seconds, start time nanoseconds, period seconds, and
		period nanoseconds. To disable a periodic output, set
		all the seconds and nanoseconds values to zero.

What:		/sys/class/ptp/ptp<N>/pps_enable
Date:		September 2010
Contact:	Richard Cochran <<EMAIL>>
Description:
		This write-only file enables or disables delivery of
		PPS events to the Linux PPS subsystem. To enable PPS
		events, write a "1" into the file. To disable events,
		write a "0" into the file.
