What:		/sys/class/leds/<led>/device/brightness
Date:		July 2020
KernelVersion:	5.9
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) On the front panel of the Turris Omnia router there is also
		a button which can be used to control the intensity of all the
		LEDs at once, so that if they are too bright, user can dim them.

		The microcontroller cycles between 8 levels of this global
		brightness (from 100% to 0%), but this setting can have any
		integer value between 0 and 100. It is therefore convenient to be
		able to change this setting from software.

		Format: %i

What:		/sys/class/leds/<led>/device/gamma_correction
Date:		August 2023
KernelVersion:	6.6
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) Newer versions of the microcontroller firmware of the
		Turris Omnia router support gamma correction for the RGB LEDs.
		This feature can be enabled/disabled by writing to this file.

		If the feature is not supported because the MCU firmware is too
		old, the file always reads as 0, and writing to the file results
		in the EOPNOTSUPP error.

		Format: %i
