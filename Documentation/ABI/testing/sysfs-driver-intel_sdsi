What:		/sys/bus/auxiliary/devices/intel_vsec.sdsi.X
Date:		Feb 2022
KernelVersion:	5.18
Contact:	"David <PERSON> Box" <<EMAIL>>
Description:
		This directory contains interface files for accessing Intel
		On Demand (formerly Software Defined Silicon or SDSi) features
		on a CPU. X represents the socket instance (though not the
		socket ID). The socket ID is determined by reading the
		registers file and decoding it per the specification.

		Some files communicate with On Demand hardware through a
		mailbox. Should the operation fail, one of the following error
		codes may be returned:

		==========	=====
		Error Code	Cause
		==========	=====
		EIO		General mailbox failure. Log may indicate cause.
		EBUSY		Mailbox is owned by another agent.
		EPERM		On Demand capability is not enabled in hardware.
		EPROTO		Failure in mailbox protocol detected by driver.
				See log for details.
		EOVERFLOW	For provision commands, the size of the data
				exceeds what may be written.
		ESPIPE		Seeking is not allowed.
		ETIMEDOUT	Failure to complete mailbox transaction in time.
		==========	=====

What:		/sys/bus/auxiliary/devices/intel_vsec.sdsi.X/guid
Date:		Feb 2022
KernelVersion:	5.18
Contact:	"David E<PERSON> Box" <<EMAIL>>
Description:
		(RO) The GUID for the registers file. The GUID identifies
		the layout of the registers file in this directory.
		Information about the register layouts for a particular GUID
		is available at http://github.com/intel/intel-sdsi

What:		/sys/bus/auxiliary/devices/intel_vsec.sdsi.X/registers
Date:		Feb 2022
KernelVersion:	5.18
Contact:	"David E. Box" <<EMAIL>>
Description:
		(RO) Contains information needed by applications to provision
		a CPU and monitor status information. The layout of this file
		is determined by the GUID in this directory. Information about
		the layout for a particular GUID is available at
		http://github.com/intel/intel-sdsi

What:		/sys/bus/auxiliary/devices/intel_vsec.sdsi.X/provision_akc
Date:		Feb 2022
KernelVersion:	5.18
Contact:	"David E. Box" <<EMAIL>>
Description:
		(WO) Used to write an Authentication Key Certificate (AKC) to
		the On Demand NVRAM for the CPU. The AKC is used to authenticate
		a Capability Activation Payload. Mailbox command.

What:		/sys/bus/auxiliary/devices/intel_vsec.sdsi.X/provision_cap
Date:		Feb 2022
KernelVersion:	5.18
Contact:	"David E. Box" <<EMAIL>>
Description:
		(WO) Used to write a Capability Activation Payload (CAP) to the
		On Demand NVRAM for the CPU. CAPs are used to activate a given
		CPU feature. A CAP is validated by On Demand hardware using a
		previously provisioned AKC file. Upon successful authentication,
		the CPU configuration is updated. A cold reboot is required to
		fully activate the feature. Mailbox command.

What:		/sys/bus/auxiliary/devices/intel_vsec.sdsi.X/meter_certificate
Date:		Nov 2022
KernelVersion:	6.2
Contact:	"David E. Box" <<EMAIL>>
Description:
		(RO) Used to read back the current meter certificate for the CPU
		from Intel On Demand hardware. The meter certificate contains
		utilization metrics of On Demand enabled features. Mailbox
		command.

What:		/sys/bus/auxiliary/devices/intel_vsec.sdsi.X/state_certificate
Date:		Feb 2022
KernelVersion:	5.18
Contact:	"David E. Box" <<EMAIL>>
Description:
		(RO) Used to read back the current state certificate for the CPU
		from On Demand hardware. The state certificate contains
		information about the current licenses on the CPU. Mailbox
		command.
