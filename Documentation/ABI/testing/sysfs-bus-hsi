What:		/sys/bus/hsi
Date:		April 2012
KernelVersion:	3.4
Contact:	<PERSON> <<EMAIL>>
Description:
		High Speed Synchronous Serial Interface (HSI) is a
		serial interface mainly used for connecting application
		engines (APE) with cellular modem engines (CMT) in cellular
		handsets.
		The bus will be populated with devices (hsi_clients) representing
		the protocols available in the system. Bus drivers implement
		those protocols.

What:		/sys/bus/hsi/devices/.../modalias
Date:		April 2012
KernelVersion:	3.4
Contact:	<PERSON> <<EMAIL>>
Description:	Stores the same MODALIAS value emitted by uevent
		Format: hsi:<hsi_client device name>
