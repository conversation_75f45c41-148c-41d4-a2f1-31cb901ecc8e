What:           /sys/bus/event_source/devices/hv_gpci/format
Date:           September 2020
Contact:        Linux on PowerPC Developer List <<EMAIL>>
Description:    Read-only. Attribute group to describe the magic bits
                that go into perf_event_attr.config for a particular pmu.
                (See ABI/testing/sysfs-bus-event_source-devices-format).

                Each attribute under this group defines a bit range of the
                perf_event_attr.config. All supported attributes are listed
                below::

				counter_info_version  = "config:16-23"
				length  = "config:24-31"
				partition_id  = "config:32-63"
				request = "config:0-31"
				sibling_part_id = "config:32-63"
				hw_chip_id = "config:32-63"
				offset = "config:32-63"
				phys_processor_idx = "config:32-63"
				secondary_index = "config:0-15"
				starting_index = "config:32-63"

                For example::

		  processor_core_utilization_instructions_completed = "request=0x94,
					phys_processor_idx=?,counter_info_version=0x8,
					length=8,offset=0x18"

		In this event, '?' after phys_processor_idx specifies this value
		this value will be provided by user while running this event.

What:		/sys/bus/event_source/devices/hv_gpci/interface/collect_privileged
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		'0' if the hypervisor is configured to forbid access to event
		counters being accumulated by other guests and to physical
		domain event counters.

		'1' if that access is allowed.

What:		/sys/bus/event_source/devices/hv_gpci/interface/ga
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		0 or 1. Indicates whether we have access to "GA" events (listed
		in arch/powerpc/perf/hv-gpci.h).

What:		/sys/bus/event_source/devices/hv_gpci/interface/expanded
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		0 or 1. Indicates whether we have access to "EXPANDED" events (listed
		in arch/powerpc/perf/hv-gpci.h).

What:		/sys/bus/event_source/devices/hv_gpci/interface/lab
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		0 or 1. Indicates whether we have access to "LAB" events (listed
		in arch/powerpc/perf/hv-gpci.h).

What:		/sys/bus/event_source/devices/hv_gpci/interface/version
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		A number indicating the version of the gpci interface that the
		hypervisor reports supporting.

What:		/sys/bus/event_source/devices/hv_gpci/interface/kernel_version
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		A number indicating the latest version of the gpci interface
		that the kernel is aware of.

What:		/sys/devices/hv_gpci/cpumask
Date:		October 2020
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	read only
		This sysfs file exposes the cpumask which is designated to make
		HCALLs to retrieve hv-gpci pmu event counter data.

What:		/sys/devices/hv_gpci/interface/processor_bus_topology
Date:		July 2023
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	admin read only
		This sysfs file exposes the system topology information by making HCALL
		H_GET_PERF_COUNTER_INFO. The HCALL is made with counter request value
		PROCESSOR_BUS_TOPOLOGY(0xD0).

		* This sysfs file will be created only for power10 and above platforms.

		* User needs root privileges to read data from this sysfs file.

		* This sysfs file will be created, only when the HCALL returns "H_SUCCESS",
		  "H_AUTHORITY" or "H_PARAMETER" as the return type.

		  HCALL with return error type "H_AUTHORITY" can be resolved during
		  runtime by setting "Enable Performance Information Collection" option.

		* The end user reading this sysfs file must decode the content as per
		  underlying platform/firmware.

		Possible error codes while reading this sysfs file:

		* "-EPERM" : Partition is not permitted to retrieve performance information,
			    required to set "Enable Performance Information Collection" option.

		* "-EIO" : Can't retrieve system information because of invalid buffer length/invalid address
			   or because of some hardware error. Refer to getPerfCountInfo documentation for
			   more information.

		* "-EFBIG" : System information exceeds PAGE_SIZE.

What:		/sys/devices/hv_gpci/interface/processor_config
Date:		July 2023
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	admin read only
		This sysfs file exposes the system topology information by making HCALL
		H_GET_PERF_COUNTER_INFO. The HCALL is made with counter request value
		PROCESSOR_CONFIG(0x90).

		* This sysfs file will be created only for power10 and above platforms.

		* User needs root privileges to read data from this sysfs file.

		* This sysfs file will be created, only when the HCALL returns "H_SUCCESS",
		  "H_AUTHORITY" or "H_PARAMETER" as the return type.

		  HCALL with return error type "H_AUTHORITY" can be resolved during
		  runtime by setting "Enable Performance Information Collection" option.

		* The end user reading this sysfs file must decode the content as per
		  underlying platform/firmware.

		Possible error codes while reading this sysfs file:

		* "-EPERM" : Partition is not permitted to retrieve performance information,
			    required to set "Enable Performance Information Collection" option.

		* "-EIO" : Can't retrieve system information because of invalid buffer length/invalid address
			   or because of some hardware error. Refer to getPerfCountInfo documentation for
			   more information.

		* "-EFBIG" : System information exceeds PAGE_SIZE.

What:		/sys/devices/hv_gpci/interface/affinity_domain_via_virtual_processor
Date:		July 2023
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	admin read only
		This sysfs file exposes the system topology information by making HCALL
		H_GET_PERF_COUNTER_INFO. The HCALL is made with counter request value
		AFFINITY_DOMAIN_INFORMATION_BY_VIRTUAL_PROCESSOR(0xA0).

		* This sysfs file will be created only for power10 and above platforms.

		* User needs root privileges to read data from this sysfs file.

		* This sysfs file will be created, only when the HCALL returns "H_SUCCESS",
		  "H_AUTHORITY" or "H_PARAMETER" as the return type.

		  HCALL with return error type "H_AUTHORITY" can be resolved during
		  runtime by setting "Enable Performance Information Collection" option.

		* The end user reading this sysfs file must decode the content as per
		  underlying platform/firmware.

		Possible error codes while reading this sysfs file:

		* "-EPERM" : Partition is not permitted to retrieve performance information,
			    required to set "Enable Performance Information Collection" option.

		* "-EIO" : Can't retrieve system information because of invalid buffer length/invalid address
			   or because of some hardware error. Refer to getPerfCountInfo documentation for
			   more information.

		* "-EFBIG" : System information exceeds PAGE_SIZE.

What:		/sys/devices/hv_gpci/interface/affinity_domain_via_domain
Date:		July 2023
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	admin read only
		This sysfs file exposes the system topology information by making HCALL
		H_GET_PERF_COUNTER_INFO. The HCALL is made with counter request value
		AFFINITY_DOMAIN_INFORMATION_BY_DOMAIN(0xB0).

		* This sysfs file will be created only for power10 and above platforms.

		* User needs root privileges to read data from this sysfs file.

		* This sysfs file will be created, only when the HCALL returns "H_SUCCESS",
		  "H_AUTHORITY" or "H_PARAMETER" as the return type.

		  HCALL with return error type "H_AUTHORITY" can be resolved during
		  runtime by setting "Enable Performance Information Collection" option.

		* The end user reading this sysfs file must decode the content as per
		  underlying platform/firmware.

		Possible error codes while reading this sysfs file:

		* "-EPERM" : Partition is not permitted to retrieve performance information,
			    required to set "Enable Performance Information Collection" option.

		* "-EIO" : Can't retrieve system information because of invalid buffer length/invalid address
			   or because of some hardware error. Refer to getPerfCountInfo documentation for
			   more information.

		* "-EFBIG" : System information exceeds PAGE_SIZE.

What:		/sys/devices/hv_gpci/interface/affinity_domain_via_partition
Date:		July 2023
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	admin read only
		This sysfs file exposes the system topology information by making HCALL
		H_GET_PERF_COUNTER_INFO. The HCALL is made with counter request value
		AFFINITY_DOMAIN_INFORMATION_BY_PARTITION(0xB1).

		* This sysfs file will be created only for power10 and above platforms.

		* User needs root privileges to read data from this sysfs file.

		* This sysfs file will be created, only when the HCALL returns "H_SUCCESS",
		  "H_AUTHORITY" or "H_PARAMETER" as the return type.

		  HCALL with return error type "H_AUTHORITY" can be resolved during
		  runtime by setting "Enable Performance Information Collection" option.

		* The end user reading this sysfs file must decode the content as per
		  underlying platform/firmware.

		Possible error codes while reading this sysfs file:

		* "-EPERM" : Partition is not permitted to retrieve performance information,
			    required to set "Enable Performance Information Collection" option.

		* "-EIO" : Can't retrieve system information because of invalid buffer length/invalid address
			   or because of some hardware error. Refer to getPerfCountInfo documentation for
			   more information.

		* "-EFBIG" : System information exceeds PAGE_SIZE.
