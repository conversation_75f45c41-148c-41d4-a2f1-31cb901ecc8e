What:		/sys/bus/i3c/devices/i3c-<bus-id>
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		An I3C bus. This directory will contain one sub-directory per
		I3C device present on the bus.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/current_master
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		Expose the master that owns the bus (<bus-id>-<master-pid>) at
		the time this file is read. Note that bus ownership can change
		overtime, so there's no guarantee that when the read() call
		returns, the value returned is still valid.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/mode
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		I3C bus mode. Can be "pure", "mixed-fast" or "mixed-slow". See
		the I3C specification for a detailed description of what each
		of these modes implies.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/i3c_scl_frequency
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		The frequency (expressed in Hz) of the SCL signal when
		operating in I3C SDR mode.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/i2c_scl_frequency
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		The frequency (expressed in Hz) of the SCL signal when
		operating in I2C mode.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/dynamic_address
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		Dynamic address assigned to the master controller. This
		address may change if the bus is re-initialized.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/bcr
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		BCR stands for Bus Characteristics Register and express the
		device capabilities in term of speed, maximum read/write
		length, etc. See the I3C specification for more details.
		This entry describes the BCR of the master controller driving
		the bus.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/dcr
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		DCR stands for Device Characteristics Register and express the
		device capabilities in term of exposed features. See the I3C
		specification for more details.
		This entry describes the DCR of the master controller driving
		the bus.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/pid
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		PID stands for Provisioned ID and is used to uniquely identify
		a device on a bus. This PID contains information about the
		vendor, the part and an instance ID so that several devices of
		the same type can be connected on the same bus.
		See the I3C specification for more details.
		This entry describes the PID of the master controller driving
		the bus.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/hdrcap
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		Expose the HDR (High Data Rate) capabilities of a device.
		Returns a list of supported HDR mode, each element is separated
		by space. Modes can be "hdr-ddr", "hdr-tsp" and "hdr-tsl".
		See the I3C specification for more details about these HDR
		modes.

		This entry describes the HDRCAP of the master controller
		driving the bus.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/hotjoin
KernelVersion:  6.8
Contact:	<EMAIL>
Description:
		I3C’s Hot-Join mechanism allows an I3C Device to inform the
		Active Controller that a newly-joined Target is present on the
		I3C Bus and is ready to receive a Dynamic Address, in order to
		become fully functional on the Bus. Hot-Join is used when the
		Target is mounted on the same I3C bus and remains depowered
		until needed or until the Target is physically inserted into the
		I3C bus

		This entry allows to enable or disable Hot-join of the Current
		Controller driving the bus.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/<bus-id>-<device-pid>
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		An I3C device present on I3C bus identified by <bus-id>. Note
		that all devices are represented including the master driving
		the bus.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/<bus-id>-<device-pid>/dynamic_address
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		Dynamic address assigned to device <bus-id>-<device-pid>. This
		address may change if the bus is re-initialized.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/<bus-id>-<device-pid>/bcr
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		BCR stands for Bus Characteristics Register and express the
		device capabilities in term of speed, maximum read/write
		length, etc. See the I3C specification for more details.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/<bus-id>-<device-pid>/dcr
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		DCR stands for Device Characteristics Register and express the
		device capabilities in term of exposed features. See the I3C
		specification for more details.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/<bus-id>-<device-pid>/pid
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		PID stands for Provisioned ID and is used to uniquely identify
		a device on a bus. This PID contains information about the
		vendor, the part and an instance ID so that several devices of
		the same type can be connected on the same bus.
		See the I3C specification for more details.

What:		/sys/bus/i3c/devices/i3c-<bus-id>/<bus-id>-<device-pid>/hdrcap
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		Expose the HDR (High Data Rate) capabilities of a device.
		Returns a list of supported HDR mode, each element is separated
		by space. Modes can be "hdr-ddr", "hdr-tsp" and "hdr-tsl".

		See the I3C specification for more details about these HDR
		modes.

What:		/sys/bus/i3c/devices/<bus-id>-<device-pid>
KernelVersion:  5.0
Contact:	<EMAIL>
Description:
		These directories are just symbolic links to
		/sys/bus/i3c/devices/i3c-<bus-id>/<bus-id>-<device-pid>.
