What:           /sys/bus/platform/devices/../fsi-master/fsi0/rescan
Date:		May 2017
KernelVersion:  4.12
Contact:        <EMAIL>
Description:
                Initiates a FSI master scan for all connected slave devices
		on its links.

What:           /sys/bus/platform/devices/../fsi-master/fsi0/break
Date:		May 2017
KernelVersion:  4.12
Contact:        <EMAIL>
Description:
		Sends an FSI BREAK command on a master's communication
		link to any connected slaves.  A BREAK resets connected
		device's logic and preps it to receive further commands
		from the master.

What:           /sys/bus/platform/devices/../fsi-master/fsi0/slave@00:00/term
Date:		May 2017
KernelVersion:  4.12
Contact:        <EMAIL>
Description:
		Sends an FSI terminate command from the master to its
		connected slave. A terminate resets the slave's state machines
		that control access to the internally connected engines.  In
		addition the slave freezes its internal error register for
		debugging purposes.  This command is also needed to abort any
		ongoing operation in case of an expired 'Master Time Out'
		timer.

What:           /sys/bus/platform/devices/../fsi-master/fsi0/slave@00:00/raw
Date:		May 2017
KernelVersion:  4.12
Contact:        <EMAIL>
Description:
		Provides a means of reading/writing a 32 bit value from/to a
		specified FSI bus address.

What:           /sys/bus/platform/devices/../cfam_reset
Date:		Sept 2020
KernelVersion:  5.10
Contact:        <EMAIL>
Description:
		Provides a means of resetting the cfam that is attached to the
		FSI device.
