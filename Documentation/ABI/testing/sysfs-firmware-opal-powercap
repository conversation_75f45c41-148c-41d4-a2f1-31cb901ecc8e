What:		/sys/firmware/opal/powercap
Date:		August 2017
Contact:	Linux for PowerPC mailing list <<EMAIL>>
Description:	Powercap directory for Powernv (P8, P9) servers

		Each folder in this directory contains a
		power-cappable component.

What:		/sys/firmware/opal/powercap/system-powercap
		/sys/firmware/opal/powercap/system-powercap/powercap-min
		/sys/firmware/opal/powercap/system-powercap/powercap-max
		/sys/firmware/opal/powercap/system-powercap/powercap-current
Date:		August 2017
Contact:	Linux for PowerPC mailing list <<EMAIL>>
Description:	System powercap directory and attributes applicable for
		Powernv (P8, P9) servers

		This directory provides powercap information. It
		contains below sysfs attributes:

		- powercap-min : This file provides the minimum
		  possible powercap in Watt units

		- powercap-max : This file provides the maximum
		  possible powercap in Watt units

		- powercap-current : This file provides the current
		  powercap set on the system. Writing to this file
		  creates a request for setting a new-powercap. The
		  powercap requested must be between powercap-min
		  and powercap-max.
