What:		/sys/class/rnbd-server
Date:		Feb 2020
KernelVersion:	5.7
Contact:	<PERSON> <<EMAIL>> <PERSON><PERSON> <<EMAIL>>
Description:	provide information about RNBD-server.

What:		/sys/class/rnbd-server/ctl/
Date:		Feb 2020
KernelVersion:	5.7
Contact:	<PERSON> <<EMAIL>> <PERSON><PERSON> <<EMAIL>>
Description:	When a client maps a device, a directory entry with the name of the
		block device is created under /sys/class/rnbd-server/ctl/devices/.

What:		/sys/class/rnbd-server/ctl/devices/<device_name>/block_dev
Date:		Feb 2020
KernelVersion:	5.7
Contact:	<PERSON> <<EMAIL>> <PERSON><PERSON> <<EMAIL>>
Description:	Is a symlink to the sysfs entry of the exported device.

		Example:
		block_dev -> ../../../../class/block/ram0

What:		/sys/class/rnbd-server/ctl/devices/<device_name>/sessions/
Date:		Feb 2020
KernelVersion:	5.7
Contact:	<PERSON> <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	For each client a particular device is exported to, following directory will be
		created:

		/sys/class/rnbd-server/ctl/devices/<device_name>/sessions/<session-name>/

		When the device is unmapped by that client, the directory will be removed.

What:		/sys/class/rnbd-server/ctl/devices/<device_name>/sessions/<session-name>/read_only
Date:		Feb 2020
KernelVersion:	5.7
Contact:	Jack Wang <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	Contains '1' if device is mapped read-only, otherwise '0'.

What:		/sys/class/rnbd-server/ctl/devices/<device_name>/sessions/<session-name>/mapping_path
Date:		Feb 2020
KernelVersion:	5.7
Contact:	Jack Wang <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	Contains the relative device path provided by the user during mapping.

What:		/sys/class/rnbd-server/ctl/devices/<device_name>/sessions/<session-name>/access_mode
Date:		Feb 2020
KernelVersion:	5.7
Contact:	Jack Wang <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	Contains the device access mode: ro, rw or migration.

What:		/sys/class/rnbd-server/ctl/devices/<device_name>/sessions/<session-name>/force_close
Date:		Nov 2020
KernelVersion:	5.10
Contact:	Jack Wang <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	Write "1" to the file to close the device on server side. Please
		note that the client side device will not be closed, read or
		write to the device will get -ENOTCONN.
