switchtec - Microsemi Switchtec PCI Switch Management Endpoint

For details on this subsystem look at Documentation/driver-api/switchtec.rst.

What: 		/sys/class/switchtec
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	<PERSON> <<EMAIL>>
Description:	The switchtec class subsystem folder.
		Each registered switchtec driver is represented by a switchtecX
		subfolder (X being an integer >= 0).


What:		/sys/class/switchtec/switchtec[0-9]+/component_id
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	<PERSON> <<EMAIL>>
Description:	Component identifier as stored in the hardware (eg. PM8543)
		(read only)
Values: 	arbitrary string.


What:		/sys/class/switchtec/switchtec[0-9]+/component_revision
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	<PERSON> <<EMAIL>>
Description:	Component revision stored in the hardware (read only)
Values: 	integer.


What:		/sys/class/switchtec/switchtec[0-9]+/component_vendor
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	<PERSON> <<EMAIL>>
Description:	Component vendor as stored in the hardware (eg. MICROSEM)
		(read only)
Values: 	arbitrary string.


What:		/sys/class/switchtec/switchtec[0-9]+/device_version
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	Logan Gunthorpe <<EMAIL>>
Description:	Device version as stored in the hardware (read only)
Values: 	integer.


What:		/sys/class/switchtec/switchtec[0-9]+/fw_version
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	Logan Gunthorpe <<EMAIL>>
Description:	Currently running firmware version (read only)
Values: 	integer (in hexadecimal).


What:		/sys/class/switchtec/switchtec[0-9]+/partition
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	Logan Gunthorpe <<EMAIL>>
Description:	Partition number for this device in the switch (read only)
Values: 	integer.


What:		/sys/class/switchtec/switchtec[0-9]+/partition_count
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	Logan Gunthorpe <<EMAIL>>
Description:	Total number of partitions in the switch (read only)
Values: 	integer.


What:		/sys/class/switchtec/switchtec[0-9]+/product_id
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	Logan Gunthorpe <<EMAIL>>
Description:	Product identifier as stored in the hardware (eg. PSX 48XG3)
		(read only)
Values: 	arbitrary string.


What:		/sys/class/switchtec/switchtec[0-9]+/product_revision
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	Logan Gunthorpe <<EMAIL>>
Description:	Product revision stored in the hardware (eg. RevB)
		(read only)
Values: 	arbitrary string.


What:		/sys/class/switchtec/switchtec[0-9]+/product_vendor
Date:		05-Jan-2017
KernelVersion:	v4.11
Contact:	Logan Gunthorpe <<EMAIL>>
Description:	Product vendor as stored in the hardware (eg. MICROSEM)
		(read only)
Values: 	arbitrary string.
