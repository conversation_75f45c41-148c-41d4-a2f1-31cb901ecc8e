What:           /sys/bus/pci/drivers/pciback/quirks
Date:           Oct 2011
KernelVersion:  3.1
Contact:        <EMAIL>
Description:
                If the permissive attribute is set, then writing a string in
                the format of DDDD:BB:DD.F-REG:SIZE:MASK will allow the guest
                to write and read from the PCI device. That is Domain:Bus:
                Device.Function-Register:Size:Mask (Domain is optional).
                For example::

                  #echo 00:19.0-E0:2:FF > /sys/bus/pci/drivers/pciback/quirks

                will allow the guest to read and write to the configuration
                register 0x0E.

What:           /sys/bus/pci/drivers/pciback/allow_interrupt_control
Date:           Jan 2020
KernelVersion:  5.6
Contact:        <EMAIL>
Description:
                List of devices which can have interrupt control flag (INTx,
                MSI, MSI-X) set by a connected guest. It is meant to be set
                only when the guest is a stubdomain hosting device model (qemu)
                and the actual device is assigned to a HVM. It is not safe
                (similar to permissive attribute) to set for a devices assigned
                to a PV guest. The device is automatically removed from this
                list when the connected pcifront terminates.
