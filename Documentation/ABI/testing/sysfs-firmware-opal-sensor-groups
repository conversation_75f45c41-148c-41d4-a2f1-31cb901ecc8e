What:		/sys/firmware/opal/sensor_groups
Date:		August 2017
Contact:	Linux for PowerPC mailing list <<EMAIL>>
Description:	Sensor groups directory for POWER9 powernv servers

		Each folder in this directory contains a sensor group
		which are classified based on type of the sensor
		like power, temperature, frequency, current, etc. They
		can also indicate the group of sensors belonging to
		different owners like CSM, Profiler, Job-Scheduler

What:		/sys/firmware/opal/sensor_groups/<sensor_group_name>/clear
Date:		August 2017
Contact:	Linux for PowerPC mailing list <<EMAIL>>
Description:	Sysfs file to clear the min-max of all the sensors
		belonging to the group.

		Writing 1 to this file will clear the minimum and
		maximum values of all the sensors in the group.
		In POWER9, the min-max of a sensor is the historical minimum
		and maximum value of the sensor cached by OCC.
