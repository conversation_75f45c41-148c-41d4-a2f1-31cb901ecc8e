// SPDX-License-Identifier: GPL-2.0 WITH Linux-syscall-note
/*
 *
 * (C) COPYRIGHT 2017-2024 ARM Limited. All rights reserved.
 *
 * This program is free software and is provided to you under the terms of the
 * GNU General Public License version 2 as published by the Free Software
 * Foundation, and any use by you of this program is subject to the terms
 * of such GNU license.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you can access it online at
 * http://www.gnu.org/licenses/gpl-2.0.html.
 *
 */

What:		/sys/bus/coresight/devices/mali-source-etm/enable_source
Description:
        Attribute used to enable Coresight Source ETM.

What:		/sys/bus/coresight/devices/mali-source-etm/is_enabled
Description:
        Attribute used to check if Coresight Source ETM is enabled.

What:		/sys/bus/coresight/devices/mali-source-etm/trcconfigr
Description:
        Coresight Source ETM trace configuration to enable global
        timestamping, and data value tracing.

What:		/sys/bus/coresight/devices/mali-source-etm/trctraceidr
Description:
        Coresight Source ETM trace ID.

What:		/sys/bus/coresight/devices/mali-source-etm/trcvdarcctlr
Description:
        Coresight Source ETM viewData include/exclude address
        range comparators.

What:		/sys/bus/coresight/devices/mali-source-etm/trcviiectlr
Description:
        Coresight Source ETM viewInst include and exclude control.

What:		/sys/bus/coresight/devices/mali-source-etm/trcstallctlr
Description:
        Coresight Source ETM stall control register.

What:		/sys/bus/coresight/devices/mali-source-itm/enable_source
Description:
        Attribute used to enable Coresight Source ITM.

What:		/sys/bus/coresight/devices/mali-source-itm/is_enabled
Description:
        Attribute used to check if Coresight Source ITM is enabled.

What:		/sys/bus/coresight/devices/mali-source-itm/dwt_ctrl
Description:
        Coresight Source DWT configuration:
            [0] = 1, enable cycle counter
            [4:1] = 4, set PC sample rate pf 256 cycles
            [8:5] = 1, set initial post count value
            [9] = 1, select position of post count tap on the cycle counter
            [10:11] = 1, enable sync packets
            [12] = 1, enable periodic PC sample packets

What:		/sys/bus/coresight/devices/mali-source-itm/itm_tcr
Description:
        Coresight Source ITM configuration:
            [0] = 1, Enable ITM
            [1] = 1, Enable Time stamp generation
            [2] = 1, Enable sync packet transmission
            [3] = 1, Enable HW event forwarding
            [11:10] = 1, Generate TS request approx every 128 cycles
            [22:16] = 1, Trace bus ID

What:		/sys/bus/coresight/devices/mali-source-ela/reset_regs
Description:
        Attribute used to reset registers to zero.

What:		/sys/bus/coresight/devices/mali-source-ela/enable_source
Description:
        Attribute used to enable Coresight Source ELA.

What:		/sys/bus/coresight/devices/mali-source-ela/is_enabled
Description:
        Attribute used to check if Coresight Source ELA is enabled.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/TIMECTRL
Description:
        Coresight Source ELA TIMECTRL register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/TSSR
Description:
        Coresight Source ELA TSR register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/ATBCTRL
Description:
        Coresight Source ELA ATBCTRL register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/PTACTION
Description:
        Coresight Source ELA PTACTION register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/AUXCTRL
Description:
        Coresight Source ELA AUXCTRL register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/CNTSEL
Description:
        Coresight Source ELA CNTSEL register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/SIGSELn
Description:
        Coresight Source ELA SIGSELn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/TRIGCTRLn
Description:
        Coresight Source ELA TRIGCTRLn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/NEXTSTATEn
Description:
        Coresight Source ELA NEXTSTATEn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/ACTIONn
Description:
        Coresight Source ELA ACTIONn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/ALTNEXTSTATEn
Description:
        Coresight Source ELA ALTNEXTSTATEn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/ALTACTIONn
Description:
        Coresight Source ELA ALTACTIONn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/COMPCTRLn
Description:
        Coresight Source ELA COMPCTRLn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/ALTCOMPCTRLn
Description:
        Coresight Source ELA ALTCOMPCTRLn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/COUNTCOMPn
Description:
        Coresight Source ELA COUNTCOMPn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/TWBSELn
Description:
        Coresight Source ELA TWBSELn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/EXTMASKn
Description:
        Coresight Source ELA EXTMASKn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/EXTCOMPn
Description:
        Coresight Source ELA EXTCOMPn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/QUALMASKn
Description:
        Coresight Source ELA QUALMASKn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/QUALCOMPn
        Coresight Source ELA QUALCOMPn register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/SIGMASKn_0-7
Description:
        Coresight Source ELA SIGMASKn_0-7 register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/SIGCOMPn_0-7
Description:
        Coresight Source ELA SIGCOMPn_0-7 register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/SIGSELn_0-7
Description:
        Coresight Source ELA SIGSELn_0-7 register set/get.
        Refer to specification for more details.

What:		/sys/bus/coresight/devices/mali-source-ela/regs/SIGMASKn_0-7
Description:
        Coresight Source ELA SIGMASKn_0-7 register set/get.
        Refer to specification for more details.
