What:		/sys/bus/spi/<dev>/update_firmware
Date:		Jul 2021
Contact:	<EMAIL>
Description:	Write 1 to this file to update the ACHC microcontroller
		firmware via the EzPort interface. For this the kernel
		will load "achc.bin" via the firmware API (so usually
		from /lib/firmware). The write will block until the FW
		has either been flashed successfully or an error occurred.

What:		/sys/bus/spi/<dev>/reset
Date:		Jul 2021
Contact:	<EMAIL>
Description:	This file represents the microcontroller's reset line.
                1 means the reset line is asserted, 0 means it's not
		asserted. The file is read and writable.
