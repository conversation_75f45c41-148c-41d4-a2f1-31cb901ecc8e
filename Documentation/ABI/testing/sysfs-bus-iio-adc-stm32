What:		/sys/bus/iio/devices/triggerX/trigger_polarity
KernelVersion:	4.11
Contact:	<EMAIL>
Description:
		The STM32 ADC can be configured to use external trigger sources
		(e.g. timers, pwm or exti gpio). Then, it can be tuned to start
		conversions on external trigger by either:

		- "rising-edge"
		- "falling-edge"
		- "both-edges".

		Reading returns current trigger polarity.

		Writing value before enabling conversions sets trigger polarity.

What:		/sys/bus/iio/devices/triggerX/trigger_polarity_available
KernelVersion:	4.11
Contact:	<EMAIL>
Description:
		List all available trigger_polarity settings.
