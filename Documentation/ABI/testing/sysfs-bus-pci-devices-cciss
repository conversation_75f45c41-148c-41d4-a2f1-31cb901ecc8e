What:		/sys/bus/pci/devices/<dev>/ccissX/cXdY/model
Date:		March 2009
KernelVersion: 2.6.30
Contact:	<EMAIL>
Description:	Displays the SCSI INQUIRY page 0 model for logical drive
		Y of controller X.

What:		/sys/bus/pci/devices/<dev>/ccissX/cXdY/rev
Date:		March 2009
KernelVersion: 2.6.30
Contact:	<EMAIL>
Description:	Displays the SCSI INQUIRY page 0 revision for logical
		drive Y of controller X.

What:		/sys/bus/pci/devices/<dev>/ccissX/cXdY/unique_id
Date:		March 2009
KernelVersion: 2.6.30
Contact:	<EMAIL>
Description:	Displays the SCSI INQUIRY page 83 serial number for logical
		drive Y of controller X.

What:		/sys/bus/pci/devices/<dev>/ccissX/cXdY/vendor
Date:		March 2009
KernelVersion: 2.6.30
Contact:	<EMAIL>
Description:	Displays the SCSI INQUIRY page 0 vendor for logical drive
		Y of controller X.

What:		/sys/bus/pci/devices/<dev>/ccissX/cXdY/block:cciss!cXdY
Date:		March 2009
KernelVersion: 2.6.30
Contact:	<EMAIL>
Description:	A symbolic link to /sys/block/cciss!cXdY

What:		/sys/bus/pci/devices/<dev>/ccissX/rescan
Date:		August 2009
KernelVersion:	2.6.31
Contact:	<EMAIL>
Description:	Kicks of a rescan of the controller to discover logical
		drive topology changes.

What:		/sys/bus/pci/devices/<dev>/ccissX/cXdY/lunid
Date:		August 2009
KernelVersion: 2.6.31
Contact:	<EMAIL>
Description:	Displays the 8-byte LUN ID used to address logical
		drive Y of controller X.

What:		/sys/bus/pci/devices/<dev>/ccissX/cXdY/raid_level
Date:		August 2009
KernelVersion: 2.6.31
Contact:	<EMAIL>
Description:	Displays the RAID level of logical drive Y of
		controller X.

What:		/sys/bus/pci/devices/<dev>/ccissX/cXdY/usage_count
Date:		August 2009
KernelVersion: 2.6.31
Contact:	<EMAIL>
Description:	Displays the usage count (number of opens) of logical drive Y
		of controller X.

What:		/sys/bus/pci/devices/<dev>/ccissX/resettable
Date:		February 2011
KernelVersion:	2.6.38
Contact:	<EMAIL>
Description:	Value of 1 indicates the controller can honor the reset_devices
		kernel parameter.  Value of 0 indicates reset_devices cannot be
		honored.  This is to allow, for example, kexec tools to be able
		to warn the user if they designate an unresettable device as
		a dump device, as kdump requires resetting the device in order
		to work reliably.

What:		/sys/bus/pci/devices/<dev>/ccissX/transport_mode
Date:		July 2011
KernelVersion:	3.0
Contact:	<EMAIL>
Description:	Value of "simple" indicates that the controller has been placed
		in "simple mode". Value of "performant" indicates that the
		controller has been placed in "performant mode".
