What:		/sys/bus/iio/devices/iio:deviceX/in_allow_async_readout
Date:		December 2015
KernelVersion:	4.4
Contact:	<EMAIL>
Description:
		By default (value '0'), the capture thread checks for the Conversion
		Ready Flag to being set prior to committing a new value to the sample
		buffer. This synchronizes the in-chip conversion rate with the
		in-driver readout rate at the cost of an additional register read.

		Writing '1' will remove the polling for the Conversion Ready Flags to
		save the additional i2c transaction, which will improve the bandwidth
		available for reading data. However, samples can be occasionally skipped
		or repeated, depending on the beat between the capture and conversion
		rates.
