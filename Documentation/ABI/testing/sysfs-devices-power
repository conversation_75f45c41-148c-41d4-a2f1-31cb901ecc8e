What:		/sys/devices/.../power/
Date:		January 2009
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/devices/.../power directory contains attributes
		allowing the user space to check and modify some power
		management related properties of given device.

What:		/sys/devices/.../power/wakeup
Date:		January 2009
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/devices/.../power/wakeup attribute allows the user
		space to check if the device is enabled to wake up the system
		from sleep states, such as the memory sleep state (suspend to
		RAM) and hibernation (suspend to disk), and to enable or disable
		it to do that as desired.

		Some devices support "wakeup" events, which are hardware signals
		used to activate the system from a sleep state.  Such devices
		have one of the following two values for the sysfs power/wakeup
		file:

		+ "enabled\n" to issue the events;
		+ "disabled\n" not to do so;

		In that cases the user space can change the setting represented
		by the contents of this file by writing either "enabled", or
		"disabled" to it.

		For the devices that are not capable of generating system wakeup
		events this file is not present.  In that case the device cannot
		be enabled to wake up the system from sleep states.

What:		/sys/devices/.../power/control
Date:		January 2009
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/devices/.../power/control attribute allows the user
		space to control the run-time power management of the device.

		All devices have one of the following two values for the
		power/control file:

		+ "auto\n" to allow the device to be power managed at run time;
		+ "on\n" to prevent the device from being power managed;

		The default for all devices is "auto", which means that they may
		be subject to automatic power management, depending on their
		drivers.  Changing this attribute to "on" prevents the driver
		from power managing the device at run time.  Doing that while
		the device is suspended causes it to be woken up.

What:		/sys/devices/.../power/async
Date:		January 2009
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../async attribute allows the user space to
		enable or diasble the device's suspend and resume callbacks to
		be executed asynchronously (ie. in separate threads, in parallel
		with the main suspend/resume thread) during system-wide power
		transitions (eg. suspend to RAM, hibernation).

		All devices have one of the following two values for the
		power/async file:

		+ "enabled\n" to permit the asynchronous suspend/resume;
		+ "disabled\n" to forbid it;

		The value of this attribute may be changed by writing either
		"enabled", or "disabled" to it.

		It generally is unsafe to permit the asynchronous suspend/resume
		of a device unless it is certain that all of the PM dependencies
		of the device are known to the PM core.  However, for some
		devices this attribute is set to "enabled" by bus type code or
		device drivers and in that cases it should be safe to leave the
		default value.

What:		/sys/devices/.../power/wakeup_count
Date:		September 2010
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_count attribute contains the number
		of signaled wakeup events associated with the device.  This
		attribute is read-only.  If the device is not capable to wake up
		the system from sleep states, this attribute is not present.
		If the device is not enabled to wake up the system from sleep
		states, this attribute is empty.

What:		/sys/devices/.../power/wakeup_active_count
Date:		September 2010
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_active_count attribute contains the
		number of times the processing of wakeup events associated with
		the device was completed (at the kernel level).  This attribute
		is read-only.  If the device is not capable to wake up the
		system from sleep states, this attribute is not present.  If
		the device is not enabled to wake up the system from sleep
		states, this attribute is empty.

What:		/sys/devices/.../power/wakeup_abort_count
Date:		February 2012
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_abort_count attribute contains the
		number of times the processing of a wakeup event associated with
		the device might have aborted system transition into a sleep
		state in progress.  This attribute is read-only.  If the device
		is not capable to wake up the system from sleep states, this
		attribute is not present.  If the device is not enabled to wake
		up the system from sleep states, this attribute is empty.

What:		/sys/devices/.../power/wakeup_expire_count
Date:		February 2012
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_expire_count attribute contains the
		number of times a wakeup event associated with the device has
		been reported with a timeout that expired.  This attribute is
		read-only.  If the device is not capable to wake up the system
		from sleep states, this attribute is not present.  If the
		device is not enabled to wake up the system from sleep states,
		this attribute is empty.

What:		/sys/devices/.../power/wakeup_active
Date:		September 2010
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_active attribute contains either 1,
		or 0, depending on whether or not a wakeup event associated with
		the device is being processed (1).  This attribute is read-only.
		If the device is not capable to wake up the system from sleep
		states, this attribute is not present.  If the device is not
		enabled to wake up the system from sleep states, this attribute
		is empty.

What:		/sys/devices/.../power/wakeup_total_time_ms
Date:		September 2010
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_total_time_ms attribute contains
		the total time of processing wakeup events associated with the
		device, in milliseconds.  This attribute is read-only.  If the
		device is not capable to wake up the system from sleep states,
		this attribute is not present.  If the device is not enabled to
		wake up the system from sleep states, this attribute is empty.

What:		/sys/devices/.../power/wakeup_max_time_ms
Date:		September 2010
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_max_time_ms attribute contains
		the maximum time of processing a single wakeup event associated
		with the device, in milliseconds.  This attribute is read-only.
		If the device is not capable to wake up the system from sleep
		states, this attribute is not present.  If the device is not
		enabled to wake up the system from sleep states, this attribute
		is empty.

What:		/sys/devices/.../power/wakeup_last_time_ms
Date:		September 2010
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_last_time_ms attribute contains
		the value of the monotonic clock corresponding to the time of
		signaling the last wakeup event associated with the device, in
		milliseconds.  This attribute is read-only.  If the device is
		not enabled to wake up the system from sleep states, this
		attribute is not present.  If the device is not enabled to wake
		up the system from sleep states, this attribute is empty.

What:		/sys/devices/.../power/wakeup_prevent_sleep_time_ms
Date:		February 2012
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../wakeup_prevent_sleep_time_ms attribute
		contains the total time the device has been preventing
		opportunistic transitions to sleep states from occurring.
		This attribute is read-only.  If the device is not capable to
		wake up the system from sleep states, this attribute is not
		present.  If the device is not enabled to wake up the system
		from sleep states, this attribute is empty.

What:		/sys/devices/.../power/autosuspend_delay_ms
Date:		September 2010
Contact:	Alan Stern <<EMAIL>>
Description:
		The /sys/devices/.../power/autosuspend_delay_ms attribute
		contains the autosuspend delay value (in milliseconds).  Some
		drivers do not want their device to suspend as soon as it
		becomes idle at run time; they want the device to remain
		inactive for a certain minimum period of time first.  That
		period is called the autosuspend delay.  Negative values will
		prevent the device from being suspended at run time (similar
		to writing "on" to the power/control attribute).  Values >=
		1000 will cause the autosuspend timer expiration to be rounded
		up to the nearest second.

		Not all drivers support this attribute.  If it isn't supported,
		attempts to read or write it will yield I/O errors.

What:		/sys/devices/.../power/pm_qos_resume_latency_us
Date:		March 2012
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../power/pm_qos_resume_latency_us attribute
		contains the PM QoS resume latency limit for the given device,
		which is the maximum allowed time it can take to resume the
		device, after it has been suspended at run time, from a resume
		request to the moment the device will be ready to process I/O,
		in microseconds.  If it is equal to 0, however, this means that
		the PM QoS resume latency may be arbitrary and the special value
		"n/a" means that user space cannot accept any resume latency at
		all for the given device.

		Not all drivers support this attribute.  If it isn't supported,
		it is not present.

		This attribute has no effect on system-wide suspend/resume and
		hibernation.

What:		/sys/devices/.../power/pm_qos_latency_tolerance_us
Date:		January 2014
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../power/pm_qos_latency_tolerance_us attribute
		contains the PM QoS active state latency tolerance limit for the
		given device in microseconds.  That is the maximum memory access
		latency the device can suffer without any visible adverse
		effects on user space functionality.  If that value is the
		string "any", the latency does not matter to user space at all,
		but hardware should not be allowed to set the latency tolerance
		for the device automatically.

		Reading "auto" from this file means that the maximum memory
		access latency for the device may be determined automatically
		by the hardware as needed.  Writing "auto" to it allows the
		hardware to be switched to this mode if there are no other
		latency tolerance requirements from the kernel side.

		This attribute is only present if the feature controlled by it
		is supported by the hardware.

		This attribute has no effect on runtime suspend and resume of
		devices and on system-wide suspend/resume and hibernation.

What:		/sys/devices/.../power/pm_qos_no_power_off
Date:		September 2012
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../power/pm_qos_no_power_off attribute
		is used for manipulating the PM QoS "no power off" flag.  If
		set, this flag indicates to the kernel that power should not
		be removed entirely from the device.

		Not all drivers support this attribute.  If it isn't supported,
		it is not present.

		This attribute has no effect on system-wide suspend/resume and
		hibernation.

What:		/sys/devices/.../power/runtime_status
Date:		April 2010
Contact:	Rafael J. Wysocki <<EMAIL>>
Description:
		The /sys/devices/.../power/runtime_status attribute contains
		the current runtime PM status of the device, which may be
		"suspended", "suspending", "resuming", "active", "error" (fatal
		error), or "unsupported" (runtime PM is disabled).

What:		/sys/devices/.../power/runtime_active_time
Date:		Jul 2010
Contact:	Arjan van de Ven <<EMAIL>>
Description:
		Reports the total time that the device has been active.
		Used for runtime PM statistics.

What:		/sys/devices/.../power/runtime_suspended_time
Date:		Jul 2010
Contact:	Arjan van de Ven <<EMAIL>>
Description:
		Reports total time that the device has been suspended.
		Used for runtime PM statistics.

What:		/sys/devices/.../power/runtime_usage
Date:		Apr 2010
Contact:	Dominik Brodowski <<EMAIL>>
Description:
		Reports the runtime PM usage count of a device.

What:		/sys/devices/.../power/runtime_enabled
Date:		Apr 2010
Contact:	Dominik Brodowski <<EMAIL>>
Description:
		Is runtime PM enabled for this device?
		States are "enabled", "disabled", "forbidden" or a
		combination of the latter two.

What:		/sys/devices/.../power/runtime_active_kids
Date:		Apr 2010
Contact:	Dominik Brodowski <<EMAIL>>
Description:
		Reports the runtime PM children usage count of a device, or
		0 if the children will be ignored.

