What:		/sys/class/wakeup/
Date:		June 2019
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/class/wakeup/ directory contains pointers to all
		wakeup sources in the kernel at that moment in time.

What:		/sys/class/wakeup/.../name
Date:		June 2019
Contact:	<PERSON> <<EMAIL>>
Description:
		This file contains the name of the wakeup source.

What:		/sys/class/wakeup/.../active_count
Date:		June 2019
Contact:	Tri Vo <<EMAIL>>
Description:
		This file contains the number of times the wakeup source was
		activated.

What:		/sys/class/wakeup/.../event_count
Date:		June 2019
Contact:	Tri Vo <<EMAIL>>
Description:
		This file contains the number of signaled wakeup events
		associated with the wakeup source.

What:		/sys/class/wakeup/.../wakeup_count
Date:		June 2019
Contact:	Tri Vo <<EMAIL>>
Description:
		This file contains the number of times the wakeup source might
		abort suspend.

What:		/sys/class/wakeup/.../expire_count
Date:		June 2019
Contact:	<PERSON> <<EMAIL>>
Description:
		This file contains the number of times the wakeup source's
		timeout has expired.

What:		/sys/class/wakeup/.../active_time_ms
Date:		June 2019
Contact:	<PERSON> <<EMAIL>>
Description:
		This file contains the amount of time the wakeup source has
		been continuously active, in milliseconds.  If the wakeup
		source is not active, this file contains '0'.

What:		/sys/class/wakeup/.../total_time_ms
Date:		June 2019
Contact:	Tri Vo <<EMAIL>>
Description:
		This file contains the total amount of time this wakeup source
		has been active, in milliseconds.

What:		/sys/class/wakeup/.../max_time_ms
Date:		June 2019
Contact:	Tri Vo <<EMAIL>>
Description:
		This file contains the maximum amount of time this wakeup
		source has been continuously active, in milliseconds.

What:		/sys/class/wakeup/.../last_change_ms
Date:		June 2019
Contact:	Tri Vo <<EMAIL>>
Description:
		This file contains the monotonic clock time when the wakeup
		source was touched last time, in milliseconds.

What:		/sys/class/wakeup/.../prevent_suspend_time_ms
Date:		June 2019
Contact:	Tri Vo <<EMAIL>>
Description:
		The file contains the total amount of time this wakeup source
		has been preventing autosleep, in milliseconds.
