What:		/sys/bus/platform/drivers/zynqmp_fpga_manager/firmware:zynqmp-firmware:pcap/status
Date:		February 2023
KernelVersion:	6.4
Contact:	Nava kish<PERSON> <<EMAIL>>
Description:	(RO) Read fpga status.
		Read returns a hexadecimal value that tells the current status
		of the FPGA device. Each bit position in the status value is
		described Below(see ug570 chapter 9).
		https://docs.xilinx.com/v/u/en-US/ug570-ultrascale-configuration

		======================  ==============================================
		BIT(0)			0: No CRC error
					1: CRC error

		BIT(1)			0: Decryptor security not set
					1: Decryptor security set

		BIT(2)			0: MMCMs/PLLs are not locked
					1: MMCMs/PLLs are locked

		BIT(3)			0: DCI not matched
					1: DCI matched

		BIT(4)			0: Start-up sequence has not finished
					1: Start-up sequence has finished

		BIT(5)			0: All I/Os are placed in High-Z state
					1: All I/Os behave as configured

		BIT(6)			0: Flip-flops and block RAM are write disabled
					1: Flip-flops and block RAM are write enabled

		BIT(7)			0: GHIGH_B_STATUS asserted
					1: GHIGH_B_STATUS deasserted

		BIT(8) to BIT(10)	Status of the mode pins

		BIT(11)			0: Initialization has not finished
					1: Initialization finished

		BIT(12)			Value on INIT_B_PIN pin

		BIT(13)			0: Signal not released
					1: Signal released

		BIT(14)			Value on DONE_PIN pin.

		BIT(15)			0: No IDCODE_ERROR
					1: IDCODE_ERROR

		BIT(16)			0: No SECURITY_ERROR
					1: SECURITY_ERROR

		BIT(17)			System Monitor over-temperature if set

		BIT(18) to BIT(20)	Start-up state machine (0 to 7)
					Phase 0 = 000
					Phase 1 = 001
					Phase 2 = 011
					Phase 3 = 010
					Phase 4 = 110
					Phase 5 = 111
					Phase 6 = 101
					Phase 7 = 100

		BIT(25) to BIT(26)	Indicates the detected bus width
					00 = x1
					01 = x8
					10 = x16
					11 = x32
		======================  ==============================================

		The other bits are reserved.
