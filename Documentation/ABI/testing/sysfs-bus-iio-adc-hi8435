What:		/sys/bus/iio/devices/iio:deviceX/in_voltageY_sensing_mode
Date:		August 2015
KernelVersion:	4.2.0
Contact:	<EMAIL>
Description:
		Program sensor type for threshold detector inputs.
		Could be either "GND-Open" or "Supply-Open" mode. Y is a
		threshold detector input channel. Channels 0..7, 8..15, 16..23
		and 24..31 has common sensor types.

What:		/sys/bus/iio/devices/iio:deviceX/events/in_voltageY_thresh_falling_value
Date:		August 2015
KernelVersion:	4.2.0
Contact:	<EMAIL>
Description:
		Channel Y low voltage threshold. If sensor input voltage goes lower then
		this value then the threshold falling event is pushed.
		Depending on in_voltageY_sensing_mode the low voltage threshold
		is separately set for "GND-Open" and "Supply-Open" modes.
		Channels 0..31 have common low threshold values, but could have different
		sensing_modes.

		The low voltage threshold range is between 2..21V.
		Hysteresis between low and high thresholds can not be lower then 2 and
		can not be odd.

		If falling threshold results hysteresis to odd value then rising
		threshold is automatically subtracted by one.

What:		/sys/bus/iio/devices/iio:deviceX/events/in_voltageY_thresh_rising_value
Date:		August 2015
KernelVersion:	4.2.0
Contact:	<EMAIL>
Description:
		Channel Y high voltage threshold. If sensor input voltage goes higher then
		this value then the threshold rising event is pushed.
		Depending on in_voltageY_sensing_mode the high voltage threshold
		is separately set for "GND-Open" and "Supply-Open" modes.

		Channels 0..31 have common high threshold values, but could have different
		sensing_modes.

		The high voltage threshold range is between 3..22V.
		Hysteresis between low and high thresholds can not be lower then 2 and
		can not be odd.

		If rising threshold results hysteresis to odd value then falling
		threshold is automatically appended by one.
