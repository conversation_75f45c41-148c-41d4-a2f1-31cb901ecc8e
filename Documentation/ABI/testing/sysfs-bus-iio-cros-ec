What:		/sys/bus/iio/devices/iio:deviceX/calibrate
Date:		July 2015
KernelVersion:	4.7
Contact:	<EMAIL>
Description:
		Writing '1' will perform a FOC (Fast Online Calibration). The
                corresponding calibration offsets can be read from `*_calibbias`
                entries.

What:		/sys/bus/iio/devices/iio:deviceX/location
Date:		July 2015
KernelVersion:	4.7
Contact:	<EMAIL>
Description:
		This attribute returns a string with the physical location where
                the motion sensor is placed. For example, in a laptop a motion
                sensor can be located on the base or on the lid. Current valid
		values are 'base' and 'lid'.

What:		/sys/bus/iio/devices/iio:deviceX/id
Date:		September 2017
KernelVersion:	4.14
Contact:	<EMAIL>
Description:
		This attribute is exposed by the CrOS EC sensors driver and
		represents the sensor ID as exposed by the EC. This ID is used
		by the Android sensor service hardware abstraction layer (sensor
		HAL) through the Android container on ChromeOS.
