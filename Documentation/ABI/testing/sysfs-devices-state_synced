What:		/sys/devices/.../state_synced
Date:		May 2020
Contact:	<PERSON><PERSON> <sa<PERSON><PERSON><PERSON>@google.com>
Description:
		The /sys/devices/.../state_synced attribute is only present for
		devices whose bus types or driver provides the .sync_state()
		callback. The number read from it (0 or 1) reflects the value
		of the device's 'state_synced' field. A value of 0 means the
		.sync_state() callback hasn't been called yet. A value of 1
		means the .sync_state() callback has been called.

		Generally, if a device has sync_state() support and has some of
		the resources it provides enabled at the time the kernel starts
		(Eg: enabled by hardware reset or bootloader or anything that
		run before the kernel starts), then it'll keep those resources
		enabled and in a state that's compatible with the state they
		were in at the start of the kernel. The device will stop doing
		this only when the sync_state() callback has been called --
		which happens only when all its consumer devices are registered
		and have probed successfully. Resources that were left disabled
		at the time the kernel starts are not affected or limited in
		any way by sync_state() callbacks.

		Writing "1" to this file will force a call to the device's
		sync_state() function if it hasn't been called already. The
		sync_state() call happens independent of the state of the
		consumer devices.


