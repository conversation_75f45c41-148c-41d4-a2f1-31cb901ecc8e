What:		/sys/firmware/lefi/boardinfo
Date:		October 2020
Contact:	<PERSON><PERSON><PERSON><PERSON> <yang<PERSON><PERSON><PERSON>@loongson.cn>
Description:
		Get mainboard and BIOS info easily on the Loongson platform,
		this is useful to point out the current used mainboard type
		and BIOS version when there exists problems related with
		hardware or firmware.

		The related structures are already defined in the interface
		specification about firmware and kernel which are common
		requirement and specific for Loongson64, so only add a new
		boardinfo.c file in arch/mips/loongson64.

		For example:

		[loongson@linux ~]$ cat /sys/firmware/lefi/boardinfo
		Board Info
		Manufacturer            : LEMOTE
		Board Name              : LEMOTE-LS3A4000-7A1000-1w-V01-pc
		Family                  : LOONGSON3

		BIOS Info
		Vendor                  : Kunlun
		Version                 : Kunlun-A1901-V4.1.3-20200414093938
		ROM Size                : 4 KB
		Release Date            : 2020-04-14

		By the way, using dmidecode command can get the similar info if there
		exists SMBIOS in firmware, but the fact is that there is no SMBIOS on
		some machines, we can see nothing when execute dmidecode, like this:

		[root@linux loongson]# dmidecode
		# dmidecode 2.12
		# No SMBIOS nor DMI entry point found, sorry.
