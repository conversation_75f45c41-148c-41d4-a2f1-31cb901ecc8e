What:		/sys/bus/pci/drivers/xhci_hcd/.../dbc
Date:		June 2017
Contact:	<PERSON> <<EMAIL>>
Description:
		xHCI compatible USB host controllers (i.e. super-speed
		USB3 controllers) are often implemented with the Debug
		Capability (DbC). It can present a debug device which
		is fully compliant with the USB framework and provides
		the equivalent of a very high performance full-duplex
		serial link for debug purpose.

		The DbC debug device shares a root port with xHCI host.
		When the DbC is enabled, the root port will be assigned
		to the Debug Capability. Otherwise, it will be assigned
		to xHCI.

		Writing "enable" to this attribute will enable the DbC
		functionality and the shared root port will be assigned
		to the DbC device. Writing "disable" to this attribute
		will disable the DbC functionality and the shared root
		port will roll back to the xHCI.

		Reading this attribute gives the state of the DbC. It
		can be one of the following states: disabled, enabled,
		initialized, connected, configured and stalled.

What:		/sys/bus/pci/drivers/xhci_hcd/.../dbc_idVendor
Date:		March 2023
Contact:	<PERSON> <<EMAIL>>
Description:
		This dbc_idVendor attribute lets us change the idVendor field
		presented in the USB device descriptor by this xhci debug
		device.
		Value can only be changed while debug capability (DbC) is in
		disabled state to prevent USB device descriptor change while
		connected to a USB host.
		The default value is 0x1d6b (Linux Foundation).
		It can be any 16-bit integer.

What:		/sys/bus/pci/drivers/xhci_hcd/.../dbc_idProduct
Date:		March 2023
Contact:	Mathias Nyman <<EMAIL>>
Description:
		This dbc_idProduct attribute lets us change the idProduct field
		presented in the USB device descriptor by this xhci debug
		device.
		Value can only be changed while debug capability (DbC) is in
		disabled state to prevent USB device descriptor change while
		connected to a USB host.
		The default value is 0x0010. It can be any 16-bit integer.

What:		/sys/bus/pci/drivers/xhci_hcd/.../dbc_bcdDevice
Date:		March 2023
Contact:	Mathias Nyman <<EMAIL>>
Description:
		This dbc_bcdDevice attribute lets us change the bcdDevice field
		presented in the USB device descriptor by this xhci debug
		device.
		Value can only be changed while debug capability (DbC) is in
		disabled state to prevent USB device descriptor change while
		connected to a USB host.
		The default value is 0x0010. (device rev 0.10)
		It can be any 16-bit integer.

What:		/sys/bus/pci/drivers/xhci_hcd/.../dbc_bInterfaceProtocol
Date:		March 2023
Contact:	Mathias Nyman <<EMAIL>>
Description:
		This attribute lets us change the bInterfaceProtocol field
		presented in the USB Interface descriptor by the xhci debug
		device.
		Value can only be changed while debug capability (DbC) is in
		disabled state to prevent USB descriptor change while
		connected to a USB host.
		The default value is 1  (GNU Remote Debug command).
		Other permissible value is 0 which is for vendor defined debug
		target.

What:		/sys/bus/pci/drivers/xhci_hcd/.../dbc_poll_interval_ms
Date:		February 2024
Contact:	Mathias Nyman <<EMAIL>>
Description:
		This attribute adjust the polling interval used to check for
		DbC events. Unit is milliseconds. Accepted values range from 0
		up to 5000. The default value is 64 ms.
		This polling interval is used while DbC is enabled but has no
		active data transfers.
