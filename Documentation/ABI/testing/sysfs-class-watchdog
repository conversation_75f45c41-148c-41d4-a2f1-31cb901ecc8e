What:		/sys/class/watchdog/watchdogn/bootstatus
Date:		August 2015
Contact:	Wim <PERSON> <<EMAIL>>
Description:
		It is a read only file. It contains status of the watchdog
		device at boot. It is equivalent to WDIOC_GETBOOTSTATUS of
		ioctl interface.

What:		/sys/class/watchdog/watchdogn/options
Date:		April 2023
Contact:	<PERSON>
Description:
		It is a read only file. It contains options of watchdog device.

What:		/sys/class/watchdog/watchdogn/fw_version
Date:		April 2023
Contact:	<PERSON>
Description:
		It is a read only file. It contains firmware version of
		watchdog device.

What:		/sys/class/watchdog/watchdogn/identity
Date:		August 2015
Contact:	Wim <PERSON> <<EMAIL>>
Description:
		It is a read only file. It contains identity string of
		watchdog device.

What:		/sys/class/watchdog/watchdogn/nowayout
Date:		August 2015
Contact:	Wim <PERSON> <<EMAIL>>
Description:
		It is a read/write file. While reading, it gives '1'
		if the device has the nowayout feature set, otherwise
		it gives '0'. Writing a '1' to the file enables the
		nowayout feature. Once set, the nowayout feature
		cannot be disabled, so writing a '0' either has no
		effect (if the feature was already disabled) or
		results in a permission error.

What:		/sys/class/watchdog/watchdogn/state
Date:		August 2015
Contact:	Wim Van Sebroeck <<EMAIL>>
Description:
		It is a read only file. It gives active/inactive status of
		watchdog device.

What:		/sys/class/watchdog/watchdogn/status
Date:		August 2015
Contact:	Wim Van Sebroeck <<EMAIL>>
Description:
		It is a read only file. It contains watchdog device's
		internal status bits. It is equivalent to WDIOC_GETSTATUS
		of ioctl interface.

What:		/sys/class/watchdog/watchdogn/timeleft
Date:		August 2015
Contact:	Wim Van Sebroeck <<EMAIL>>
Description:
		It is a read only file. It contains value of time left for
		reset generation. It is equivalent to WDIOC_GETTIMELEFT of
		ioctl interface.

What:		/sys/class/watchdog/watchdogn/timeout
Date:		August 2015
Contact:	Wim Van Sebroeck <<EMAIL>>
Description:
		It is a read only file. It is read to know about current
		value of timeout programmed.

What:		/sys/class/watchdog/watchdogn/pretimeout
Date:		December 2016
Contact:	Wim Van Sebroeck <<EMAIL>>
Description:
		It is a read only file. It specifies the time in seconds before
		timeout when the pretimeout interrupt is delivered.  Pretimeout
		is an optional feature.

What:		/sys/class/watchdog/watchdogn/pretimeout_avaialable_governors
Date:		February 2017
Contact:	Wim Van Sebroeck <<EMAIL>>
Description:
		It is a read only file. It shows the pretimeout governors
		available for this watchdog.

What:		/sys/class/watchdog/watchdogn/pretimeout_governor
Date:		February 2017
Contact:	Wim Van Sebroeck <<EMAIL>>
Description:
		It is a read/write file. When read, the currently assigned
		pretimeout governor is returned.  When written, it sets
		the pretimeout governor.

What:		/sys/class/watchdog/watchdog1/access_cs0
Date:		August 2019
Contact:	Ivan Mikhaylov <<EMAIL>>,
		Alexander Amelkin <<EMAIL>>
Description:
		It is a read/write file. This attribute exists only if the
		system has booted from the alternate flash chip due to
		expiration of a watchdog timer of AST2400/AST2500 when
		alternate boot function was enabled with 'aspeed,alt-boot'
		devicetree option for that watchdog or with an appropriate
		h/w strapping (for WDT2 only).

		At alternate flash the 'access_cs0' sysfs node provides:

			ast2400:
				a way to get access to the primary SPI flash
				chip at CS0 after booting from the alternate
				chip at CS1.
			ast2500:
				a way to restore the normal address mapping
				from (CS0->CS1, CS1->CS0) to (CS0->CS0,
				CS1->CS1).

		Clearing the boot code selection and timeout counter also
		resets to the initial state the chip select line mapping. When
		the SoC is in normal mapping state (i.e. booted from CS0),
		clearing those bits does nothing for both versions of the SoC.
		For alternate boot mode (booted from CS1 due to wdt2
		expiration) the behavior differs as described above.

		This option can be used with wdt2 (watchdog1) only.

		When read, the current status of the boot code selection is
		shown. When written with any non-zero value, it clears
		the boot code selection and the timeout counter, which results
		in chipselect reset for AST2400/AST2500.
