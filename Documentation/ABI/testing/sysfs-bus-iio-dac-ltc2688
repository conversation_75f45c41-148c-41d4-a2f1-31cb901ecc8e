What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_dither_en
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Dither enable. Write 1 to enable dither or 0 to disable it. This is useful
		for changing the dither parameters. They way it should be done is:

		- disable dither operation;
		- change dither parameters (eg: frequency, phase...);
		- enabled dither operation

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_dither_raw
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		This raw, unscaled value refers to the dither signal amplitude.
		The same scale as in out_voltageY_raw applies. However, the
		offset might be different as it's always 0 for this attribute.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_dither_raw_available
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Available range for dither raw amplitude values.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_dither_offset
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Offset applied to out_voltageY_dither_raw. Read only attribute
		always set to 0.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_dither_frequency
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Sets the dither signal frequency. Units are in Hz.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_dither_frequency_available
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Returns the available values for the dither frequency.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_dither_phase
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Sets the dither signal phase. Units are in Radians.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_dither_phase_available
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Returns the available values for the dither phase.
