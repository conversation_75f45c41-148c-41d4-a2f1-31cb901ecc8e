What:		/sys/bus/dax/devices/daxX.Y/align
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(RW) Provides a way to specify an alignment for a dax device.
		Values allowed are constrained by the physical address ranges
		that back the dax device, and also by arch requirements.

What:		/sys/bus/dax/devices/daxX.Y/mapping
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(WO) Provides a way to allocate a mapping range under a dax
		device. Specified in the format <start>-<end>.

What:		/sys/bus/dax/devices/daxX.Y/mapping[0..N]/start
What:		/sys/bus/dax/devices/daxX.Y/mapping[0..N]/end
What:		/sys/bus/dax/devices/daxX.Y/mapping[0..N]/page_offset
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(RO) A dax device may have multiple constituent discontiguous
		address ranges. These are represented by the different
		'mappingX' subdirectories. The 'start' attribute indicates the
		start physical address for the given range. The 'end' attribute
		indicates the end physical address for the given range. The
		'page_offset' attribute indicates the offset of the current
		range in the dax device.

What:		/sys/bus/dax/devices/daxX.Y/resource
Date:		June, 2019
KernelVersion:	v5.3
Contact:	<EMAIL>
Description:
		(RO) The resource attribute indicates the starting physical
		address of a dax device. In case of a device with multiple
		constituent ranges, it indicates the starting address of the
		first range.

What:		/sys/bus/dax/devices/daxX.Y/size
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(RW) The size attribute indicates the total size of a dax
		device. For creating subdivided dax devices, or for resizing
		an existing device, the new size can be written to this as
		part of the reconfiguration process.

What:		/sys/bus/dax/devices/daxX.Y/numa_node
Date:		November, 2019
KernelVersion:	v5.5
Contact:	<EMAIL>
Description:
		(RO) If NUMA is enabled and the platform has affinitized the
		backing device for this dax device, emit the CPU node
		affinity for this device.

What:		/sys/bus/dax/devices/daxX.Y/target_node
Date:		February, 2019
KernelVersion:	v5.1
Contact:	<EMAIL>
Description:
		(RO) The target-node attribute is the Linux numa-node that a
		device-dax instance may create when it is online. Prior to
		being online the device's 'numa_node' property reflects the
		closest online cpu node which is the typical expectation of a
		device 'numa_node'. Once it is online it becomes its own
		distinct numa node.

What:		$(readlink -f /sys/bus/dax/devices/daxX.Y)/../dax_region/available_size
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(RO) The available_size attribute tracks available dax region
		capacity. This only applies to volatile hmem devices, not pmem
		devices, since pmem devices are defined by nvdimm namespace
		boundaries.

What:		$(readlink -f /sys/bus/dax/devices/daxX.Y)/../dax_region/size
Date:		July, 2017
KernelVersion:	v5.1
Contact:	<EMAIL>
Description:
		(RO) The size attribute indicates the size of a given dax region
		in bytes.

What:		$(readlink -f /sys/bus/dax/devices/daxX.Y)/../dax_region/align
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(RO) The align attribute indicates alignment of the dax region.
		Changes on align may not always be valid, when say certain
		mappings were created with 2M and then we switch to 1G. This
		validates all ranges against the new value being attempted, post
		resizing.

What:		$(readlink -f /sys/bus/dax/devices/daxX.Y)/../dax_region/seed
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(RO) The seed device is a concept for dynamic dax regions to be
		able to split the region amongst multiple sub-instances.  The
		seed device, similar to libnvdimm seed devices, is a device
		that starts with zero capacity allocated and unbound to a
		driver.

What:		$(readlink -f /sys/bus/dax/devices/daxX.Y)/../dax_region/create
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(RW) The create interface to the dax region provides a way to
		create a new unconfigured dax device under the given region, which
		can then be configured (with a size etc.) and then probed.

What:		$(readlink -f /sys/bus/dax/devices/daxX.Y)/../dax_region/delete
Date:		October, 2020
KernelVersion:	v5.10
Contact:	<EMAIL>
Description:
		(WO) The delete interface for a dax region provides for deletion
		of any 0-sized and idle dax devices.

What:		$(readlink -f /sys/bus/dax/devices/daxX.Y)/../dax_region/id
Date:		July, 2017
KernelVersion:	v5.1
Contact:	<EMAIL>
Description:
		(RO) The id attribute indicates the region id of a dax region.

What:		/sys/bus/dax/devices/daxX.Y/memmap_on_memory
Date:		January, 2024
KernelVersion:	v6.8
Contact:	<EMAIL>
Description:
		(RW) Control the memmap_on_memory setting if the dax device
		were to be hotplugged as system memory. This determines whether
		the 'altmap' for the hotplugged memory will be placed on the
		device being hotplugged (memmap_on_memory=1) or if it will be
		placed on regular memory (memmap_on_memory=0). This attribute
		must be set before the device is handed over to the 'kmem'
		driver (i.e.  hotplugged into system-ram). Additionally, this
		depends on CONFIG_MHP_MEMMAP_ON_MEMORY, and a globally enabled
		memmap_on_memory parameter for memory_hotplug. This is
		typically set on the kernel command line -
		memory_hotplug.memmap_on_memory set to 'true' or 'force'."
