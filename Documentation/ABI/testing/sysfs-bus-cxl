What:		/sys/bus/cxl/flush
Date:		Januarry, 2022
KernelVersion:	v5.18
Contact:	<EMAIL>
Description:
		(WO) If userspace manually unbinds a port the kernel schedules
		all descendant memdevs for unbind. Writing '1' to this attribute
		flushes that work.


What:		/sys/bus/cxl/devices/memX/firmware_version
Date:		December, 2020
KernelVersion:	v5.12
Contact:	<EMAIL>
Description:
		(RO) "FW Revision" string as reported by the Identify
		Memory Device Output Payload in the CXL-2.0
		specification.


What:		/sys/bus/cxl/devices/memX/ram/size
Date:		December, 2020
KernelVersion:	v5.12
Contact:	<EMAIL>
Description:
		(RO) "Volatile Only Capacity" as bytes. Represents the
		identically named field in the Identify Memory Device Output
		Payload in the CXL-2.0 specification.


What:		/sys/bus/cxl/devices/memX/ram/qos_class
Date:		May, 2023
KernelVersion:	v6.8
Contact:	<EMAIL>
Description:
		(RO) For CXL host platforms that support "QoS Telemmetry"
		this attribute conveys a comma delimited list of platform
		specific cookies that identifies a QoS performance class
		for the volatile partition of the CXL mem device. These
		class-ids can be compared against a similar "qos_class"
		published for a root decoder. While it is not required
		that the endpoints map their local memory-class to a
		matching platform class, mismatches are not recommended
		and there are platform specific performance related
		side-effects that may result. First class-id is displayed.


What:		/sys/bus/cxl/devices/memX/pmem/size
Date:		December, 2020
KernelVersion:	v5.12
Contact:	<EMAIL>
Description:
		(RO) "Persistent Only Capacity" as bytes. Represents the
		identically named field in the Identify Memory Device Output
		Payload in the CXL-2.0 specification.


What:		/sys/bus/cxl/devices/memX/pmem/qos_class
Date:		May, 2023
KernelVersion:	v6.8
Contact:	<EMAIL>
Description:
		(RO) For CXL host platforms that support "QoS Telemmetry"
		this attribute conveys a comma delimited list of platform
		specific cookies that identifies a QoS performance class
		for the persistent partition of the CXL mem device. These
		class-ids can be compared against a similar "qos_class"
		published for a root decoder. While it is not required
		that the endpoints map their local memory-class to a
		matching platform class, mismatches are not recommended
		and there are platform specific performance related
		side-effects that may result. First class-id is displayed.


What:		/sys/bus/cxl/devices/memX/serial
Date:		January, 2022
KernelVersion:	v5.18
Contact:	<EMAIL>
Description:
		(RO) 64-bit serial number per the PCIe Device Serial Number
		capability. Mandatory for CXL devices, see CXL 2.0 ********
		Memory Device PCIe Capabilities and Extended Capabilities.


What:		/sys/bus/cxl/devices/memX/numa_node
Date:		January, 2022
KernelVersion:	v5.18
Contact:	<EMAIL>
Description:
		(RO) If NUMA is enabled and the platform has affinitized the
		host PCI device for this memory device, emit the CPU node
		affinity for this device.


What:		/sys/bus/cxl/devices/memX/security/state
Date:		June, 2023
KernelVersion:	v6.5
Contact:	<EMAIL>
Description:
		(RO) Reading this file will display the CXL security state for
		that device. Such states can be: 'disabled', 'sanitize', when
		a sanitization is currently underway; or those available only
		for persistent memory: 'locked', 'unlocked' or 'frozen'. This
		sysfs entry is select/poll capable from userspace to notify
		upon completion of a sanitize operation.


What:           /sys/bus/cxl/devices/memX/security/sanitize
Date:           June, 2023
KernelVersion:  v6.5
Contact:        <EMAIL>
Description:
		(WO) Write a boolean 'true' string value to this attribute to
		sanitize the device to securely re-purpose or decommission it.
		This is done by ensuring that all user data and meta-data,
		whether it resides in persistent capacity, volatile capacity,
		or the LSA, is made permanently unavailable by whatever means
		is appropriate for the media type. This functionality requires
		the device to be disabled, that is, not actively decoding any
		HPA ranges. This permits avoiding explicit global CPU cache
		management, relying instead for it to be done when a region
		transitions between software programmed and hardware committed
		states. If this file is not present, then there is no hardware
		support for the operation.


What            /sys/bus/cxl/devices/memX/security/erase
Date:           June, 2023
KernelVersion:  v6.5
Contact:        <EMAIL>
Description:
		(WO) Write a boolean 'true' string value to this attribute to
		secure erase user data by changing the media encryption keys for
		all user data areas of the device. This functionality requires
		the device to be disabled, that is, not actively decoding any
		HPA ranges. This permits avoiding explicit global CPU cache
		management, relying instead for it to be done when a region
		transitions between software programmed and hardware committed
		states. If this file is not present, then there is no hardware
		support for the operation.


What:		/sys/bus/cxl/devices/memX/firmware/
Date:		April, 2023
KernelVersion:	v6.5
Contact:	<EMAIL>
Description:
		(RW) Firmware uploader mechanism. The different files under
		this directory can be used to upload and activate new
		firmware for CXL devices. The interfaces under this are
		documented in sysfs-class-firmware.


What:		/sys/bus/cxl/devices/*/devtype
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) CXL device objects export the devtype attribute which
		mirrors the same value communicated in the DEVTYPE environment
		variable for uevents for devices on the "cxl" bus.


What:		/sys/bus/cxl/devices/*/modalias
Date:		December, 2021
KernelVersion:	v5.18
Contact:	<EMAIL>
Description:
		(RO) CXL device objects export the modalias attribute which
		mirrors the same value communicated in the MODALIAS environment
		variable for uevents for devices on the "cxl" bus.


What:		/sys/bus/cxl/devices/portX/uport
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) CXL port objects are enumerated from either a platform
		firmware device (ACPI0017 and ACPI0016) or PCIe switch upstream
		port with CXL component registers. The 'uport' symlink connects
		the CXL portX object to the device that published the CXL port
		capability.


What:		/sys/bus/cxl/devices/{port,endpoint}X/parent_dport
Date:		January, 2023
KernelVersion:	v6.3
Contact:	<EMAIL>
Description:
		(RO) CXL port objects are instantiated for each upstream port in
		a CXL/PCIe switch, and for each endpoint to map the
		corresponding memory device into the CXL port hierarchy. When a
		descendant CXL port (switch or endpoint) is enumerated it is
		useful to know which 'dport' object in the parent CXL port
		routes to this descendant. The 'parent_dport' symlink points to
		the device representing the downstream port of a CXL switch that
		routes to {port,endpoint}X.


What:		/sys/bus/cxl/devices/portX/dportY
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) CXL port objects are enumerated from either a platform
		firmware device (ACPI0017 and ACPI0016) or PCIe switch upstream
		port with CXL component registers. The 'dportY' symlink
		identifies one or more downstream ports that the upstream port
		may target in its decode of CXL memory resources.  The 'Y'
		integer reflects the hardware port unique-id used in the
		hardware decoder target list.


What:		/sys/bus/cxl/devices/portX/decoders_committed
Date:		October, 2023
KernelVersion:	v6.7
Contact:	<EMAIL>
Description:
		(RO) A memory device is considered active when any of its
		decoders are in the "committed" state (See CXL 3.0 ********.7
		CXL HDM Decoder n Control Register). Hotplug and destructive
		operations like "sanitize" are blocked while device is actively
		decoding a Host Physical Address range. Note that this number
		may be elevated without any regionX objects active or even
		enumerated, as this may be due to decoders established by
		platform firwmare or a previous kernel (kexec).


What:		/sys/bus/cxl/devices/decoderX.Y
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) CXL decoder objects are enumerated from either a platform
		firmware description, or a CXL HDM decoder register set in a
		PCIe device (see CXL 2.0 section ******** CXL HDM Decoder
		Capability Structure). The 'X' in decoderX.Y represents the
		cxl_port container of this decoder, and 'Y' represents the
		instance id of a given decoder resource.


What:		/sys/bus/cxl/devices/decoderX.Y/{start,size}
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) The 'start' and 'size' attributes together convey the
		physical address base and number of bytes mapped in the
		decoder's decode window. For decoders of devtype
		"cxl_decoder_root" the address range is fixed. For decoders of
		devtype "cxl_decoder_switch" the address is bounded by the
		decode range of the cxl_port ancestor of the decoder's cxl_port,
		and dynamically updates based on the active memory regions in
		that address space.


What:		/sys/bus/cxl/devices/decoderX.Y/locked
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) CXL HDM decoders have the capability to lock the
		configuration until the next device reset. For decoders of
		devtype "cxl_decoder_root" there is no standard facility to
		unlock them.  For decoders of devtype "cxl_decoder_switch" a
		secondary bus reset, of the PCIe bridge that provides the bus
		for this decoders uport, unlocks / resets the decoder.


What:		/sys/bus/cxl/devices/decoderX.Y/target_list
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) Display a comma separated list of the current decoder
		target configuration. The list is ordered by the current
		configured interleave order of the decoder's dport instances.
		Each entry in the list is a dport id.


What:		/sys/bus/cxl/devices/decoderX.Y/cap_{pmem,ram,type2,type3}
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) When a CXL decoder is of devtype "cxl_decoder_root", it
		represents a fixed memory window identified by platform
		firmware. A fixed window may only support a subset of memory
		types. The 'cap_*' attributes indicate whether persistent
		memory, volatile memory, accelerator memory, and / or expander
		memory may be mapped behind this decoder's memory window.


What:		/sys/bus/cxl/devices/decoderX.Y/target_type
Date:		June, 2021
KernelVersion:	v5.14
Contact:	<EMAIL>
Description:
		(RO) When a CXL decoder is of devtype "cxl_decoder_switch", it
		can optionally decode either accelerator memory (type-2) or
		expander memory (type-3). The 'target_type' attribute indicates
		the current setting which may dynamically change based on what
		memory regions are activated in this decode hierarchy.


What:		/sys/bus/cxl/devices/endpointX/CDAT
Date:		July, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RO) If this sysfs entry is not present no DOE mailbox was
		found to support CDAT data.  If it is present and the length of
		the data is 0 reading the CDAT data failed.  Otherwise the CDAT
		data is reported.


What:		/sys/bus/cxl/devices/decoderX.Y/mode
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RW) When a CXL decoder is of devtype "cxl_decoder_endpoint" it
		translates from a host physical address range, to a device local
		address range. Device-local address ranges are further split
		into a 'ram' (volatile memory) range and 'pmem' (persistent
		memory) range. The 'mode' attribute emits one of 'ram', 'pmem',
		'mixed', or 'none'. The 'mixed' indication is for error cases
		when a decoder straddles the volatile/persistent partition
		boundary, and 'none' indicates the decoder is not actively
		decoding, or no DPA allocation policy has been set.

		'mode' can be written, when the decoder is in the 'disabled'
		state, with either 'ram' or 'pmem' to set the boundaries for the
		next allocation.


What:		/sys/bus/cxl/devices/decoderX.Y/dpa_resource
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RO) When a CXL decoder is of devtype "cxl_decoder_endpoint",
		and its 'dpa_size' attribute is non-zero, this attribute
		indicates the device physical address (DPA) base address of the
		allocation.


What:		/sys/bus/cxl/devices/decoderX.Y/dpa_size
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RW) When a CXL decoder is of devtype "cxl_decoder_endpoint" it
		translates from a host physical address range, to a device local
		address range. The range, base address plus length in bytes, of
		DPA allocated to this decoder is conveyed in these 2 attributes.
		Allocations can be mutated as long as the decoder is in the
		disabled state. A write to 'dpa_size' releases the previous DPA
		allocation and then attempts to allocate from the free capacity
		in the device partition referred to by 'decoderX.Y/mode'.
		Allocate and free requests can only be performed on the highest
		instance number disabled decoder with non-zero size. I.e.
		allocations are enforced to occur in increasing 'decoderX.Y/id'
		order and frees are enforced to occur in decreasing
		'decoderX.Y/id' order.


What:		/sys/bus/cxl/devices/decoderX.Y/interleave_ways
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RO) The number of targets across which this decoder's host
		physical address (HPA) memory range is interleaved. The device
		maps every Nth block of HPA (of size ==
		'interleave_granularity') to consecutive DPA addresses. The
		decoder's position in the interleave is determined by the
		device's (endpoint or switch) switch ancestry. For root
		decoders their interleave is specified by platform firmware and
		they only specify a downstream target order for host bridges.


What:		/sys/bus/cxl/devices/decoderX.Y/interleave_granularity
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RO) The number of consecutive bytes of host physical address
		space this decoder claims at address N before the decode rotates
		to the next target in the interleave at address N +
		interleave_granularity (assuming N is aligned to
		interleave_granularity).


What:		/sys/bus/cxl/devices/decoderX.Y/create_{pmem,ram}_region
Date:		May, 2022, January, 2023
KernelVersion:	v6.0 (pmem), v6.3 (ram)
Contact:	<EMAIL>
Description:
		(RW) Write a string in the form 'regionZ' to start the process
		of defining a new persistent, or volatile memory region
		(interleave-set) within the decode range bounded by root decoder
		'decoderX.Y'. The value written must match the current value
		returned from reading this attribute. An atomic compare exchange
		operation is done on write to assign the requested id to a
		region and allocate the region-id for the next creation attempt.
		EBUSY is returned if the region name written does not match the
		current cached value.


What:		/sys/bus/cxl/devices/decoderX.Y/delete_region
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(WO) Write a string in the form 'regionZ' to delete that region,
		provided it is currently idle / not bound to a driver.


What:		/sys/bus/cxl/devices/decoderX.Y/qos_class
Date:		May, 2023
KernelVersion:	v6.5
Contact:	<EMAIL>
Description:
		(RO) For CXL host platforms that support "QoS Telemmetry" this
		root-decoder-only attribute conveys a platform specific cookie
		that identifies a QoS performance class for the CXL Window.
		This class-id can be compared against a similar "qos_class"
		published for each memory-type that an endpoint supports. While
		it is not required that endpoints map their local memory-class
		to a matching platform class, mismatches are not recommended and
		there are platform specific side-effects that may result.


What:		/sys/bus/cxl/devices/regionZ/uuid
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RW) Write a unique identifier for the region. This field must
		be set for persistent regions and it must not conflict with the
		UUID of another region. For volatile ram regions this
		attribute is a read-only empty string.


What:		/sys/bus/cxl/devices/regionZ/interleave_granularity
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RW) Set the number of consecutive bytes each device in the
		interleave set will claim. The possible interleave granularity
		values are determined by the CXL spec and the participating
		devices.


What:		/sys/bus/cxl/devices/regionZ/interleave_ways
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RW) Configures the number of devices participating in the
		region is set by writing this value. Each device will provide
		1/interleave_ways of storage for the region.


What:		/sys/bus/cxl/devices/regionZ/size
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RW) System physical address space to be consumed by the region.
		When written trigger the driver to allocate space out of the
		parent root decoder's address space. When read the size of the
		address space is reported and should match the span of the
		region's resource attribute. Size shall be set after the
		interleave configuration parameters. Once set it cannot be
		changed, only freed by writing 0. The kernel makes no guarantees
		that data is maintained over an address space freeing event, and
		there is no guarantee that a free followed by an allocate
		results in the same address being allocated.


What:		/sys/bus/cxl/devices/regionZ/mode
Date:		January, 2023
KernelVersion:	v6.3
Contact:	<EMAIL>
Description:
		(RO) The mode of a region is established at region creation time
		and dictates the mode of the endpoint decoder that comprise the
		region. For more details on the possible modes see
		/sys/bus/cxl/devices/decoderX.Y/mode


What:		/sys/bus/cxl/devices/regionZ/resource
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RO) A region is a contiguous partition of a CXL root decoder
		address space. Region capacity is allocated by writing to the
		size attribute, the resulting physical address space determined
		by the driver is reflected here. It is therefore not useful to
		read this before writing a value to the size attribute.


What:		/sys/bus/cxl/devices/regionZ/target[0..N]
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RW) Write an endpoint decoder object name to 'targetX' where X
		is the intended position of the endpoint device in the region
		interleave and N is the 'interleave_ways' setting for the
		region. ENXIO is returned if the write results in an impossible
		to map decode scenario, like the endpoint is unreachable at that
		position relative to the root decoder interleave. EBUSY is
		returned if the position in the region is already occupied, or
		if the region is not in a state to accept interleave
		configuration changes. EINVAL is returned if the object name is
		not an endpoint decoder. Once all positions have been
		successfully written a final validation for decode conflicts is
		performed before activating the region.


What:		/sys/bus/cxl/devices/regionZ/commit
Date:		May, 2022
KernelVersion:	v6.0
Contact:	<EMAIL>
Description:
		(RW) Write a boolean 'true' string value to this attribute to
		trigger the region to transition from the software programmed
		state to the actively decoding in hardware state. The commit
		operation in addition to validating that the region is in proper
		configured state, validates that the decoders are being
		committed in spec mandated order (last committed decoder id +
		1), and checks that the hardware accepts the commit request.
		Reading this value indicates whether the region is committed or
		not.


What:		/sys/bus/cxl/devices/memX/trigger_poison_list
Date:		April, 2023
KernelVersion:	v6.4
Contact:	<EMAIL>
Description:
		(WO) When a boolean 'true' is written to this attribute the
		memdev driver retrieves the poison list from the device. The
		list consists of addresses that are poisoned, or would result
		in poison if accessed, and the source of the poison. This
		attribute is only visible for devices supporting the
		capability. The retrieved errors are logged as kernel
		events when cxl_poison event tracing is enabled.


What:		/sys/bus/cxl/devices/regionZ/accessY/read_bandwidth
		/sys/bus/cxl/devices/regionZ/accessY/write_banwidth
Date:		Jan, 2024
KernelVersion:	v6.9
Contact:	<EMAIL>
Description:
		(RO) The aggregated read or write bandwidth of the region. The
		number is the accumulated read or write bandwidth of all CXL memory
		devices that contributes to the region in MB/s. It is
		identical data that should appear in
		/sys/devices/system/node/nodeX/accessY/initiators/read_bandwidth or
		/sys/devices/system/node/nodeX/accessY/initiators/write_bandwidth.
		See Documentation/ABI/stable/sysfs-devices-node. access0 provides
		the number to the closest initiator and access1 provides the
		number to the closest CPU.


What:		/sys/bus/cxl/devices/regionZ/accessY/read_latency
		/sys/bus/cxl/devices/regionZ/accessY/write_latency
Date:		Jan, 2024
KernelVersion:	v6.9
Contact:	<EMAIL>
Description:
		(RO) The read or write latency of the region. The number is
		the worst read or write latency of all CXL memory devices that
		contributes to the region in nanoseconds. It is identical data
		that should appear in
		/sys/devices/system/node/nodeX/accessY/initiators/read_latency or
		/sys/devices/system/node/nodeX/accessY/initiators/write_latency.
		See Documentation/ABI/stable/sysfs-devices-node. access0 provides
		the number to the closest initiator and access1 provides the
		number to the closest CPU.
