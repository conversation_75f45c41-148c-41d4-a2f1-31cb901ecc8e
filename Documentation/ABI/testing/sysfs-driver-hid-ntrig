What:		/sys/bus/hid/drivers/ntrig/<dev>/activate_slack
Date:		May, 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		(RW) Number of contact frames ignored before acknowledging the
		start of activity (activating touch).


What:		/sys/bus/hid/drivers/ntrig/<dev>/decativate_slack
Date:		May, 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		(RW) Number of empty (no contact) frames ignored before
		acknowledging the end of activity (deactivating touch).

		When the last finger is removed from the device, it sends a
		number of empty frames. By holding off on deactivation for a few
		frames false erroneous disconnects can be tolerated, where the
		sensor may mistakenly not detect a finger that is still present.


What:		/sys/bus/hid/drivers/ntrig/<dev>/activation_width
What:		/sys/bus/hid/drivers/ntrig/<dev>/activation_height
Date:		May, 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		Threholds to override activation slack.

		=================	=====================================
		activation_width	(RW) Width threshold to immediately
					start processing touch events.

		activation_height	(RW) Height threshold to immediately
					start processing touch events.
		=================	=====================================

What:		/sys/bus/hid/drivers/ntrig/<dev>/min_width
What:		/sys/bus/hid/drivers/ntrig/<dev>/min_height
Date:		May, 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		Minimum size contact accepted.

		==========	===========================================
		min_width	(RW) Minimum touch contact width to decide
				activation and activity.

		min_height	(RW) Minimum touch contact height to decide
				activation and activity.
		==========	===========================================


What:		/sys/bus/hid/drivers/ntrig/<dev>/sensor_physical_width
What:		/sys/bus/hid/drivers/ntrig/<dev>/sensor_physical_height
Date:		May, 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		(RO) These are internal ranges not used for normal events but
		useful for tuning.


What:		/sys/bus/hid/drivers/ntrig/<dev>/sensor_logical_width
What:		/sys/bus/hid/drivers/ntrig/<dev>/sensor_logical_height
Date:		May, 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		(RO) The range for positions reported during activity.
