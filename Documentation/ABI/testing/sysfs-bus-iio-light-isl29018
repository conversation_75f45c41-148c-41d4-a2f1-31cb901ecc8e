What:		/sys/bus/iio/devices/iio:deviceX/proximity_on_chip_ambient_infrared_suppression
Date:		January 2011
KernelVersion:	2.6.37
Contact:	<EMAIL>
Description:
		From ISL29018 Data Sheet (FN6619.4, Oct 8, 2012) regarding the
		infrared suppression:

		Scheme 0, makes full n (4, 8, 12, 16) bits (unsigned) proximity
		detection. The range of Scheme 0 proximity count is from 0 to
		2^n. Logic 1 of this bit, Scheme 1, makes n-1 (3, 7, 11, 15)
		bits (2's complementary) proximity_less_ambient detection. The
		range of Scheme 1 proximity count is from -2^(n-1) to 2^(n-1).
		The sign bit is extended for resolutions less than 16. While
		Scheme 0 has wider dynamic range, Scheme 1 proximity detection
		is less affected by the ambient IR noise variation.

		== =============================================
		0  Sensing IR from LED and ambient
		1  Sensing IR from LED with ambient IR rejection
		== =============================================
