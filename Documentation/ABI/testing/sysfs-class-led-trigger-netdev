What:		/sys/class/leds/<led>/device_name
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Specifies the network device name to monitor.

What:		/sys/class/leds/<led>/interval
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Specifies the duration of the LED blink in milliseconds.
		Defaults to 50 ms.

		When offloaded is true, the interval value MUST be set to the
		default value and cannot be changed.
		Trying to set any value in this specific mode will return
		an EINVAL error.

What:		/sys/class/leds/<led>/link
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Signal the link state of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link state
		of the named network device.
		Setting this value also immediately changes the LED state.


What:		/sys/class/leds/<led>/tx
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Signal transmission of data on the named network device.

		If set to 0 (default), the LED will not blink on transmission.

		If set to 1, the LED will blink for the milliseconds specified
		in interval to signal transmission.

		When offloaded is true, the blink interval is controlled by
		hardware and won't reflect the value set in interval.

What:		/sys/class/leds/<led>/rx
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Signal reception of data on the named network device.

		If set to 0 (default), the LED will not blink on reception.

		If set to 1, the LED will blink for the milliseconds specified
		in interval to signal reception.

		When offloaded is true, the blink interval is controlled by
		hardware and won't reflect the value set in interval.

What:		/sys/class/leds/<led>/offloaded
Date:		Jun 2023
KernelVersion:	6.5
Contact:	<EMAIL>
Description:
		Communicate whether the LED trigger modes are offloaded to
		hardware or whether software fallback is used.

		If 0, the LED is using software fallback to blink.

		If 1, the LED blinking in requested mode is offloaded to
		hardware.

What:		/sys/class/leds/<led>/link_10
Date:		Jun 2023
KernelVersion:	6.5
Contact:	<EMAIL>
Description:
		Signal the link speed state of 10Mbps of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link state
		speed of 10MBps of the named network device.
		Setting this value also immediately changes the LED state.

		Present only if the named network device supports 10Mbps link speed.

What:		/sys/class/leds/<led>/link_100
Date:		Jun 2023
KernelVersion:	6.5
Contact:	<EMAIL>
Description:
		Signal the link speed state of 100Mbps of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link state
		speed of 100Mbps of the named network device.
		Setting this value also immediately changes the LED state.

		Present only if the named network device supports 100Mbps link speed.

What:		/sys/class/leds/<led>/link_1000
Date:		Jun 2023
KernelVersion:	6.5
Contact:	<EMAIL>
Description:
		Signal the link speed state of 1000Mbps of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link state
		speed of 1000Mbps of the named network device.
		Setting this value also immediately changes the LED state.

		Present only if the named network device supports 1000Mbps link speed.

What:		/sys/class/leds/<led>/link_2500
Date:		Nov 2023
KernelVersion:	6.8
Contact:	<EMAIL>
Description:
		Signal the link speed state of 2500Mbps of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link state
		speed of 2500Mbps of the named network device.
		Setting this value also immediately changes the LED state.

		Present only if the named network device supports 2500Mbps link speed.

What:		/sys/class/leds/<led>/link_5000
Date:		Nov 2023
KernelVersion:	6.8
Contact:	<EMAIL>
Description:
		Signal the link speed state of 5000Mbps of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link state
		speed of 5000Mbps of the named network device.
		Setting this value also immediately changes the LED state.

		Present only if the named network device supports 5000Mbps link speed.

What:		/sys/class/leds/<led>/link_10000
Date:		Nov 2023
KernelVersion:	6.8
Contact:	<EMAIL>
Description:
		Signal the link speed state of 10000Mbps of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link state
		speed of 10000Mbps of the named network device.
		Setting this value also immediately changes the LED state.

		Present only if the named network device supports 10000Mbps link speed.

What:		/sys/class/leds/<led>/half_duplex
Date:		Jun 2023
KernelVersion:	6.5
Contact:	<EMAIL>
Description:
		Signal the link half duplex state of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link half
		duplex state of the named network device.
		Setting this value also immediately changes the LED state.

What:		/sys/class/leds/<led>/full_duplex
Date:		Jun 2023
KernelVersion:	6.5
Contact:	<EMAIL>
Description:
		Signal the link full duplex state of the named network device.

		If set to 0 (default), the LED's normal state is off.

		If set to 1, the LED's normal state reflects the link full
		duplex state of the named network device.
		Setting this value also immediately changes the LED state.
