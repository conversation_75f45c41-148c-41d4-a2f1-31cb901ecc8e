What:		/sys/.../events/in_illuminance0_thresh_either_en
Date:		April 2012
KernelVersion:	3.5
Contact:	<PERSON> <<EMAIL>>
Description:
		Event generated when channel passes one of the four thresholds
		in each direction (rising|falling) and a zone change occurs.
		The corresponding light zone can be read from
		in_illuminance0_zone.

What:		/sys/.../events/in_illuminance0_threshY_hysteresis
Date:		May 2012
KernelVersion:	3.5
Contact:	<PERSON> <<EMAIL>>
Description:
		Get the hysteresis for thresholds Y, that is,
		threshY_hysteresis = threshY_raising - threshY_falling

What:		/sys/.../events/illuminance_threshY_falling_value
What:		/sys/.../events/illuminance_threshY_raising_value
Date:		April 2012
KernelVersion:	3.5
Contact:	<PERSON> <jhov<PERSON>@gmail.com>
Description:
		Specifies the value of threshold that the device is comparing
		against for the events enabled by
		in_illuminance0_thresh_either_en (0..255), where Y in 0..3.

		Note that threshY_falling must be less than or equal to
		threshY_raising.

		These thresholds correspond to the eight zone-boundary
		registers (boundaryY_{low,high}) and define the five light
		zones.

What:		/sys/bus/iio/devices/iio:deviceX/in_illuminance0_zone
Date:		April 2012
KernelVersion:	3.5
Contact:	Johan Hovold <<EMAIL>>
Description:
		Get the current light zone (0..4) as defined by the
		in_illuminance0_threshY_{falling,rising} thresholds.

What:		/sys/bus/iio/devices/iio:deviceX/out_currentY_currentZ_raw
Date:		May 2012
KernelVersion:	3.5
Contact:	Johan Hovold <<EMAIL>>
Description:
		Set the output current for channel out_currentY when in zone
		Z (0..255), where Y in 0..2 and Z in 0..4.

		These values correspond to the ALS-mapper target registers for
		ALS-mapper Y + 1.

		Note that out_currentY_raw provides the current for the
		current zone.
