What:		/sys/class/tee/tee{,priv}X/rpmb_routing_model
Date:		May 2024
KernelVersion:	6.10
Contact:        <EMAIL>
Description:
		RPMB frames can be routed to the RPMB device via the
		user-space daemon tee-supplicant or the RPMB subsystem
		in the kernel. The value "user" means that the driver
		will route the RPMB frames via user space. Conversely,
		"kernel" means that the frames are routed via the RPMB
		subsystem without assistance from tee-supplicant. It
		should be assumed that RPMB frames are routed via user
		space if the variable is absent. The primary purpose
		of this variable is to let systemd know whether
		tee-supplicant is needed in the early boot with initramfs.
