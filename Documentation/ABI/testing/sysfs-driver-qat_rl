What:		/sys/bus/pci/devices/<BDF>/qat_rl/sla_op
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:
		(WO) This attribute is used to perform an operation on an SLA.
		The supported operations are: add, update, rm, rm_all, and get.

		Input values must be filled through the associated attribute in
		this group before a write to this file.
		If the operation completes successfully, the associated
		attributes will be updated.
		The associated attributes are: cir, pir, srv, rp, and id.

		Supported operations:

		* add: Creates a new SLA with the provided inputs from user.
			* Inputs: cir, pir, srv, and rp
			* Output: id

		* get: Returns the configuration of the specified SLA in id attribute
			* Inputs: id
			* Outputs: cir, pir, srv, and rp

		* update: Updates the SLA with new values set in the following attributes
			* Inputs: id, cir, and pir

		* rm: Removes the specified SLA in the id attribute.
			* Inputs: id

		* rm_all: Removes all the configured SLAs.
			* Inputs: None

		This attribute is only available for qat_4xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_rl/rp
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:
		(RW) When read, reports the current assigned ring pairs for the
		queried SLA.
		When wrote to, configures the ring pairs associated to a new SLA.

		The value is a 64-bit bit mask and is written/displayed in hex.
		Each bit of this mask represents a single ring pair i.e.,
		bit 1 == ring pair id 0; bit 3 == ring pair id 2.

		Selected ring pairs must to be assigned to a single service,
		i.e. the one provided with the srv attribute. The service
		assigned to a certain ring pair can be checked by querying
		the attribute qat/rp2srv.

		The maximum number of ring pairs is 4 per SLA.

		Applicability in sla_op:

		* WRITE: add operation
		* READ: get operation

		Example usage::

			## Read
			# echo 4 > /sys/bus/pci/devices/<BDF>/qat_rl/id
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/rp
			0x5

			## Write
			# echo 0x5 > /sys/bus/pci/devices/<BDF>/qat_rl/rp

		This attribute is only available for qat_4xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_rl/id
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:
		(RW) If written to, the value is used to retrieve a particular
		SLA and operate on it.
		This is valid only for the following operations: update, rm,
		and get.
		A read of this attribute is only guaranteed to have correct data
		after creation of an SLA.

		Applicability in sla_op:

		* WRITE: rm and update operations
		* READ: add and get operations

		Example usage::

			## Read
			## Set attributes e.g. cir, pir, srv, etc
			# echo "add" > /sys/bus/pci/devices/<BDF>/qat_rl/sla_op
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/id
			4

			## Write
			# echo 7 > /sys/bus/pci/devices/<BDF>/qat_rl/id
			# echo "get" > /sys/bus/pci/devices/<BDF>/qat_rl/sla_op
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/rp
			0x5  ## ring pair ID 0 and ring pair ID 2

		This attribute is only available for qat_4xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_rl/cir
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:
		(RW) Committed information rate (CIR). Rate guaranteed to be
		achieved by a particular SLA. The value is expressed in
		permille scale, i.e. 1000 refers to the maximum device
		throughput for a selected service.

		After sending a "get" to sla_op, this will be populated with the
		CIR for that queried SLA.
		Write to this file before sending an "add/update" sla_op, to set
		the SLA to the specified value.

		Applicability in sla_op:

		* WRITE: add and update operations
		* READ: get operation

		Example usage::

			## Write
			# echo 500 > /sys/bus/pci/devices/<BDF>/qat_rl/cir
			# echo "add" /sys/bus/pci/devices/<BDF>/qat_rl/sla_op

			## Read
			# echo 4 > /sys/bus/pci/devices/<BDF>/qat_rl/id
			# echo "get" > /sys/bus/pci/devices/<BDF>/qat_rl/sla_op
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/cir
			500

		This attribute is only available for qat_4xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_rl/pir
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:
		(RW) Peak information rate (PIR). The maximum rate that can be
		achieved by that particular SLA. An SLA can reach a value
		between CIR and PIR when the device is not fully utilized by
		requests from other users (assigned to different SLAs).

		After sending a "get" to sla_op, this will be populated with the
		PIR for that queried SLA.
		Write to this file before sending an "add/update" sla_op, to set
		the SLA to the specified value.

		Applicability in sla_op:

		* WRITE: add and update operations
		* READ: get operation

		Example usage::

			## Write
			# echo 750 > /sys/bus/pci/devices/<BDF>/qat_rl/pir
			# echo "add" > /sys/bus/pci/devices/<BDF>/qat_rl/sla_op

			## Read
			# echo 4 > /sys/bus/pci/devices/<BDF>/qat_rl/id
			# echo "get" > /sys/bus/pci/devices/<BDF>/qat_rl/sla_op
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/pir
			750

		This attribute is only available for qat_4xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_rl/srv
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:
		(RW) Service (SRV). Represents the service (sym, asym, dc)
		associated to an SLA.
		Can be written to or queried to set/show the SRV type for an SLA.
		The SRV attribute is used to specify the SRV type before adding
		an SLA. After an SLA is configured, reports the service
		associated to that SLA.

		Applicability in sla_op:

		* WRITE: add and update operations
		* READ: get operation

		Example usage::

			## Write
			# echo "dc" > /sys/bus/pci/devices/<BDF>/qat_rl/srv
			# echo "add" > /sys/bus/pci/devices/<BDF>/qat_rl/sla_op
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/id
			4

			## Read
			# echo 4 > /sys/bus/pci/devices/<BDF>/qat_rl/id
			# echo "get" > /sys/bus/pci/devices/<BDF>/qat_rl/sla_op
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/srv
			dc

		This attribute is only available for qat_4xxx devices.

What:		/sys/bus/pci/devices/<BDF>/qat_rl/cap_rem
Date:		January 2024
KernelVersion:	6.7
Contact:	<EMAIL>
Description:
		(RW) This file will return the remaining capability for a
		particular service/sla. This is the remaining value that a new
		SLA can be set to or a current SLA can be increased with.

		Example usage::

			# echo "asym" > /sys/bus/pci/devices/<BDF>/qat_rl/cap_rem
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/cap_rem
			250
			# echo 250 > /sys/bus/pci/devices/<BDF>/qat_rl/cir
			# echo "add" > /sys/bus/pci/devices/<BDF>/qat_rl/sla_op
			# cat /sys/bus/pci/devices/<BDF>/qat_rl/cap_rem
			0

		This attribute is only available for qat_4xxx devices.
