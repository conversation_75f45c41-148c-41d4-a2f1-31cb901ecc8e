What:		/sys/bus/iio/devices/iio:deviceX/in_voltage0_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 USBID ADC which connected to connector ID pin.
		Calculating with scale and offset returns voltage in uV

What:		/sys/bus/iio/devices/iio:deviceX/in_voltage1_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 VBUS ADC with lower accuracy(+-75mA)
		higher measure range(1~22mV)
		Calculating with scale and offset returns voltage in uV

What:		/sys/bus/iio/devices/iio:deviceX/in_voltage2_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 VBUS ADC with higher accuracy(+-30mA)
		lower measure range(1~9.76V)
		Calculating with scale and offset returns voltage in uV

What:		/sys/bus/iio/devices/iio:deviceX/in_voltage3_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 VSYS ADC
		Calculating with scale and offset returns voltage in uV

What:		/sys/bus/iio/devices/iio:deviceX/in_voltage4_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 VBAT ADC
		Calculating with scale and offset returns voltage in uV

What:		/sys/bus/iio/devices/iio:deviceX/in_current5_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 IBUS ADC
		Calculating with scale and offset returns voltage in uA

What:		/sys/bus/iio/devices/iio:deviceX/in_current6_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 IBAT ADC
		Calculating with scale and offset returns voltage in uA

What:		/sys/bus/iio/devices/iio:deviceX/in_current7_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 CHG_VDDP ADC
		Calculating with scale and offset returns voltage in uV

What:		/sys/bus/iio/devices/iio:deviceX/in_temp8_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 IC junction temperature
		Calculating with scale and offset returns temperature in degree

What:		/sys/bus/iio/devices/iio:deviceX/in_voltage9_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 VREF_TS ADC
		Calculating with scale and offset returns voltage in uV

What:		/sys/bus/iio/devices/iio:deviceX/in_voltage10_raw
KernelVersion:	5.8.0
Contact:	<EMAIL>
Description:
		Indicated MT6360 TS ADC
		Calculating with scale and offset returns voltage in uV
