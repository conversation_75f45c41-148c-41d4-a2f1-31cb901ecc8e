What:		/sys/bus/platform/devices/dfl-fme.0/ports_num
Date:		June 2018
KernelVersion:  4.19
Contact:	<PERSON> <<EMAIL>>
Description:	Read-only. One DFL FPGA device may have more than 1
		port/Accelerator Function Unit (AFU). It returns the
		number of ports on the FPGA device when read it.

What:		/sys/bus/platform/devices/dfl-fme.0/bitstream_id
Date:		June 2018
KernelVersion:  4.19
Contact:	<PERSON> <<EMAIL>>
Description:	Read-only. It returns Bitstream (static FPGA region)
		identifier number, which includes the detailed version
		and other information of this static FPGA region.

What:		/sys/bus/platform/devices/dfl-fme.0/bitstream_metadata
Date:		June 2018
KernelVersion:  4.19
Contact:	Wu Ha<PERSON> <<EMAIL>>
Description:	Read-only. It returns Bitstream (static FPGA region) meta
		data, which includes the synthesis date, seed and other
		information of this static FPGA region.

What:		/sys/bus/platform/devices/dfl-fme.0/cache_size
Date:		August 2019
KernelVersion:  5.4
Contact:	<PERSON> <<EMAIL>>
Description:	Read-only. It returns cache size of this FPGA device.

What:		/sys/bus/platform/devices/dfl-fme.0/fabric_version
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. It returns fabric version of this FPGA device.
		Userspace applications need this information to select
		best data channels per different fabric design.

What:		/sys/bus/platform/devices/dfl-fme.0/socket_id
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. It returns socket_id to indicate which socket
		this FPGA belongs to, only valid for integrated solution.
		User only needs this information, in case standard numa node
		can't provide correct information.

What:		/sys/bus/platform/devices/dfl-fme.0/errors/pcie0_errors
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Write. Read this file for errors detected on pcie0 link.
		Write this file to clear errors logged in pcie0_errors. Write
		fails with -EINVAL if input parsing fails or input error code
		doesn't match.

What:		/sys/bus/platform/devices/dfl-fme.0/errors/pcie1_errors
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Write. Read this file for errors detected on pcie1 link.
		Write this file to clear errors logged in pcie1_errors. Write
		fails with -EINVAL if input parsing fails or input error code
		doesn't match.

What:		/sys/bus/platform/devices/dfl-fme.0/errors/nonfatal_errors
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. It returns non-fatal errors detected.

What:		/sys/bus/platform/devices/dfl-fme.0/errors/catfatal_errors
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. It returns catastrophic and fatal errors detected.

What:		/sys/bus/platform/devices/dfl-fme.0/errors/inject_errors
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Write. Read this file to check errors injected. Write this
		file to inject errors for testing purpose. Write fails with
		-EINVAL if input parsing fails or input inject error code isn't
		supported.

What:		/sys/bus/platform/devices/dfl-fme.0/errors/fme_errors
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Write. Read this file to get errors detected on FME.
		Write this file to clear errors logged in fme_errors. Write
		fails with -EINVAL if input parsing fails or input error code
		doesn't match.

What:		/sys/bus/platform/devices/dfl-fme.0/errors/first_error
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. Read this file to get the first error detected by
		hardware.

What:		/sys/bus/platform/devices/dfl-fme.0/errors/next_error
Date:		August 2019
KernelVersion:  5.4
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. Read this file to get the second error detected by
		hardware.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/name
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. Read this file to get the name of hwmon device, it
		supports values:

		=================  =========================
		'dfl_fme_thermal'  thermal hwmon device name
		'dfl_fme_power'    power hwmon device name
		=================  =========================

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/temp1_input
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. It returns FPGA device temperature in millidegrees
		Celsius.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/temp1_max
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. It returns hardware threshold1 temperature in
		millidegrees Celsius. If temperature rises at or above this
		threshold, hardware starts 50% or 90% throttling (see
		'temp1_max_policy').

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/temp1_crit
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. It returns hardware threshold2 temperature in
		millidegrees Celsius. If temperature rises at or above this
		threshold, hardware starts 100% throttling.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/temp1_emergency
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. It returns hardware trip threshold temperature in
		millidegrees Celsius. If temperature rises at or above this
		threshold, a fatal event will be triggered to board management
		controller (BMC) to shutdown FPGA.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/temp1_max_alarm
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. It returns 1 if temperature is currently at or above
		hardware threshold1 (see 'temp1_max'), otherwise 0.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/temp1_crit_alarm
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. It returns 1 if temperature is currently at or above
		hardware threshold2 (see 'temp1_crit'), otherwise 0.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/temp1_max_policy
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. Read this file to get the policy of hardware threshold1
		(see 'temp1_max'). It only supports two values (policies):

		==  ==========================
		 0  AP2 state (90% throttling)
	         1  AP1 state (50% throttling)
		==  ==========================

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/power1_input
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. It returns current FPGA power consumption in uW.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/power1_max
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Write. Read this file to get current hardware power
		threshold1 in uW. If power consumption rises at or above
		this threshold, hardware starts 50% throttling.
		Write this file to set current hardware power threshold1 in uW.
		As hardware only accepts values in Watts, so input value will
		be round down per Watts (< 1 watts part will be discarded) and
		clamped within the range from 0 to 127 Watts. Write fails with
		-EINVAL if input parsing fails.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/power1_crit
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Write. Read this file to get current hardware power
		threshold2 in uW. If power consumption rises at or above
		this threshold, hardware starts 90% throttling.
		Write this file to set current hardware power threshold2 in uW.
		As hardware only accepts values in Watts, so input value will
		be round down per Watts (< 1 watts part will be discarded) and
		clamped within the range from 0 to 127 Watts. Write fails with
		-EINVAL if input parsing fails.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/power1_max_alarm
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. It returns 1 if power consumption is currently at or
		above hardware threshold1 (see 'power1_max'), otherwise 0.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/power1_crit_alarm
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. It returns 1 if power consumption is currently at or
		above hardware threshold2 (see 'power1_crit'), otherwise 0.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/power1_xeon_limit
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. It returns power limit for XEON in uW.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/power1_fpga_limit
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-Only. It returns power limit for FPGA in uW.

What:		/sys/bus/platform/devices/dfl-fme.0/hwmon/hwmonX/power1_ltr
Date:		October 2019
KernelVersion:	5.5
Contact:	Wu Hao <<EMAIL>>
Description:	Read-only. Read this file to get current Latency Tolerance
		Reporting (ltr) value. It returns 1 if all Accelerated
		Function Units (AFUs) can tolerate latency >= 40us for memory
		access or 0 if any AFU is latency sensitive (< 40us).
