What:		/sys/bus/pci/drivers/ehci_hcd/.../companion
		/sys/bus/usb/devices/usbN/../companion
Date:		January 2007
KernelVersion:	2.6.21
Contact:	<PERSON> <<EMAIL>>
Description:
		PCI-based EHCI USB controllers (i.e., high-speed USB-2.0
		controllers) are often implemented along with a set of
		"companion" full/low-speed USB-1.1 controllers.  When a
		high-speed device is plugged in, the connection is routed
		to the EHCI controller; when a full- or low-speed device
		is plugged in, the connection is routed to the companion
		controller.

		Sometimes you want to force a high-speed device to connect
		at full speed, which can be accomplished by forcing the
		connection to be routed to the companion controller.
		That's what this file does.  Writing a port number to the
		file causes connections on that port to be routed to the
		companion controller, and writing the negative of a port
		number returns the port to normal operation.

		For example: To force the high-speed device attached to
		port 4 on bus 2 to run at full speed::

			echo 4 >/sys/bus/usb/devices/usb2/../companion

		To return the port to high-speed operation::

			echo -4 >/sys/bus/usb/devices/usb2/../companion

		Reading the file gives the list of ports currently forced
		to the companion controller.

		Note: Some EHCI controllers do not have companions; they
		may contain an internal "transaction translator" or they
		may be attached directly to a "rate-matching hub".  This
		mechanism will not work with such controllers.  Also, it
		cannot be used to force a port on a high-speed hub to
		connect at full speed.

		Note: When this file was first added, it appeared in a
		different sysfs directory.  The location given above is
		correct for 2.6.35 (and probably several earlier kernel
		versions as well).

