What:		/sys/class/power_supply/wilco-charger/charge_type
Date:		April 2019
KernelVersion:	5.2
Description:
		What charging algorithm to use:

		Standard:
			Fully charges battery at a standard rate.
		Adaptive:
			Battery settings adaptively optimized based on
			typical battery usage pattern.
		Fast:
			Battery charges over a shorter period.
		Trickle:
			Extends battery lifespan, intended for users who
			primarily use their Chromebook while connected to AC.
		Custom:
			A low and high threshold percentage is specified.
			Charging begins when level drops below
			charge_control_start_threshold, and ceases when
			level is above charge_control_end_threshold.
		Long Life:
			Customized charge rate for last longer battery life.
			On Wilco device this mode is pre-configured in the factory
			through EC's private PID. Switching to a different mode will
			be denied by Wilco EC when Long Life mode is enabled.

What:		/sys/class/power_supply/wilco-charger/charge_control_start_threshold
Date:		April 2019
KernelVersion:	5.2
Description:
		Used when charge_type="Custom", as described above. Measured in
		percentages. The valid range is [50, 95].

What:		/sys/class/power_supply/wilco-charger/charge_control_end_threshold
Date:		April 2019
KernelVersion:	5.2
Description:
		Used when charge_type="Custom", as described above. Measured in
		percentages. The valid range is [55, 100].
