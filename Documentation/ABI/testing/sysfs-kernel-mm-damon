what:		/sys/kernel/mm/damon/
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Interface for Data Access MONitoring (DAMON).  Contains files
		for controlling DAMON.  For more details on DAMON itself,
		please refer to Documentation/admin-guide/mm/damon/index.rst.

What:		/sys/kernel/mm/damon/admin/
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Interface for privileged users of DAMON.  Contains files for
		controlling DAMON that aimed to be used by privileged users.

What:		/sys/kernel/mm/damon/admin/kdamonds/nr_kdamonds
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a number 'N' to this file creates the number of
		directories for controlling each DAMON worker thread (kdamond)
		named '0' to 'N-1' under the kdamonds/ directory.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/state
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing 'on' or 'off' to this file makes the kdamond starts or
		stops, respectively.  Reading the file returns the keywords
		based on the current status.  Writing 'commit' to this file
		makes the kdamond reads the user inputs in the sysfs files
		except 'state' again.  Writing 'commit_schemes_quota_goals' to
		this file makes the kdamond reads the quota goal files again.
		Writing 'update_schemes_stats' to the file updates contents of
		schemes stats files of the kdamond.  Writing
		'update_schemes_tried_regions' to the file updates contents of
		'tried_regions' directory of every scheme directory of this
		kdamond.  Writing 'update_schemes_tried_bytes' to the file
		updates only '.../tried_regions/total_bytes' files of this
		kdamond.  Writing 'clear_schemes_tried_regions' to the file
		removes contents of the 'tried_regions' directory.  Writing
		'update_schemes_effective_quotas' to the file updates
		'.../quotas/effective_bytes' files of this kdamond.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/pid
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the pid of the kdamond if it is
		running.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/nr_contexts
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a number 'N' to this file creates the number of
		directories for controlling each DAMON context named '0' to
		'N-1' under the contexts/ directory.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/avail_operations
Date:		Apr 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the available monitoring operations
		sets on the currently running kernel.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/operations
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a keyword for a monitoring operations set ('vaddr' for
		virtual address spaces monitoring, 'fvaddr' for fixed virtual
		address ranges monitoring, and 'paddr' for the physical address
		space monitoring) to this file makes the context to use the
		operations set.  Reading the file returns the keyword for the
		operations set the context is set to use.

		Note that only the operations sets that listed in
		'avail_operations' file are valid inputs.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/monitoring_attrs/intervals/sample_us
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a value to this file sets the sampling interval of the
		DAMON context in microseconds as the value.  Reading this file
		returns the value.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/monitoring_attrs/intervals/aggr_us
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a value to this file sets the aggregation interval of
		the DAMON context in microseconds as the value.  Reading this
		file returns the value.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/monitoring_attrs/intervals/update_us
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a value to this file sets the update interval of the
		DAMON context in microseconds as the value.  Reading this file
		returns the value.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/monitoring_attrs/nr_regions/min

WDate:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a value to this file sets the minimum number of
		monitoring regions of the DAMON context as the value.  Reading
		this file returns the value.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/monitoring_attrs/nr_regions/max
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a value to this file sets the maximum number of
		monitoring regions of the DAMON context as the value.  Reading
		this file returns the value.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/targets/nr_targets
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a number 'N' to this file creates the number of
		directories for controlling each DAMON target of the context
		named '0' to 'N-1' under the contexts/ directory.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/targets/<T>/pid_target
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the pid of
		the target process if the context is for virtual address spaces
		monitoring, respectively.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/targets/<T>/regions/nr_regions
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a number 'N' to this file creates the number of
		directories for setting each DAMON target memory region of the
		context named '0' to 'N-1' under the regions/ directory.  In
		case of the virtual address space monitoring, DAMON
		automatically sets the target memory region based on the target
		processes' mappings.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/targets/<T>/regions/<R>/start
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the start
		address of the monitoring region.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/targets/<T>/regions/<R>/end
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the end
		address of the monitoring region.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/nr_schemes
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a number 'N' to this file creates the number of
		directories for controlling each DAMON-based operation scheme
		of the context named '0' to 'N-1' under the schemes/ directory.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/action
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the action
		of the scheme.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/target_nid
Date:		Jun 2024
Contact:	SeongJae Park <<EMAIL>>
Description:	Action's target NUMA node id.  Supported by only relevant
		actions.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/apply_interval_us
Date:		Sep 2023
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a value to this file sets the action apply interval of
		the scheme in microseconds.  Reading this file returns the
		value.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/access_pattern/sz/min
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the minimum
		size of the scheme's target regions in bytes.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/access_pattern/sz/max
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the maximum
		size of the scheme's target regions in bytes.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/access_pattern/nr_accesses/min
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the manimum
		'nr_accesses' of the scheme's target regions.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/access_pattern/nr_accesses/max
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the maximum
		'nr_accesses' of the scheme's target regions.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/access_pattern/age/min
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the minimum
		'age' of the scheme's target regions.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/access_pattern/age/max
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the maximum
		'age' of the scheme's target regions.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/ms
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the time
		quota of the scheme in milliseconds.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/bytes
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the size
		quota of the scheme in bytes.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/effective_bytes
Date:		Feb 2024
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading from this file gets the effective size quota of the
		scheme in bytes, which adjusted for the time quota and goals.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/reset_interval_ms
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the quotas
		charge reset interval of the scheme in milliseconds.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/goals/nr_goals
Date:		Nov 2023
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a number 'N' to this file creates the number of
		directories for setting automatic tuning of the scheme's
		aggressiveness named '0' to 'N-1' under the goals/ directory.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/goals/<G>/target_metric
Date:		Feb 2024
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the quota
		auto-tuning goal metric.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/goals/<G>/target_value
Date:		Nov 2023
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the target
		value of the goal metric.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/goals/<G>/current_value
Date:		Nov 2023
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the current
		value of the goal metric.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/weights/sz_permil
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the
		under-quota limit regions prioritization weight for 'size' in
		permil.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/weights/nr_accesses_permil
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the
		under-quota limit regions prioritization weight for
		'nr_accesses' in permil.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/quotas/weights/age_permil
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the
		under-quota limit regions prioritization weight for 'age' in
		permil.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/watermarks/metric
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the metric
		of the watermarks for the scheme.  The writable/readable
		keywords for this file are 'none' for disabling the watermarks
		feature, or 'free_mem_rate' for the system's global free memory
		rate in permil.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/watermarks/interval_us
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the metric
		check interval of the watermarks for the scheme in
		microseconds.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/watermarks/high
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the high
		watermark of the scheme in permil.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/watermarks/mid
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the mid
		watermark of the scheme in permil.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/watermarks/low
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the low
		watermark of the scheme in permil.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/filters/nr_filters
Date:		Dec 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing a number 'N' to this file creates the number of
		directories for setting filters of the scheme named '0' to
		'N-1' under the filters/ directory.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/filters/<F>/type
Date:		Dec 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing to and reading from this file sets and gets the type of
		the memory of the interest.  'anon' for anonymous pages,
		'memcg' for specific memory cgroup, 'young' for young pages,
		'addr' for address range (an open-ended interval), or 'target'
		for DAMON monitoring target can be written and read.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/filters/<F>/memcg_path
Date:		Dec 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	If 'memcg' is written to the 'type' file, writing to and
		reading from this file sets and gets the path to the memory
		cgroup of the interest.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/filters/<F>/addr_start
Date:		Jul 2023
Contact:	SeongJae Park <<EMAIL>>
Description:	If 'addr' is written to the 'type' file, writing to or reading
		from this file sets or gets the start address of the address
		range for the filter.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/filters/<F>/addr_end
Date:		Jul 2023
Contact:	SeongJae Park <<EMAIL>>
Description:	If 'addr' is written to the 'type' file, writing to or reading
		from this file sets or gets the end address of the address
		range for the filter.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/filters/<F>/target_idx
Date:		Dec 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	If 'target' is written to the 'type' file, writing to or
		reading from this file sets or gets the index of the DAMON
		monitoring target of the interest.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/filters/<F>/matching
Date:		Dec 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Writing 'Y' or 'N' to this file sets whether to filter out
		pages that do or do not match to the 'type' and 'memcg_path',
		respectively.  Filter out means the action of the scheme will
		not be applied to.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/stats/nr_tried
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the number of regions that the action
		of the scheme has tried to be applied.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/stats/sz_tried
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the total size of regions that the
		action of the scheme has tried to be applied in bytes.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/stats/nr_applied
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the number of regions that the action
		of the scheme has successfully applied.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/stats/sz_applied
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the total size of regions that the
		action of the scheme has successfully applied in bytes.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/stats/qt_exceeds
Date:		Mar 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the number of the exceed events of
		the scheme's quotas.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/tried_regions/total_bytes
Date:		Jul 2023
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the total amount of memory that
		corresponding DAMON-based Operation Scheme's action has tried
		to be applied.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/tried_regions/<R>/start
Date:		Oct 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the start address of a memory region
		that corresponding DAMON-based Operation Scheme's action has
		tried to be applied.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/tried_regions/<R>/end
Date:		Oct 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the end address of a memory region
		that corresponding DAMON-based Operation Scheme's action has
		tried to be applied.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/tried_regions/<R>/nr_accesses
Date:		Oct 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the 'nr_accesses' of a memory region
		that corresponding DAMON-based Operation Scheme's action has
		tried to be applied.

What:		/sys/kernel/mm/damon/admin/kdamonds/<K>/contexts/<C>/schemes/<S>/tried_regions/<R>/age
Date:		Oct 2022
Contact:	SeongJae Park <<EMAIL>>
Description:	Reading this file returns the 'age' of a memory region that
		corresponding DAMON-based Operation Scheme's action has tried
		to be applied.
