What:		/sys/bus/i2c/devices/.../heading0_input
Date:		April 2010
KernelVersion: 2.6.36?
Contact:	<EMAIL>
Description:	Reports the current heading from the compass as a floating
		point value in degrees.

What:		/sys/bus/i2c/devices/.../power_state
Date:		April 2010
KernelVersion: 2.6.36?
Contact:	<EMAIL>
Description:	Sets the power state of the device. 0 sets the device into
		sleep mode, 1 wakes it up.

What:		/sys/bus/i2c/devices/.../calibration
Date:		April 2010
KernelVersion: 2.6.36?
Contact:	<EMAIL>
Description:	Sets the calibration on or off (1 = on, 0 = off). See the
		chip data sheet.

