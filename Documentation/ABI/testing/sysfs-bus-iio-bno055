What:		/sys/bus/iio/devices/iio:deviceX/in_accel_raw_range
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Raw (unscaled) range for acceleration readings. Unit after
		application of scale is m/s^2. Note that this doesn't affects
		the scale (which should be used when changing the maximum and
		minimum readable value affects also the reading scaling factor).

What:		/sys/bus/iio/devices/iio:deviceX/in_anglvel_raw_range
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Range for angular velocity readings in radians per second. Note
		that this does not affects the scale (which should be used when
		changing the maximum and minimum readable value affects also the
		reading scaling factor).

What:		/sys/bus/iio/devices/iio:deviceX/in_accel_raw_range_available
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		List of allowed values for in_accel_raw_range attribute

What:		/sys/bus/iio/devices/iio:deviceX/in_anglvel_raw_range_available
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		List of allowed values for in_anglvel_raw_range attribute

What:		/sys/bus/iio/devices/iio:deviceX/in_magn_calibration_fast_enable
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Can be 1 or 0. Enables/disables the "Fast Magnetometer
		Calibration" HW function.

What:		/sys/bus/iio/devices/iio:deviceX/fusion_enable
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Can be 1 or 0. Enables/disables the "sensor fusion" (a.k.a.
		NDOF) HW function.

What:		/sys/bus/iio/devices/iio:deviceX/calibration_data
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Reports the binary calibration data blob for the IMU sensors.

What:		/sys/bus/iio/devices/iio:deviceX/in_accel_calibration_auto_status
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Reports the autocalibration status for the accelerometer sensor.
		Can be 0 (calibration non even enabled) or 1 to 5 where the greater
		the number, the better the calibration status.

What:		/sys/bus/iio/devices/iio:deviceX/in_gyro_calibration_auto_status
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Reports the autocalibration status for the gyroscope sensor.
		Can be 0 (calibration non even enabled) or 1 to 5 where the greater
		the number, the better the calibration status.

What:		/sys/bus/iio/devices/iio:deviceX/in_magn_calibration_auto_status
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Reports the autocalibration status for the magnetometer sensor.
		Can be 0 (calibration non even enabled) or 1 to 5 where the greater
		the number, the better the calibration status.

What:		/sys/bus/iio/devices/iio:deviceX/sys_calibration_auto_status
KernelVersion:	6.1
Contact:	<EMAIL>
Description:
		Reports the status for the IMU overall autocalibration.
		Can be 0 (calibration non even enabled) or 1 to 5 where the greater
		the number, the better the calibration status.
