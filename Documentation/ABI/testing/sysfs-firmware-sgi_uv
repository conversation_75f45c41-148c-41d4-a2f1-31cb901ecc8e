What:		/sys/firmware/sgi_uv/
Date:		September 2020
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/firmware/sgi_uv directory contains information
		about the UV platform.

		Under that directory are a number of read-only attributes::

			archtype
			hub_type
			hubless
			partition_id
			coherence_id
			uv_type

		The archtype entry contains the UV architecture type that
		is used to select arch-dependent addresses and features.
		It can be set via the OEM_ID in the ACPI MADT table or by
		UVsystab entry both passed from UV BIOS.

		The hub_type entry is used to select the type of hub which is
		similar to uv_type but encoded in a binary format.  Include
		the file uv_hub.h to get the definitions.

		The hubless entry basically is present and set only if there
		is no hub.  In this case the hub_type entry is not present.

		The partition_id entry contains the partition id.
		UV systems can be partitioned into multiple physical
		machines, which each partition running a unique copy
		of the operating system. Each partition will have a unique
		partition id.

		The coherence_id entry contains the coherence id.
		A partitioned UV system can have one or more coherence
		domains. The coherence id indicates which coherence domain
		this partition is in.

		The uv_type entry contains the hub revision number.
		This value can be used to identify the UV system version::

			"0.*" = Hubless UV ('*' is subtype)
			"3.0" = UV2
			"5.0" = UV3
			"7.0" = UV4
			"7.1" = UV4a
			"9.0" = UV5

		The /sys/firmware/sgi_uv directory also contains two directories::

			hubs/
			pcibuses/

		The hubs directory contains a number of hub objects, each representing
		a UV Hub visible to the BIOS. Each hub object's name is appended by a
		unique ordinal value (ex. /sys/firmware/sgi_uv/hubs/hub_5)

		Each hub object directory contains a number of read-only attributes::

			cnode
			location
			name
			nasid
			shared
			this_partition

		The cnode entry contains the cnode number of the corresponding hub.
		If a cnode value is not applicable, the value returned will be -1.

		The location entry contains the location string of the corresponding hub.
		This value is used to physically identify a hub within a system.

		The name entry contains the name of the corresponding hub. This name can
		be two variants::

			"UVHub x.x" = A 'node' ASIC, connecting a CPU to the interconnect
			fabric. The 'x.x' value represents the ASIC revision.
			(ex. 'UVHub 5.0')

			"NLxRouter" = A 'router ASIC, only connecting other ASICs to
			the interconnect fabric. The 'x' value representing
			the fabric technology version. (ex. 'NL8Router')

		The nasid entry contains the nasid number of the corresponding hub.
		If a nasid value is not applicable, the value returned will be -1.

		The shared entry contains a boolean value describing whether the
		corresponding hub is shared between system partitions.

		The this_partition entry contains a boolean value describing whether
		the corresponding hub is local to the current partition.

		Each hub object directory also contains a number of port objects,
		each representing a fabric port on the corresponding hub.
		A port object's name is appended by a unique ordinal value
		(ex. /sys/firmware/sgi_uv/hubs/hub_5/port_3)

		Each port object directory contains a number of read-only attributes::

			conn_hub
			conn_port

		The conn_hub entry contains a value representing the unique
		ordinal value of the hub on the other end of the fabric
		cable plugged into the port. If the port is disconnected,
		the value returned will be -1.

		The conn_port entry contains a value representing the unique
		ordinal value of the port on the other end of the fabric cable
		plugged into the port. If the port is disconnected, the value
		returned will be -1.

		Ex:
			A value of '3' is read from:
				/sys/firmware/sgi_uv/hubs/hub_5/port_3/conn_hub

			and a value of '6' is read from:
				/sys/firmware/sgi_uv/hubs/hub_5/port_3/conn_port

			representing that this port is connected to:
				/sys/firmware/sgi_uv/hubs/hub_3/port_6

		The pcibuses directory contains a number of PCI bus objects.
		Each PCI bus object's name is appended by its PCI bus address.
		(ex. pcibus_0003:80)

		Each pcibus object has a number of possible read-only attributes::

			type
			location
			slot
			ppb_addr
			iio_stack

		The type entry contains a value describing the type of IO at
		the corresponding PCI bus address. Known possible values
		across all UV versions are::

			BASE IO
			PCIe IO
			PCIe SLOT
			NODE IO
			Riser
			PPB

		The location entry contains the location string of the UV Hub
		of the CPU physically connected to the corresponding PCI bus.

		The slot entry contains the physical slot number of the
		corresponding PCI bus. This value is used to physically locate
		PCI cards within a system.

		The ppb_addr entry contains the PCI address string of the
		bridged PCI bus. This entry is only present when the PCI bus
		object type is 'PPB'.

		The iio_stack entry contains a value describing the IIO stack
		number that the corresponding PCI bus object is connected to.
