What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/press_to_select
Date:		July 2011
Contact:	<EMAIL>
Description:	This controls if mouse clicks should be generated if the trackpoint is quickly pressed. How fast this press has to be
		is being controlled by press_speed.

		Values are 0 or 1.

		Applies to Thinkpad USB Keyboard with TrackPoint.

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/dragging
Date:		July 2011
Contact:	<EMAIL>
Description:	If this setting is enabled, it is possible to do dragging by pressing the trackpoint. This requires press_to_select to be enabled.

		Values are 0 or 1.

		Applies to Thinkpad USB Keyboard with TrackPoint.

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/release_to_select
Date:		July 2011
Contact:	<EMAIL>
Description:	For details regarding this setting please refer to http://www.pc.ibm.com/ww/healthycomputing/trkpntb.html
		Values are 0 or 1.
		Applies to Thinkpad USB Keyboard with TrackPoint.

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/select_right
Date:		July 2011
Contact:	<EMAIL>
Description:	This setting controls if the mouse click events generated by pressing the trackpoint (if press_to_select is enabled) generate
		a left or right mouse button click.

		Values are 0 or 1.

		Applies to Thinkpad USB Keyboard with TrackPoint.

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/sensitivity
Date:		July 2011
Contact:	<EMAIL>
Description:	This file contains the trackpoint sensitivity.
		Values are decimal integers from 1 (lowest sensitivity) to 255 (highest sensitivity).
		Applies to Thinkpad USB Keyboard with TrackPoint.

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/press_speed
Date:		July 2011
Contact:	<EMAIL>
Description:	This setting controls how fast the trackpoint needs to be pressed to generate a mouse click if press_to_select is enabled.

		Values are decimal integers from 1 (slowest) to 255 (fastest).

		Applies to Thinkpad USB Keyboard with TrackPoint.

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/fn_lock
Date:		July 2014
Contact:	<EMAIL>
Description:	This setting controls whether Fn Lock is enabled on the keyboard (i.e. if F1 is Mute or F1)

		Values are 0 or 1

		Applies to ThinkPad Compact (USB|Bluetooth) Keyboard with TrackPoint.
