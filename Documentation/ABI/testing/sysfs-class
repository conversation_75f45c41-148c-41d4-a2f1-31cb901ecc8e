What:		/sys/class/
Date:		February 2006
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/class directory will consist of a group of
		subdirectories describing individual classes of devices
		in the kernel.  The individual directories will consist
		of either subdirectories, or symlinks to other
		directories.

		All programs that use this directory tree must be able
		to handle both subdirectories or symlinks in order to
		work properly.

Users:
	udev <<EMAIL>>
