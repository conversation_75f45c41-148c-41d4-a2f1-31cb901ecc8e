What:		/sys/bus/scsi/drivers/st/debug_flag
Date:		October 2015
KernelVersion:	?.?
Contact:	<EMAIL>
Description:
		This file allows you to turn debug output from the st driver
		off if you write a '0' to the file or on if you write a '1'.
		Note that debug output requires that the module be compiled
		with the #define DEBUG set to a non-zero value (this is the
		default). If DEBUG is set to 0 then this file will not
		appear in sysfs as its presence is conditional upon debug
		output support being compiled into the module.
