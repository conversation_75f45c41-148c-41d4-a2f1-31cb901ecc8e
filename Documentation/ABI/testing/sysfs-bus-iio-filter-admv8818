What:		/sys/bus/iio/devices/iio:deviceX/filter_mode_available
KernelVersion:
Contact:	<EMAIL>
Description:
		Reading this returns the valid values that can be written to the
		filter_mode attribute:

		- auto -> Adjust bandpass filter to track changes in input clock rate.
		- manual -> disable/unregister the clock rate notifier / input clock tracking.
		- bypass -> bypass low pass filter, high pass filter and disable/unregister
								the clock rate notifier

What:		/sys/bus/iio/devices/iio:deviceX/filter_mode
KernelVersion:
Contact:	<EMAIL>
Description:
		This attribute configures the filter mode.
		Reading returns the actual mode.
