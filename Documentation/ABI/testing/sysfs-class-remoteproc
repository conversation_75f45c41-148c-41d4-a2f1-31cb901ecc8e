What:		/sys/class/remoteproc/.../firmware
Date:		October 2016
Contact:	<PERSON> <<EMAIL>>
Description:	Remote processor firmware

		Reports the name of the firmware currently loaded to the
		remote processor.

		To change the running firmware, ensure the remote processor is
		stopped (using /sys/class/remoteproc/.../state) and write a new filename.

What:		/sys/class/remoteproc/.../state
Date:		October 2016
Contact:	<PERSON> <<EMAIL>>
Description:	Remote processor state

		Reports the state of the remote processor, which will be one of:

		- "offline"
		- "suspended"
		- "running"
		- "crashed"
		- "invalid"

		"offline" means the remote processor is powered off.

		"suspended" means that the remote processor is suspended and
		must be woken to receive messages.

		"running" is the normal state of an available remote processor

		"crashed" indicates that a problem/crash has been detected on
		the remote processor.

		"invalid" is returned if the remote processor is in an
		unknown state.

		Writing this file controls the state of the remote processor.
		The following states can be written:

		- "start"
		- "stop"

		Writing "start" will attempt to start the processor running the
		firmware indicated by, or written to,
		/sys/class/remoteproc/.../firmware. The remote processor should
		transition to "running" state.

		Writing "stop" will attempt to halt the remote processor and
		return it to the "offline" state.

What:		/sys/class/remoteproc/.../name
Date:		August 2019
KernelVersion:	5.4
Contact:	Suman Anna <<EMAIL>>
Description:	Remote processor name

		Reports the name of the remote processor. This can be used by
		userspace in exactly identifying a remote processor and ease
		up the usage in modifying the 'firmware' or 'state' files.

What:		/sys/class/remoteproc/.../coredump
Date:		July 2020
Contact:	Bjorn Andersson <<EMAIL>>, Ohad Ben-Cohen <<EMAIL>>
Description:	Remote processor coredump configuration

		Reports the coredump configuration of the remote processor,
		which will be one of:

		"disabled"
		"enabled"
		"inline"

		"disabled" means no dump will be collected.

		"enabled" means when the remote processor's coredump is
		collected it will be copied to a separate buffer and that
		buffer is exposed to userspace.

		"inline" means when the remote processor's coredump is
		collected userspace will directly read from the remote
		processor's device memory. Extra buffer will not be used to
		copy the dump. Also recovery process will not proceed until
		all data is read by userspace.

What:		/sys/class/remoteproc/.../recovery
Date:		July 2020
Contact:	Bjorn Andersson <<EMAIL>>, Ohad Ben-Cohen <<EMAIL>>
Description:	Remote processor recovery mechanism

		Reports the recovery mechanism of the remote processor,
		which will be one of:

		"enabled"
		"disabled"

		"enabled" means, the remote processor will be automatically
		recovered whenever it crashes. Moreover, if the remote
		processor crashes while recovery is disabled, it will
		be automatically recovered too as soon as recovery is enabled.

		"disabled" means, a remote processor will remain in a crashed
		state if it crashes. This is useful for debugging purposes;
		without it, debugging a crash is substantially harder.
