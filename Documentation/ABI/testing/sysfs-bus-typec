What:		/sys/bus/typec/devices/.../active
Date:		July 2018
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		Shows if the mode is active or not. The attribute can be used
		for entering/exiting the mode. Entering/exiting modes is
		supported as synchronous operation so write(2) to the attribute
		does not return until the enter/exit mode operation has
		finished. The attribute is notified when the mode is
		entered/exited so poll(2) on the attribute wakes up.
		Entering/exiting a mode will also generate uevent KOBJ_CHANGE.

		Valid values are boolean.

What:		/sys/bus/typec/devices/.../description
Date:		July 2018
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		Shows description of the mode. The description is optional for
		the drivers, just like with the Billboard Devices.

What:		/sys/bus/typec/devices/.../mode
Date:		July 2018
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		The index number of the mode returned by Discover Modes USB
		Power Delivery command. Depending on the alternate mode, the
		mode index may be significant.

		With some alternate modes (SVIDs), the mode index is assigned
		for specific functionality in the specification for that
		alternate mode.

		With other alternate modes, the mode index values are not
		assigned, and can not be therefore used for identification. When
		the mode index is not assigned, identifying the alternate mode
		must be done with either mode VDO or the description.

What:		/sys/bus/typec/devices/.../svid
Date:		July 2018
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The Standard or Vendor ID (SVID) assigned by USB-IF for this
		alternate mode.

What:		/sys/bus/typec/devices/.../vdo
Date:		July 2018
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows the VDO in hexadecimal returned by Discover Modes command
		for this mode.
