What:		/sys/fs/erofs/features/
Date:		November 2021
Contact:	"<PERSON>" <huang<PERSON><PERSON><PERSON>@oppo.com>
Description:	Shows all enabled kernel features.
		Supported features:
		zero_padding, compr_cfgs, big_pcluster, chunked_file,
		device_table, compr_head2, sb_chksum, ztailpacking,
		dedupe, fragments.

What:		/sys/fs/erofs/<disk>/sync_decompress
Date:		November 2021
Contact:	"<PERSON>" <<EMAIL>>
Description:	Control strategy of sync decompression:

		- 0 (default, auto): enable for readpage, and enable for
		  readahead on atomic contexts only.
		- 1 (force on): enable for readpage and readahead.
		- 2 (force off): disable for all situations.
