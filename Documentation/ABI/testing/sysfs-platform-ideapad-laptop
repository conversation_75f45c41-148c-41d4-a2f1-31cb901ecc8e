What:		/sys/bus/platform/devices/VPC2004:*/camera_power
Date:		Dec 2010
KernelVersion:	2.6.37
Contact:	"<PERSON><PERSON> <<EMAIL>>"
Description:
		Control the power of camera module. 1 means on, 0 means off.

What:		/sys/bus/platform/devices/VPC2004:*/fan_mode
Date:		June 2012
KernelVersion:	3.6
Contact:	"Maxim Mikityanskiy <<EMAIL>>"
Description:
		Change fan mode
		There are four available modes:

			* 0 -> Super Silent Mode
			* 1 -> Standard Mode
			* 2 -> Dust Cleaning
			* 4 -> Efficient Thermal Dissipation Mode

What:		/sys/bus/platform/devices/VPC2004:*/touchpad
Date:		May 2017
KernelVersion:	4.13
Contact:	"Ritesh Raj <PERSON>rraf <<EMAIL>>"
Description:
		Control touchpad mode.
			* 1 -> Switched On
			* 0 -> Switched Off

What:		/sys/bus/platform/devices/VPC2004:*/conservation_mode
Date:		Aug 2017
KernelVersion:	4.14
Contact:	<EMAIL>
Description:
		Controls whether the conservation mode is enabled or not.
		This feature limits the maximum battery charge percentage to
		around 50-60% in order to prolong the lifetime of the battery.

What:		/sys/bus/platform/devices/VPC2004:*/fn_lock
Date:		May 2018
KernelVersion:	4.18
Contact:	"Oleg Keri <<EMAIL>>"
Description:
		Control fn-lock mode.

			* 1 -> Switched On
			* 0 -> Switched Off

		For example::

		  # echo "0" >	\
		  /sys/bus/pci/devices/0000:00:1f.0/PNP0C09:00/VPC2004:00/fn_lock

What:		/sys/bus/platform/devices/VPC2004:*/usb_charging
Date:		Feb 2021
KernelVersion:	5.12
Contact:	<EMAIL>
Description:
		Controls whether the "always on USB charging" feature is
		enabled or not. This feature enables charging USB devices
		even if the computer is not turned on.
