What:		/sys/kernel/mm/mempolicy/weighted_interleave/
Date:		January 2024
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Configuration Interface for the Weighted Interleave policy

What:		/sys/kernel/mm/mempolicy/weighted_interleave/nodeN
Date:		January 2024
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Weight configuration interface for nodeN

		The interleave weight for a memory node (N). These weights are
		utilized by tasks which have set their mempolicy to
		MPOL_WEIGHTED_INTERLEAVE.

		These weights only affect new allocations, and changes at runtime
		will not cause migrations on already allocated pages.

		The minimum weight for a node is always 1.

		Minimum weight: 1
		Maximum weight: 255

		Writing an empty string or `0` will reset the weight to the
		system default. The system default may be set by the kernel
		or drivers at boot or during hotplug events.
