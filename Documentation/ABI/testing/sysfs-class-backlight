What:		/sys/class/backlight/<backlight>/scale
Date:		July 2019
KernelVersion:	5.4
Contact:	<PERSON> <<EMAIL>>
Description:
		Description of the scale of the brightness curve.

		The human eye senses brightness approximately logarithmically,
		hence linear changes in brightness are perceived as being
		non-linear. To achieve a linear perception of brightness changes
		controls like sliders need to apply a logarithmic mapping for
		backlights with a linear brightness curve.

		Possible values of the attribute are:

		unknown
		  The scale of the brightness curve is unknown.

		linear
		  The brightness changes linearly with each step. Brightness
		  controls should apply a logarithmic mapping for a linear
		  perception.

		non-linear
		  The brightness changes non-linearly with each step. Brightness
		  controls should use a linear mapping for a linear perception.

What:		/sys/class/backlight/<backlight>/ambient_light_level
Date:		Apr, 2010
KernelVersion:	v2.6.35
Contact:	<PERSON> <<EMAIL>>
Description:
		(RO) Get conversion value of the light sensor.

		The value is automatically updated every 80 ms when the
		light sensor is enabled.

		The value range is device-driver specific:

		For ADP8870:

		  It returns integer between 0 (dark) and 8000 (max ambient
		  brightness).

		For ADP8860:

		  It returns a 13-bits integer.

What:		/sys/class/backlight/<backlight>/ambient_light_zone
Date:		Apr, 2010
KernelVersion:	v2.6.35
Contact:	<PERSON> <<EMAIL>>,
		<EMAIL>

Description:
		(RW) Read or write the specific brightness level at which the
		backlight operates.

		The value meaning is device-driver specific:

		For ADP8860:

		  ==	==========================
		   0	Off: Backlight set to 0 mA
		   1	Level 1: daylight
		   2	Level 2: bright
		   3	Level 3: dark
		  ==	==========================

		For ADP8870:

		  ==	==========================
		   0	Off: Backlight set to 0 mA
		   1	Level 1: daylight
		   2	Level 2: bright
		   3	Level 3: office
		   4	Level 4: indoor
		   5	Level 5: dark
		  ==	==========================

		Writing 0 returns to normal/automatic ambient light level
		operation.

		It can be enabled by writing the value stored in
		/sys/class/backlight/<backlight>/max_brightness to
		/sys/class/backlight/<backlight>/brightness.

What:		/sys/class/backlight/<backlight>/<ambient light zone>_max
Date:		Sep, 2009
KernelVersion:	v2.6.32
Contact:	<EMAIL>
Description:
		Control the maximum brightness for <ambient light zone>
		on this <backlight>. Values are between 0 and 127. This file
		will also show the brightness level stored for this
		<ambient light zone>.

		The <ambient light zone> is device-driver specific:

		For ADP5520 and ADP5501, <ambient light zone> can be:

		===========  ================================================
		Ambient      sysfs entry
		light zone
		===========  ================================================
		daylight     /sys/class/backlight/<backlight>/daylight_max
		office       /sys/class/backlight/<backlight>/office_max
		dark         /sys/class/backlight/<backlight>/dark_max
		===========  ================================================

		For ADP8860, <ambient light zone> can be:

		===========  ================================================
		Ambient      sysfs entry
		light zone
		===========  ================================================
		l1_daylight  /sys/class/backlight/<backlight>/l1_daylight_max
		l2_office    /sys/class/backlight/<backlight>/l2_office_max
		l3_dark      /sys/class/backlight/<backlight>/l3_dark_max
		===========  ================================================

		For ADP8870, <ambient light zone> can be:

		===========  ================================================
		Ambient      sysfs entry
		light zone
		===========  ================================================
		l1_daylight  /sys/class/backlight/<backlight>/l1_daylight_max
		l2_bright    /sys/class/backlight/<backlight>/l2_bright_max
		l3_office    /sys/class/backlight/<backlight>/l3_office_max
		l4_indoor    /sys/class/backlight/<backlight>/l4_indoor_max
		l5_dark      /sys/class/backlight/<backlight>/l5_dark_max
		===========  ================================================

		See also: /sys/class/backlight/<backlight>/ambient_light_zone.

What:		/sys/class/backlight/<backlight>/<ambient light zone>_dim
Date:		Sep, 2009
KernelVersion:	v2.6.32
Contact:	<EMAIL>
Description:
		Control the dim brightness for <ambient light zone>
		on this <backlight>. Values are between 0 and 127, typically
		set to 0. Full off when the backlight is disabled.
		This file will also show the dim brightness level stored for
		this <ambient light zone>.

		The <ambient light zone> is device-driver specific:

		For ADP5520 and ADP5501, <ambient light zone> can be:

		===========  ================================================
		Ambient      sysfs entry
		light zone
		===========  ================================================
		daylight     /sys/class/backlight/<backlight>/daylight_dim
		office       /sys/class/backlight/<backlight>/office_dim
		dark         /sys/class/backlight/<backlight>/dark_dim
		===========  ================================================

		For ADP8860, <ambient light zone> can be:

		===========  ================================================
		Ambient      sysfs entry
		light zone
		===========  ================================================
		l1_daylight  /sys/class/backlight/<backlight>/l1_daylight_dim
		l2_office    /sys/class/backlight/<backlight>/l2_office_dim
		l3_dark      /sys/class/backlight/<backlight>/l3_dark_dim
		===========  ================================================

		For ADP8870, <ambient light zone> can be:

		===========  ================================================
		Ambient      sysfs entry
		light zone
		===========  ================================================
		l1_daylight  /sys/class/backlight/<backlight>/l1_daylight_dim
		l2_bright    /sys/class/backlight/<backlight>/l2_bright_dim
		l3_office    /sys/class/backlight/<backlight>/l3_office_dim
		l4_indoor    /sys/class/backlight/<backlight>/l4_indoor_dim
		l5_dark      /sys/class/backlight/<backlight>/l5_dark_dim
		===========  ================================================

		See also: /sys/class/backlight/<backlight>/ambient_light_zone.

