What:		/sys/bus/nvmem/devices/.../cells/<cell-name>
Date:		May 2023
KernelVersion:	6.5
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The "cells" folder contains one file per cell exposed by the
		NVMEM device. The name of the file is: "<name>@<byte>,<bit>",
		with <name> being the cell name and <where> its location in
		the NVMEM device, in hexadecimal bytes and bits (without the
		'0x' prefix, to mimic device tree node names). The length of
		the file is the size of the cell (when known). The content of
		the file is the binary content of the cell (may sometimes be
		ASCII, likely without trailing character).
		Note: This file is only present if CONFIG_NVMEM_SYSFS
		is enabled.

		Example::

		  hexdump -C /sys/bus/nvmem/devices/1-00563/cells/product-name@d,0
		  00000000  54 4e 34 38 4d 2d 50 2d  44 4e         |TN48M-P-DN|
		  0000000a
