What:		/sys/devices/LNXSYSTM:00/LNXSYBUS:00/TOS620A:00/protection_level
Date:		August 16, 2014
KernelVersion:	3.17
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:	This file controls the built-in accelerometer protection level,
		valid values are:

			* 0 -> Disabled
			* 1 -> Low
			* 2 -> Medium
			* 3 -> High

		The default potection value is set to 2 (Medium).
Users:		KT<PERSON>ba

What:		/sys/devices/LNXSYSTM:00/LNXSYBUS:00/TOS620A:00/reset_protection
Date:		August 16, 2014
KernelVersion:	3.17
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:	This file turns off the built-in accelerometer for a few
		seconds and then restore normal operation. Accepting 1 as the
		only parameter.
