What:		/sys/class/scsi_host/hostX/isci_id
Date:		June 2011
Contact:	<PERSON> <<EMAIL>>
Description:
		This file contains the enumerated host ID for the Intel
		SCU controller. The Intel(R) C600 Series Chipset SATA/SAS
		Storage Control Unit embeds up to two 4-port controllers in
		a single PCI device.  The controllers are enumerated in order
		which usually means the lowest number scsi_host corresponds
		with the first controller, but this association is not
		guaranteed.  The 'isci_id' attribute unambiguously identifies
		the controller index: '0' for the first controller,
		'1' for the second.

What:		/sys/class/scsi_host/hostX/acciopath_status
Date:		November 2013
Contact:	<PERSON> <<EMAIL>>
Description:	This file contains the current status of the "SSD Smart Path"
		feature of HP Smart Array RAID controllers using the hpsa
		driver.  SSD Smart Path, when enabled permits the driver to
		send i/o requests directly to physical devices that are part
		of a logical drive, bypassing the controllers firmware RAID
		stack for a performance advantage when possible.  A value of
		'1' indicates the feature is enabled, and the controller may
		use the direct i/o path to physical devices.  A value of zero
		means the feature is disabled and the controller may not use
		the direct i/o path to physical devices.  This setting is
		controller wide, affecting all configured logical drives on the
		controller.  This file is readable and writable.

What:		/sys/class/scsi_host/hostX/link_power_management_policy
Date:		Oct, 2007
KernelVersion:	v2.6.24
Contact:	<EMAIL>
Description:
		(RW) This parameter allows the user to read and set the link
		(interface) power management.

		There are four possible options:

		min_power: Tell the controller to try to make the link use the
		least possible power when possible. This may sacrifice some
		performance due to increased latency when coming out of lower
		power states.

		max_performance: Generally, this means no power management.
		Tell the controller to have performance be a priority over power
		management.

		medium_power: Tell the controller to enter a lower power state
		when possible, but do not enter the lowest power state, thus
		improving latency over min_power setting.

		med_power_with_dipm: Identical to the existing medium_power
		setting except that it enables dipm (device initiated power
		management) on top, which makes it match the Windows IRST (Intel
		Rapid Storage Technology) driver settings. This setting is also
		close to min_power, except that:

		a) It does not use host-initiated slumber mode, but it does
		   allow device-initiated slumber
		b) It does not enable low power device sleep mode (DevSlp).

What:		/sys/class/scsi_host/hostX/em_message
What:		/sys/class/scsi_host/hostX/em_message_type
Date:		Jun, 2008
KernelVersion:	v2.6.27
Contact:	<EMAIL>
Description:
		em_message: (RW) Enclosure management support. For the LED
		protocol, writes and reads correspond to the LED message format
		as defined in the AHCI spec.

		The user must turn sw_activity (under `/sys/block/*/device/`)
		OFF it they wish to control the activity LED via the em_message
		file.

		em_message_type: (RO) Displays the current enclosure management
		protocol that is being used by the driver (for eg. LED, SAF-TE,
		SES-2, SGPIO etc).

What:		/sys/class/scsi_host/hostX/ahci_port_cmd
What:		/sys/class/scsi_host/hostX/ahci_host_caps
What:		/sys/class/scsi_host/hostX/ahci_host_cap2
Date:		Mar, 2010
KernelVersion:	v2.6.35
Contact:	<EMAIL>
Description:
		[to be documented]

What:		/sys/class/scsi_host/hostX/ahci_host_version
Date:		Mar, 2010
KernelVersion:	v2.6.35
Contact:	<EMAIL>
Description:
		(RO) Display the version of the AHCI spec implemented by the
		host.

What:		/sys/class/scsi_host/hostX/em_buffer
Date:		Apr, 2010
KernelVersion:	v2.6.35
Contact:	<EMAIL>
Description:
		(RW) Allows access to AHCI EM (enclosure management) buffer
		directly if the host supports EM.

		For eg. the AHCI driver supports SGPIO EM messages but the
		SATA/AHCI specs do not define the SGPIO message format of the EM
		buffer. Different hardware(HW) vendors may have different
		definitions. With the em_buffer attribute, this issue can be
		solved by allowing HW vendors to provide userland drivers and
		tools for their SGPIO initiators.

What:		/sys/class/scsi_host/hostX/em_message_supported
Date:		Oct, 2009
KernelVersion:	v2.6.39
Contact:	<EMAIL>
Description:
		(RO) Displays supported enclosure management message types.
