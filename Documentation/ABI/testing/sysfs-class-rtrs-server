What:		/sys/class/rtrs-server
Date:		Feb 2020
KernelVersion:	5.7
Contact:	<PERSON> <<EMAIL>> <PERSON><PERSON> <<EMAIL>>
Description:	When a user of RTRS API creates a new session on a client side, a
		directory entry with the name of that session is created in here.

What:		/sys/class/rtrs-server/<session-name>/paths/
Date:		Feb 2020
KernelVersion:	5.7
Contact:	<PERSON> <<EMAIL>> <PERSON><PERSON> <<EMAIL>>
Description:	When new path is created by writing to "add_path" entry on client side,
		a directory entry named as <source address>@<destination address> is created
		on server.

What:		/sys/class/rtrs-server/<session-name>/paths/<src@dst>/disconnect
Date:		Feb 2020
KernelVersion:	5.7
Contact:	<PERSON> <<EMAIL>> <PERSON><PERSON> <<EMAIL>>
Description:	When "1" is written to the file, the RTRS session is being disconnected.
		Operations is non-blocking and returns control immediately to the caller.

What:		/sys/class/rtrs-server/<session-name>/paths/<src@dst>/hca_name
Date:		Feb 2020
KernelVersion:	5.7
Contact:	<PERSON> <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	RO, Contains the name of HCA the connection established on.

What:		/sys/class/rtrs-server/<session-name>/paths/<src@dst>/hca_port
Date:		Feb 2020
KernelVersion:	5.7
Contact:	Jack Wang <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	RO, Contains the port number of active port traffic is going through.

What:		/sys/class/rtrs-server/<session-name>/paths/<src@dst>/src_addr
Date:		Feb 2020
KernelVersion:	5.7
Contact:	Jack Wang <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	RO, Contains the source address of the path

What:		/sys/class/rtrs-server/<session-name>/paths/<src@dst>/dst_addr
Date:		Feb 2020
KernelVersion:	5.7
Contact:	Jack Wang <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	RO, Contains the destination address of the path

What:		/sys/class/rtrs-server/<session-name>/paths/<src@dst>/stats/rdma
Date:		Feb 2020
KernelVersion:	5.7
Contact:	Jack Wang <<EMAIL>> Danil Kipnis <<EMAIL>>
Description:	Contains statistics regarding rdma operations and inflight operations.
		The output consists of 5 values:
		<read-count> <read-total-size> <write-count> <write-total-size> <inflights>
