What:		/sys/dev
Date:		April 2008
KernelVersion:	2.6.26
Contact:	<PERSON> <<EMAIL>>
Description:	The /sys/dev tree provides a method to look up the sysfs
		path for a device using the information returned from
		stat(2).  There are two directories, 'block' and 'char',
		beneath /sys/dev containing symbolic links with names of
		the form "<major>:<minor>".  These links point to the
		corresponding sysfs path for the given device.

		Example::

		  $ readlink /sys/dev/block/8:32
		  ../../block/sdc

		Entries in /sys/dev/char and /sys/dev/block will be
		dynamically created and destroyed as devices enter and
		leave the system.

Users:		mdadm <<EMAIL>>
