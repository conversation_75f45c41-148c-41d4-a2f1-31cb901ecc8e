What:		/sys/module/pch_phub/drivers/.../pch_mac
Date:		August 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:	Write/read GbE MAC address.

What:		/sys/module/pch_phub/drivers/.../pch_firmware
Date:		August 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:	Write/read Option ROM data.


What:		/sys/module/ehci_hcd/drivers/.../uframe_periodic_max
Date:		July 2011
KernelVersion:	3.1
Contact:	<PERSON><PERSON>kov <<EMAIL>>
Description:	Maximum time allowed for periodic transfers per microframe (μs)

		Note:
		  USB 2.0 sets maximum allowed time for periodic transfers per
		  microframe to be 80%, that is 100 microseconds out of 125
		  microseconds (full microframe).

		  However there are cases, when 80% max isochronous bandwidth is
		  too limiting. For example two video streams could require 110
		  microseconds of isochronous bandwidth per microframe to work
		  together.

		Through this setting it is possible to raise the limit so that
		the host controller would allow allocating more than 100
		microseconds of periodic bandwidth per microframe.

		Beware, non-standard modes are usually not thoroughly tested by
		hardware designers, and the hardware can malfunction when this
		setting differ from default 100.

What:		/sys/module/*/{coresize,initsize}
Date:		Jan 2012
KernelVersion:	3.3
Contact:	Kay Sievers <<EMAIL>>
Description:	Module size in bytes.

What:		/sys/module/*/initstate
Date:		Nov 2006
KernelVersion:	2.6.19
Contact:	Kay Sievers <<EMAIL>>
Description:	Show the initialization state(live, coming, going) of
		the module.

What:		/sys/module/*/taint
Date:		Jan 2012
KernelVersion:	3.3
Contact:	Kay Sievers <<EMAIL>>
Description:	Module taint flags:
			==  =====================
			P   proprietary module
			O   out-of-tree module
			F   force-loaded module
			C   staging driver module
			E   unsigned module
			==  =====================

What:		/sys/module/grant_table/parameters/free_per_iteration
Date:		July 2023
KernelVersion:	6.5 but backported to all supported stable branches
Contact:	Xen developer discussion <<EMAIL>>
Description:	Read and write number of grant entries to attempt to free per iteration.

		Note: Future versions of Xen and Linux may provide a better
		interface for controlling the rate of deferred grant reclaim
		or may not need it at all.
Users:		Qubes OS (https://www.qubes-os.org)
