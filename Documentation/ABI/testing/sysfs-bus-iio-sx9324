What:		/sys/bus/iio/devices/iio:deviceX/in_proximity<id>_setup
Date:		November 2021
KernelVersion:	5.17
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		SX9324 has 3 inputs, CS0, CS1 and CS2. Hardware layout
		defines if the input is

		+ not connected (HZ),
		+ grounded (GD),
		+ connected to an antenna where it can act as a base
		  (DS - data shield), or measured input (MI).

		The sensor rotates measurement across 4 phases
		(PH0, PH1, PH2, PH3), where the inputs are configured
		and then measured.

		By default,  during the first phase, [PH0], CS0 is measured,
		while CS1 and CS2 are used as shields.
		`cat in_proximity0_setup` returns "MI,DS,DS".
		[PH1], CS1 is measured, CS0 and CS2 are shield:
		`cat in_proximity1_setup` returns "DS,MI,DS".
		[PH2], CS2 is measured, CS0 and CS1 are shield:
		`cat in_proximity1_setup` returns "DS,DS,MI".
		[PH3], CS1 and CS2 are measured (combo mode):
		`cat in_proximity1_setup` returns "DS,MI,MI".

		Note, these are the chip default. Hardware layout will most
		likely dictate different output. The entry is read-only.
