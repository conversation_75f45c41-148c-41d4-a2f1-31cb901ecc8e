What:           /sys/bus/event_source/devices/hv_24x7/format
Date:           September 2020
Contact:        Linux on PowerPC Developer List <<EMAIL>>
Description:    Read-only. Attribute group to describe the magic bits
                that go into perf_event_attr.config for a particular pmu.
                (See ABI/testing/sysfs-bus-event_source-devices-format).

                Each attribute under this group defines a bit range of the
                perf_event_attr.config. All supported attributes are listed
                below::

				chip = "config:16-31"
				core  = "config:16-31"
				domain = "config:0-3"
				lpar = "config:0-15"
				offset = "config:32-63"
				vcpu = "config:16-31"

                For example::

		  PM_PB_CYC =  "domain=1,offset=0x80,chip=?,lpar=0x0"

		In this event, '?' after chip specifies that
		this value will be provided by user while running this event.

What:		/sys/bus/event_source/devices/hv_24x7/interface/catalog
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		Provides access to the binary "24x7 catalog" provided by the
		hypervisor on POWER7 and 8 systems. This catalog lists events
		available from the powerpc "hv_24x7" pmu. Its format is
		documented here:
		https://raw.githubusercontent.com/jmesmon/catalog-24x7/master/hv-24x7-catalog.h

What:		/sys/bus/event_source/devices/hv_24x7/interface/catalog_length
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		A number equal to the length in bytes of the catalog. This is
		also extractable from the provided binary "catalog" sysfs entry.

What:		/sys/bus/event_source/devices/hv_24x7/interface/catalog_version
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		Exposes the "version" field of the 24x7 catalog. This is also
		extractable from the provided binary "catalog" sysfs entry.

What:		/sys/devices/hv_24x7/interface/sockets
Date:		May 2020
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	read only
		This sysfs interface exposes the number of sockets present in the
		system.

What:		/sys/devices/hv_24x7/interface/chipspersocket
Date:		May 2020
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	read only
		This sysfs interface exposes the number of chips per socket
		present in the system.

What:		/sys/devices/hv_24x7/interface/coresperchip
Date:		May 2020
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	read only
		This sysfs interface exposes the number of cores per chip
		present in the system.

What:		/sys/devices/hv_24x7/cpumask
Date:		July 2020
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:	read only
		This sysfs file exposes the cpumask which is designated to make
		HCALLs to retrieve hv-24x7 pmu event counter data.

What:		/sys/bus/event_source/devices/hv_24x7/event_descs/<event-name>
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		Provides the description of a particular event as provided by
		the firmware. If firmware does not provide a description, no
		file will be created.

		Note that the event-name lacks the domain suffix appended for
		events in the events/ dir.

What:		/sys/bus/event_source/devices/hv_24x7/event_long_descs/<event-name>
Date:		February 2014
Contact:	Linux on PowerPC Developer List <<EMAIL>>
Description:
		Provides the "long" description of a particular event as
		provided by the firmware. If firmware does not provide a
		description, no file will be created.

		Note that the event-name lacks the domain suffix appended for
		events in the events/ dir.
