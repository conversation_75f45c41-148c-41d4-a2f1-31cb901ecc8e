What:		/sys/bus/iio/devices/iio:deviceX/out_currentY_toggle_en
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Toggle enable. Write 1 to enable toggle or 0 to disable it. This
		is useful when one wants to change the DAC output codes. For
		autonomous toggling, the way it should be done is:

		- disable toggle operation;
		- change out_currentY_rawN, where N is the integer value of the symbol;
		- enable toggle operation.

What:		/sys/bus/iio/devices/iio:deviceX/out_currentY_rawN
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		This attribute has the same meaning as out_currentY_raw. It is
		specific to toggle enabled channels and refers to the DAC output
		code in INPUT_N (_rawN), where N is the integer value of the symbol.
		The same scale and offset as in out_currentY_raw applies.

What:		/sys/bus/iio/devices/iio:deviceX/out_currentY_symbol
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Performs a SW switch to a predefined output symbol. This attribute
		is specific to toggle enabled channels and allows switching between
		multiple predefined symbols. Each symbol corresponds to a different
		output, denoted as out_currentY_rawN, where N is the integer value
		of the symbol. Writing an integer value N will select out_currentY_rawN.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_toggle_en
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Toggle enable. Write 1 to enable toggle or 0 to disable it. This
		is useful when one wants to change the DAC output codes. For
		autonomous toggling, the way it should be done is:

		- disable toggle operation;
		- change out_voltageY_rawN, where N is the integer value of the symbol;
		- enable toggle operation.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_rawN
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		This attribute has the same meaning as out_currentY_raw. It is
		specific to toggle enabled channels and refers to the DAC output
		code in INPUT_N (_rawN), where N is the integer value of the symbol.
		The same scale and offset as in out_currentY_raw applies.

What:		/sys/bus/iio/devices/iio:deviceX/out_voltageY_symbol
KernelVersion:	5.18
Contact:	<EMAIL>
Description:
		Performs a SW switch to a predefined output symbol. This attribute
		is specific to toggle enabled channels and allows switching between
		multiple predefined symbols. Each symbol corresponds to a different
		output, denoted as out_voltageY_rawN, where N is the integer value
		of the symbol. Writing an integer value N will select out_voltageY_rawN.
