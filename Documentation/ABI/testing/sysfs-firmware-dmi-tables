What:		/sys/firmware/dmi/tables/
Date:		April 2015
Contact:	<PERSON> <ivan.khoron<PERSON>hu<PERSON>@globallogic.com>
Description:
		The firmware provides DMI structures as a packed list of
		data referenced by a SMBIOS table entry point. The SMBIOS
		entry point contains general information, like SMBIOS
		version, DMI table size, etc. The structure, content and
		size of SMBIOS entry point is dependent on SMBIOS version.
		The format of SMBIOS entry point and DMI structures
		can be read in SMBIOS specification.

		The dmi/tables provides raw SMBIOS entry point and DMI tables
		through sysfs as an alternative to utilities reading them
		from /dev/mem. The raw SMBIOS entry point and DMI table are
		presented as binary attributes and are accessible via:

		/sys/firmware/dmi/tables/smbios_entry_point
		/sys/firmware/dmi/tables/DMI

		The complete DMI information can be obtained using these two
		tables.
