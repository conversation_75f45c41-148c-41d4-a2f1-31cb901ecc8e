What:		/sys/bus/iio/devices/iio:deviceX/in_proximity_nearlevel
Date:		March 2020
KernelVersion:	5.7
Contact:	<EMAIL>
Description:
		Near level for proximity sensors. This is a single integer
		value that tells user space when an object should be
		considered close to the device. If the value read from the
		sensor is above or equal to the value in this file an object
		should typically be considered near.

What:		/sys/bus/iio/devices/iio:deviceX/sensor_sensitivity
Date:		March 2014
KernelVersion:	3.15
Contact:	<EMAIL>
Description:
		Proximity sensors sometimes have a controllable amplifier
		on the signal from which time of flight measurements are
		taken.
		The appropriate values to take is dependent on both the
		sensor and its operating environment:
		* as3935 (0-31 range)
		18 = indoors (default)
		14 = outdoors
