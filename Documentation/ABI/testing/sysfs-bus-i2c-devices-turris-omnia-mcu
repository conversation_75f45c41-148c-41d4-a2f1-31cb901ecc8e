What:		/sys/bus/i2c/devices/<mcu_device>/board_revision
Date:		September 2024
KernelVersion:	6.11
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RO) Contains board revision number.

		Only available if board information is burned in the MCU (older
		revisions have board information burned in the ATSHA204-A chip).

		Format: %u.

What:		/sys/bus/i2c/devices/<mcu_device>/first_mac_address
Date:		September 2024
KernelVersion:	6.11
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RO) Contains device first MAC address. Each Turris Omnia is
		allocated 3 MAC addresses. The two additional addresses are
		computed from the first one by incrementing it.

		Only available if board information is burned in the MCU (older
		revisions have board information burned in the ATSHA204-A chip).

		Format: %pM.

What:		/sys/bus/i2c/devices/<mcu_device>/front_button_mode
Date:		September 2024
KernelVersion:	6.11
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) The front button on the Turris Omnia router can be
		configured either to change the intensity of all the LEDs on the
		front panel, or to send the press event to the CPU as an
		interrupt.

		This file switches between these two modes:
		 - ``mcu`` makes the button press event be handled by the MCU to
		   change the LEDs panel intensity.
		 - ``cpu`` makes the button press event be handled by the CPU.

		Format: %s.

What:		/sys/bus/i2c/devices/<mcu_device>/front_button_poweron
Date:		September 2024
KernelVersion:	6.11
Contact:	Marek Behún <<EMAIL>>
Description:	(RW) Newer versions of the microcontroller firmware of the
		Turris Omnia router support powering off the router into true
		low power mode. The router can be powered on by pressing the
		front button.

		This file configures whether front button power on is enabled.

		This file is present only if the power off feature is supported
		by the firmware.

		Format: %i.

What:		/sys/bus/i2c/devices/<mcu_device>/fw_features
Date:		September 2024
KernelVersion:	6.11
Contact:	Marek Behún <<EMAIL>>
Description:	(RO) Newer versions of the microcontroller firmware report the
		features they support. These can be read from this file. If the
		MCU firmware is too old, this file reads 0x0.

		Format: 0x%x.

What:		/sys/bus/i2c/devices/<mcu_device>/fw_version_hash_application
Date:		September 2024
KernelVersion:	6.11
Contact:	Marek Behún <<EMAIL>>
Description:	(RO) Contains the version hash (commit hash) of the application
		part of the microcontroller firmware.

		Format: %s.

What:		/sys/bus/i2c/devices/<mcu_device>/fw_version_hash_bootloader
Date:		September 2024
KernelVersion:	6.11
Contact:	Marek Behún <<EMAIL>>
Description:	(RO) Contains the version hash (commit hash) of the bootloader
		part of the microcontroller firmware.

		Format: %s.

What:		/sys/bus/i2c/devices/<mcu_device>/mcu_type
Date:		September 2024
KernelVersion:	6.11
Contact:	Marek Behún <<EMAIL>>
Description:	(RO) Contains the microcontroller type (STM32, GD32, MKL).

		Format: %s.

What:		/sys/bus/i2c/devices/<mcu_device>/reset_selector
Date:		September 2024
KernelVersion:	6.11
Contact:	Marek Behún <<EMAIL>>
Description:	(RO) Contains the selected factory reset level, determined by
		how long the rear reset button was held by the user during board
		reset.

		Format: %i.

What:		/sys/bus/i2c/devices/<mcu_device>/serial_number
Date:		September 2024
KernelVersion:	6.11
Contact:	Marek Behún <<EMAIL>>
Description:	(RO) Contains the 64-bit board serial number in hexadecimal
		format.

		Only available if board information is burned in the MCU (older
		revisions have board information burned in the ATSHA204-A chip).

		Format: %016X.
