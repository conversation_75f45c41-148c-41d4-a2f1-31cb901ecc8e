What:		/sys/class/timecard/
Date:		September 2021
Contact:	<PERSON> <<EMAIL>>
Description:	This directory contains files and directories
		providing a standardized interface to the ancillary
		features of the OpenCompute timecard.

What:		/sys/class/timecard/ocpN/
Date:		September 2021
Contact:	<PERSON> <<EMAIL>>
Description:	This directory contains the attributes of the Nth timecard
		registered.

What:		/sys/class/timecard/ocpN/available_clock_sources
Date:		September 2021
Contact:	<PERSON> <<EMAIL>>
Description:	(RO) The list of available time sources that the PHC
		uses for clock adjustments.

		====  =================================================
                NONE  no adjustments
                PPS   adjustments come from the PPS1 selector (default)
                TOD   adjustments from the GNSS/TOD module
                IRIG  adjustments from external IRIG-B signal
                DCF   adjustments from external DCF signal
                ====  =================================================

What:		/sys/class/timecard/ocpN/available_sma_inputs
Date:		September 2021
Contact:	<PERSON> <<EMAIL>>
Description:	(RO) Set of available destinations (sinks) for a SMA
		input signal.

                =====  ================================================
                10Mhz  signal is used as the 10Mhz reference clock
                PPS1   signal is sent to the PPS1 selector
                PPS2   signal is sent to the PPS2 selector
                TS1    signal is sent to timestamper 1
                TS2    signal is sent to timestamper 2
                TS3    signal is sent to timestamper 3
                TS4    signal is sent to timestamper 4
                IRIG   signal is sent to the IRIG-B module
                DCF    signal is sent to the DCF module
                FREQ1  signal is sent to frequency counter 1
                FREQ2  signal is sent to frequency counter 2
                FREQ3  signal is sent to frequency counter 3
                FREQ4  signal is sent to frequency counter 4
                None   signal input is disabled
                =====  ================================================

What:		/sys/class/timecard/ocpN/available_sma_outputs
Date:		May 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Set of available sources for a SMA output signal.

                =====  ================================================
                10Mhz  output is from the 10Mhz reference clock
                PHC    output PPS is from the PHC clock
                MAC    output PPS is from the Miniature Atomic Clock
                GNSS1  output PPS is from the first GNSS module
                GNSS2  output PPS is from the second GNSS module
                IRIG   output is from the PHC, in IRIG-B format
                DCF    output is from the PHC, in DCF format
                GEN1   output is from frequency generator 1
                GEN2   output is from frequency generator 2
                GEN3   output is from frequency generator 3
                GEN4   output is from frequency generator 4
                GND    output is GND
                VCC    output is VCC
                =====  ================================================

What:		/sys/class/timecard/ocpN/clock_source
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RW) Contains the current synchronization source used by
		the PHC.  May be changed by writing one of the listed
		values from the available_clock_sources attribute set.

What:		/sys/class/timecard/ocpN/clock_status_drift
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Contains the current drift value used by the firmware
		for internal disciplining of the atomic clock.

What:		/sys/class/timecard/ocpN/clock_status_offset
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Contains the current offset value used by the firmware
		for internal disciplining of the atomic clock.

What:		/sys/class/timecard/ocpN/freqX
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Optional directory containing the sysfs nodes for
		frequency counter <X>.

What:		/sys/class/timecard/ocpN/freqX/frequency
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Contains the measured frequency over the specified
		measurement period.

What:		/sys/class/timecard/ocpN/freqX/seconds
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RW) Specifies the number of seconds from 0-255 that the
		frequency should be measured over.  Write 0 to disable.

What:		/sys/class/timecard/ocpN/genX
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Optional directory containing the sysfs nodes for
		frequency generator <X>.

What:		/sys/class/timecard/ocpN/genX/duty
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Specifies the signal duty cycle as a percentage from 1-99.

What:		/sys/class/timecard/ocpN/genX/period
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Specifies the signal period in nanoseconds.

What:		/sys/class/timecard/ocpN/genX/phase
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Specifies the signal phase offset in nanoseconds.

What:		/sys/class/timecard/ocpN/genX/polarity
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Specifies the signal polarity, either 1 or 0.

What:		/sys/class/timecard/ocpN/genX/running
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Either 0 or 1, showing if the signal generator is running.

What:		/sys/class/timecard/ocpN/genX/start
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Shows the time in <sec>.<nsec> that the signal generator
		started running.

What:		/sys/class/timecard/ocpN/genX/signal
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RW) Used to start the signal generator, and summarize
		the current status.

		The signal generator may be started by writing the signal
		period, followed by the optional signal values.  If the
		optional values are not provided, they default to the current
		settings, which may be obtained from the other sysfs nodes.

		    period [duty [phase [polarity]]]

		echo 500000000 > signal       # 1/2 second period
		echo 1000000 40 100 > signal
		echo 0 > signal               # turn off generator

		Period and phase are specified in nanoseconds.  Duty cycle is
		a percentage from 1-99.  Polarity is 1 or 0.

		Reading this node will return:

		    period duty phase polarity start_time

What:		/sys/class/timecard/ocpN/gnss_sync
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Indicates whether a valid GNSS signal is received,
		or when the signal was lost.

What:		/sys/class/timecard/ocpN/i2c
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	This optional attribute links to the associated i2c device.

What:		/sys/class/timecard/ocpN/irig_b_mode
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RW) An integer from 0-7 indicating the timecode format
		of the IRIG-B output signal: B00<n>

What:		/sys/class/timecard/ocpN/pps
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	This optional attribute links to the associated PPS device.

What:		/sys/class/timecard/ocpN/ptp
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	This attribute links to the associated PTP device.

What:		/sys/class/timecard/ocpN/serialnum
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) Provides the serial number of the timecard.

What:		/sys/class/timecard/ocpN/sma1
What:		/sys/class/timecard/ocpN/sma2
What:		/sys/class/timecard/ocpN/sma3
What:		/sys/class/timecard/ocpN/sma4
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RW) These attributes specify the direction of the signal
		on the associated SMA connectors, and also the signal sink
		or source.

		The display format of the attribute is a space separated
		list of signals, prefixed by the input/output direction.

		The signal direction may be changed (if supported) by
		prefixing the signal list with either "in:" or "out:".
		If neither prefix is present, then the direction is unchanged.

		The output signal may be changed by writing one of the listed
		values from the available_sma_outputs attribute set.

		The input destinations may be changed by writing multiple
		values from the available_sma_inputs attribute set,
		separated by spaces.  If there are duplicated input
		destinations between connectors, the lowest numbered SMA
		connector is given priority.

		Note that not all input combinations may make sense.

		The 10Mhz reference clock input is currently only valid
		on SMA1 and may not be combined with other destination sinks.

What:		/sys/class/timecard/ocpN/tod_correction
Date:		March 2022
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RW) The incoming GNSS signal is in UTC time, and the NMEA
		format messages do not provide a TAI offset.  This sets the
		correction value for the incoming time.

		If UBX_LS is enabled, this should be 0, and the offset is
		taken from the UBX-NAV-TIMELS message.

What:		/sys/class/timecard/ocpN/ts_window_adjust
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RW) When retrieving the PHC with the PTP SYS_OFFSET_EXTENDED
		ioctl, a system timestamp is made before and after the PHC
		time is retrieved.  The midpoint between the two system
		timestamps is usually taken to be the SYS time associated
		with the PHC time.  This estimate may be wrong, as it depends
		on PCI latencies, and when the PHC time was latched

		The attribute value reduces the end timestamp by the given
		number of nanoseconds, so the computed midpoint matches the
		retrieved PHC time.

		The initial value is set based on measured PCI latency and
		the estimated point where the FPGA latches the PHC time.  This
		value may be changed by writing an unsigned integer.

What:		/sys/class/timecard/ocpN/tty
Date:		August 2024
Contact:	Vadim Fedorenko <<EMAIL>>
Description:	(RO) Directory containing the sysfs nodes for TTY attributes

What:		/sys/class/timecard/ocpN/tty/ttyGNSS
What:		/sys/class/timecard/ocpN/tty/ttyGNSS2
Date:		August 2024
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) These optional attributes contain names of the TTY serial
		ports associated with the GNSS devices.

What:		/sys/class/timecard/ocpN/tty/ttyMAC
Date:		August 2024
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) This optional attribute contains name of the TTY serial
		port associated with the Miniature Atomic Clock.

What:		/sys/class/timecard/ocpN/tty/ttyNMEA
Date:		August 2024
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RO) This optional attribute contains name of the TTY serial
		port which outputs the PHC time in NMEA ZDA format.

What:		/sys/class/timecard/ocpN/utc_tai_offset
Date:		September 2021
Contact:	Jonathan Lemon <<EMAIL>>
Description:	(RW) The DCF and IRIG output signals are in UTC, while the
		TimeCard operates on TAI.  This attribute allows setting the
		offset in seconds, which is added to the TAI timebase for
		these formats.

		The offset may be changed by writing an unsigned integer.
