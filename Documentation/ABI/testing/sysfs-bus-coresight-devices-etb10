What:		/sys/bus/coresight/devices/<memory_map>.etb/enable_sink
Date:		November 2014
KernelVersion:	3.19
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(RW) Add/remove a sink from a trace path.  There can be multiple
		source for a single sink.

		ex::

		  echo 1 > /sys/bus/coresight/devices/20010000.etb/enable_sink

What:		/sys/bus/coresight/devices/<memory_map>.etb/trigger_cntr
Date:		November 2014
KernelVersion:	3.19
Contact:	Mathieu <PERSON>er <<EMAIL>>
Description:	(RW) Disables write access to the Trace RAM by stopping the
		formatter after a defined number of words have been stored
		following the trigger event. The number of 32-bit words written
		into the Trace RAM following the trigger event is equal to the
		value stored in this register+1 (from ARM ETB-TRM).

What:		/sys/bus/coresight/devices/<memory_map>.etb/mgmt/rdp
Date:		March 2016
KernelVersion:	4.7
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	(Read) Defines the depth, in words, of the trace RAM in powers of
		2.  The value is read directly from HW register RDP, 0x004.

What:		/sys/bus/coresight/devices/<memory_map>.etb/mgmt/sts
Date:		March 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Shows the value held by the ETB status register.  The value
		is read directly from HW register STS, 0x00C.

What:		/sys/bus/coresight/devices/<memory_map>.etb/mgmt/rrp
Date:		March 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Shows the value held by the ETB RAM Read Pointer register
		that is used to read entries from the Trace RAM over the APB
		interface.  The value is read directly from HW register RRP,
		0x014.

What:		/sys/bus/coresight/devices/<memory_map>.etb/mgmt/rwp
Date:		March 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Shows the value held by the ETB RAM Write Pointer register
		that is used to sets the write pointer to write entries from
		the CoreSight bus into the Trace RAM. The value is read directly
		from HW register RWP, 0x018.

What:		/sys/bus/coresight/devices/<memory_map>.etb/mgmt/trg
Date:		March 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Similar to "trigger_cntr" above except that this value is
		read directly from HW register TRG, 0x01C.

What:		/sys/bus/coresight/devices/<memory_map>.etb/mgmt/ctl
Date:		March 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Shows the value held by the ETB Control register. The value
		is read directly from HW register CTL, 0x020.

What:		/sys/bus/coresight/devices/<memory_map>.etb/mgmt/ffsr
Date:		March 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Shows the value held by the ETB Formatter and Flush Status
		register.  The value is read directly from HW register FFSR,
		0x300.

What:		/sys/bus/coresight/devices/<memory_map>.etb/mgmt/ffcr
Date:		March 2016
KernelVersion:	4.7
Contact:	Mathieu Poirier <<EMAIL>>
Description:	(Read) Shows the value held by the ETB Formatter and Flush Control
		register.  The value is read directly from HW register FFCR,
		0x304.
