What:		/sys/bus/vdpa/drivers_autoprobe
Date:		March 2020
Contact:	<EMAIL>
Description:
		This file determines whether new devices are immediately bound
		to a driver after the creation. It initially contains 1, which
		means the kernel automatically binds devices to a compatible
		driver immediately after they are created.

		Writing "0" to this file disable this feature, any other string
		enable it.

What:		/sys/bus/vdpa/driver_probe
Date:		March 2020
Contact:	<EMAIL>
Description:
		Writing a device name to this file will cause the kernel binds
		devices to a compatible driver.

		This can be useful when /sys/bus/vdpa/drivers_autoprobe is
		disabled.

What:		/sys/bus/vdpa/drivers/.../bind
Date:		March 2020
Contact:	<EMAIL>
Description:
		Writing a device name to this file will cause the driver to
		attempt to bind to the device. This is useful for overriding
		default bindings.

What:		/sys/bus/vdpa/drivers/.../unbind
Date:		March 2020
Contact:	<EMAIL>
Description:
		Writing a device name to this file will cause the driver to
		attempt to unbind from the device. This may be useful when
		overriding default bindings.

What:		/sys/bus/vdpa/devices/.../driver_override
Date:		November 2021
Contact:	<EMAIL>
Description:
		This file allows the driver for a device to be specified.
		When specified, only a driver with a name matching the value
		written to driver_override will have an opportunity to bind to
		the device. The override is specified by writing a string to the
		driver_override file (echo vhost-vdpa > driver_override) and may
		be cleared with an empty string (echo > driver_override).
		This returns the device to standard matching rules binding.
		Writing to driver_override does not automatically unbind the
		device from its current driver or make any attempt to
		automatically load the specified driver. If no driver with a
		matching name is currently loaded in the kernel, the device will
		not bind to any driver. This also allows devices to opt-out of
		driver binding using a driver_override name such as "none".
		Only a single driver may be specified in the override, there is
		no support for parsing delimiters.
