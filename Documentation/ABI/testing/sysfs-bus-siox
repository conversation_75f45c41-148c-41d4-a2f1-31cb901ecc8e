What:		/sys/bus/siox/devices/siox-X/active
KernelVersion:	4.16
Contact:	<PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>
Description:
		On reading represents the current state of the bus. If it
		contains a "0" the bus is stopped and connected devices are
		expected to not do anything because their watchdog triggered.
		When the file contains a "1" the bus is operated and periodically
		does a push-pull cycle to write and read data from the
		connected devices.

		When writing a "0" or "1" the bus moves to the described state.

What:		/sys/bus/siox/devices/siox-X/device_add
KernelVersion:	4.16
Contact:	<PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>
Description:
		Write-only file. Write

			<type> <inbytes> <outbytes> <statustype>

		to add a new device dynamically. <type> is the name that is used to match
		to a driver (similar to the platform bus). <inbytes> and <outbytes> define
		the length of the input and output shift register in bytes respectively.

		<statustype> defines the 4 bit device type that is check to identify connection
		problems.

		The new device is added to the end of the existing chain.

What:		/sys/bus/siox/devices/siox-X/device_remove
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Write-only file. A single write removes the last device in the siox chain.

What:		/sys/bus/siox/devices/siox-X/poll_interval_ns
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Defines the interval between two poll cycles in nano seconds.
		Note this is rounded to jiffies on writing. On reading the current value
		is returned.

What:		/sys/bus/siox/devices/siox-X-Y/connected
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Read-only value. "0" means the Yth device on siox bus X isn't "connected" i.e.
		communication with it is not ensured. "1" signals a working connection.

What:		/sys/bus/siox/devices/siox-X-Y/inbytes
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Read-only value reporting the inbytes value provided to siox-X/device_add

What:		/sys/bus/siox/devices/siox-X-Y/status_errors
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Counts the number of time intervals when the read status byte doesn't yield the
		expected value.

What:		/sys/bus/siox/devices/siox-X-Y/type
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Read-only value reporting the type value provided to siox-X/device_add.

What:		/sys/bus/siox/devices/siox-X-Y/watchdog
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Read-only value reporting if the watchdog of the siox device is
		active. "0" means the watchdog is not active and the device is expected to
		be operational. "1" means the watchdog keeps the device in reset.

What:		/sys/bus/siox/devices/siox-X-Y/watchdog_errors
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Read-only value reporting the number to time intervals when the
		watchdog was active.

What:		/sys/bus/siox/devices/siox-X-Y/outbytes
KernelVersion:	4.16
Contact:	Thorsten Scherer <<EMAIL>>, Uwe Kleine-König <<EMAIL>>
Description:
		Read-only value reporting the outbytes value provided to siox-X/device_add.
