What:		/sys/fs/xfs/<disk>/log/log_head_lsn
Date:		July 2014
KernelVersion:	3.17
Contact:	<EMAIL>
Description:
		The log sequence number (LSN) of the current head of the
		log. The LSN is exported in "cycle:basic block" format.
Users:		xfstests

What:		/sys/fs/xfs/<disk>/log/log_tail_lsn
Date:		July 2014
KernelVersion:	3.17
Contact:	<EMAIL>
Description:
		The log sequence number (LSN) of the current tail of the
		log. The LSN is exported in "cycle:basic block" format.

What:		/sys/fs/xfs/<disk>/log/reserve_grant_head_bytes
Date:		June 2024
KernelVersion:	6.11
Contact:	<EMAIL>
Description:
		The current state of the log reserve grant head. It
		represents the total log reservation of all currently
		outstanding transactions in bytes.
Users:		xfstests

What:		/sys/fs/xfs/<disk>/log/write_grant_head_bytes
Date:		June 2024
KernelVersion:	6.11
Contact:	<EMAIL>
Description:
		The current state of the log write grant head. It
		represents the total log reservation of all currently
		outstanding transactions, including regrants due to
		rolling transactions in bytes.
Users:		xfstests
