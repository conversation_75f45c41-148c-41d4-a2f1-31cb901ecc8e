What:		/sys/firmware/secvar
Date:		August 2019
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	This directory is created if the POWER firmware supports OS
		secureboot, thereby secure variables. It exposes interface
		for reading/writing the secure variables

What:		/sys/firmware/secvar/vars
Date:		August 2019
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	This directory lists all the secure variables that are supported
		by the firmware.

What:		/sys/firmware/secvar/format
Date:		August 2019
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	A string indicating which backend is in use by the firmware.
		This determines the format of the variable and the accepted
		format of variable updates.

		On powernv/OPAL, this value is provided by the OPAL firmware
		and is expected to be "ibm,edk2-compat-v1".

		On pseries/PLPKS, this is generated by the kernel based on the
		version number in the SB_VERSION variable in the keystore, and
		has the form "ibm,plpks-sb-v<version>", or
		"ibm,plpks-sb-unknown" if there is no SB_VERSION variable.

What:		/sys/firmware/secvar/vars/<variable name>
Date:		August 2019
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	Each secure variable is represented as a directory named as
		<variable_name>. The variable name is unique and is in ASCII
		representation. The data and size can be determined by reading
		their respective attribute files.

What:		/sys/firmware/secvar/vars/<variable_name>/size
Date:		August 2019
Contact:	Nayna Jain <<EMAIL>>
Description:	An integer representation of the size of the content of the
		variable. In other words, it represents the size of the data.

What:		/sys/firmware/secvar/vars/<variable_name>/data
Date:		August 2019
Contact:	Nayna Jain <<EMAIL>>
Description:	A read-only file containing the value of the variable. The size
		of the file represents the maximum size of the variable data.

What:		/sys/firmware/secvar/vars/<variable_name>/update
Date:		August 2019
Contact:	Nayna Jain <<EMAIL>>
Description:	A write-only file that is used to submit the new value for the
		variable. The size of the file represents the maximum size of
		the variable data that can be written.

What:		/sys/firmware/secvar/config
Date:		February 2023
Contact:	Nayna Jain <<EMAIL>>
Description:	This optional directory contains read-only config attributes as
		defined by the secure variable implementation.  All data is in
		ASCII format. The directory is only created if the backing
		implementation provides variables to populate it, which at
		present is only PLPKS on the pseries platform.

What:		/sys/firmware/secvar/config/version
Date:		February 2023
Contact:	Nayna Jain <<EMAIL>>
Description:	Config version as reported by the hypervisor in ASCII decimal
		format.

		Currently only provided by PLPKS on the pseries platform.

What:		/sys/firmware/secvar/config/max_object_size
Date:		February 2023
Contact:	Nayna Jain <<EMAIL>>
Description:	Maximum allowed size of	objects in the keystore in bytes,
		represented in ASCII decimal format.

		This is not necessarily the same as the max size that can be
		written to an update file as writes can contain more than
		object data, you should use the size of the update file for
		that purpose.

		Currently only provided by PLPKS on the pseries platform.

What:		/sys/firmware/secvar/config/total_size
Date:		February 2023
Contact:	Nayna Jain <<EMAIL>>
Description:	Total size of the PLPKS in bytes, represented in ASCII decimal
		format.

		Currently only provided by PLPKS on the pseries platform.

What:		/sys/firmware/secvar/config/used_space
Date:		February 2023
Contact:	Nayna Jain <<EMAIL>>
Description:	Current space consumed by the key store, in bytes, represented
		in ASCII decimal format.

		Currently only provided by PLPKS on the pseries platform.

What:		/sys/firmware/secvar/config/supported_policies
Date:		February 2023
Contact:	Nayna Jain <<EMAIL>>
Description:	Bitmask of supported policy flags by the hypervisor,
		represented as an 8 byte hexadecimal ASCII string. Consult the
		hypervisor documentation for what these flags are.

		Currently only provided by PLPKS on the pseries platform.

What:		/sys/firmware/secvar/config/signed_update_algorithms
Date:		February 2023
Contact:	Nayna Jain <<EMAIL>>
Description:	Bitmask of flags indicating which algorithms the hypervisor
		supports for signed update of objects, represented as a 16 byte
		hexadecimal ASCII string. Consult the hypervisor documentation
		for what these flags mean.

		Currently only provided by PLPKS on the pseries platform.
