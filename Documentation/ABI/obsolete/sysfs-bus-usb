What:		/sys/bus/usb/devices/.../power/level
Date:		March 2007
KernelVersion:	2.6.21
Contact:	<PERSON> <<EMAIL>>
Description:
		Each USB device directory will contain a file named
		power/level.  This file holds a power-level setting for
		the device, either "on" or "auto".

		"on" means that the device is not allowed to autosuspend,
		although normal suspends for system sleep will still
		be honored.  "auto" means the device will autosuspend
		and autoresume in the usual manner, according to the
		capabilities of its driver.

		During normal use, devices should be left in the "auto"
		level.  The "on" level is meant for administrative uses.
		If you want to suspend a device immediately but leave it
		free to wake up in response to I/O requests, you should
		write "0" to power/autosuspend.

		Device not capable of proper suspend and resume should be
		left in the "on" level.  Although the USB spec requires
		devices to support suspend/resume, many of them do not.
		In fact so many don't that by default, the USB core
		initializes all non-hub devices in the "on" level.  Some
		drivers may change this setting when they are bound.

		This file is deprecated and will be removed after 2010.
		Use the power/control file instead; it does exactly the
		same thing.
