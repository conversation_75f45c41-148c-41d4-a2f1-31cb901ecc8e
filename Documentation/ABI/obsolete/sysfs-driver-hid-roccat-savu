What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/savu/roccatsavu<minor>/buttons
Date:		Mai 2012
Contact:	<PERSON> <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. A profile is split into general settings and
		button settings. The buttons variable holds information about
		button layout. When written, this file lets one write the
		respective profile buttons to the mouse. The data has to be
		47 bytes long.
		The mouse will reject invalid data.
		Which profile to write is determined by the profile number
		contained in the data.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/savu/roccatsavu<minor>/control
Date:		Mai 2012
Contact:	<PERSON> <<EMAIL>>
Description:	When written, this file lets one select which data from which
		profile will be	read next. The data has to be 3 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/savu/roccatsavu<minor>/general
Date:		Mai 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. A profile is split into general settings and
		button settings. A profile holds information like resolution,
		sensitivity and light effects.
		When written, this file lets one write the respective profile
		settings back to the mouse. The data has to be 43 bytes long.
		The mouse will reject invalid data.
		Which profile to write is determined by the profile number
		contained in the data.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/savu/roccatsavu<minor>/info
Date:		Mai 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	When read, this file returns general data like firmware version.
		When written, the device can be reset.
		The data is 8 bytes long.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/savu/roccatsavu<minor>/macro
Date:		Mai 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one store macros with max 500
		keystrokes for a specific button for a specific profile.
		Button and profile numbers are included in written data.
		The data has to be 2083 bytes long.
		Before reading this file, control has to be written to select
		which profile and key to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/savu/roccatsavu<minor>/profile
Date:		Mai 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. profile holds number of actual profile.
		This value is persistent, so its value determines the profile
		that's active when the mouse is powered on next time.
		When written, the mouse activates the set profile immediately.
		The data has to be 3 bytes long.
		The mouse will reject invalid data.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/savu/roccatsavu<minor>/sensor
Date:		July 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse has a Avago ADNS-3090 sensor.
		This file allows reading and writing of the mouse sensors registers.
		The data has to be 4 bytes long.
Users:		http://roccat.sourceforge.net
