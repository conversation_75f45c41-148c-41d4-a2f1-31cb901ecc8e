What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/actual_profile
Date:		June 2011
Contact:	<PERSON> <<EMAIL>>
Description:	The integer value of this attribute ranges from 0-4.
		When read, this attribute returns the number of the actual
		profile. This value is persistent, so its equivalent to the
		profile that's active when the device is powered on next time.
		When written, this file sets the number of the startup profile
		and the device activates this profile immediately.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/info
Date:		June 2011
Contact:	<PERSON> <<EMAIL>>
Description:	When read, this file returns general data like firmware version.
		The data is 6 bytes long.
		This file is readonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/key_mask
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one deactivate certain keys like
		windows and application keys, to prevent accidental presses.
		Profile number for which this settings occur is included in
		written data. The data has to be 6 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/keys_capslock
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the
		capslock key for a specific profile. Profile number is included
		in written data. The data has to be 6 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/keys_easyzone
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the
		easyzone keys for a specific profile. Profile number is included
		in written data. The data has to be 65 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/keys_function
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the
		function keys for a specific profile. Profile number is included
		in written data. The data has to be 41 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/keys_macro
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the macro
		keys for a specific profile. Profile number is included in
		written data. The data has to be 35 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/keys_media
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the media
		keys for a specific profile. Profile number is included in
		written data. The data has to be 29 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/keys_thumbster
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the
		thumbster keys for a specific profile. Profile number is included
		in written data. The data has to be 23 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/last_set
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the time in secs since
		epoch in which the last configuration took place.
		The data has to be 20 bytes long.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/light
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the backlight intensity for
		a specific profile. Profile number is included in written data.
		The data has to be 10 bytes long for Isku, IskuFX needs	16 bytes
		of data.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/macro
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one store macros with max 500
		keystrokes for a specific button for a specific profile.
		Button and profile numbers are included in written data.
		The data has to be 2083 bytes long.
		Before reading this file, control has to be written to select
		which profile and key to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/reset
Date:		November 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one reset the device.
		The data has to be 3 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/control
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one select which data from which
		profile will be	read next. The data has to be 3 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/talk
Date:		June 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one trigger easyshift functionality
		from the host.
		The data has to be 16 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/isku/roccatisku<minor>/talkfx
Date:		February 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one trigger temporary color schemes
		from the host.
		The data has to be 16 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net
