What:		/sys/bus/iio/devices/iio:deviceX/buffer/length
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		Number of scans contained by the buffer.

		Since Kernel 5.11, multiple buffers are supported.
		so, it is better to use, instead:

			/sys/bus/iio/devices/iio:deviceX/bufferY/length

What:		/sys/bus/iio/devices/iio:deviceX/buffer/enable
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		Actually start the buffer capture up.  Will start trigger
		if first device and appropriate.

		Since Kernel 5.11, multiple buffers are supported.
		so, it is better to use, instead:

			/sys/bus/iio/devices/iio:deviceX/bufferY/enable

What:		/sys/bus/iio/devices/iio:deviceX/scan_elements
KernelVersion:	2.6.37
Contact:	<EMAIL>
Description:
		Directory containing interfaces for elements that will be
		captured for a single triggered sample set in the buffer.

		Since kernel 5.11 the scan_elements attributes are merged into
		the bufferY directory, to be configurable per buffer.

What:		/sys/.../iio:deviceX/scan_elements/in_accel_x_en
What:		/sys/.../iio:deviceX/scan_elements/in_accel_y_en
What:		/sys/.../iio:deviceX/scan_elements/in_accel_z_en
What:		/sys/.../iio:deviceX/scan_elements/in_anglvel_x_en
What:		/sys/.../iio:deviceX/scan_elements/in_anglvel_y_en
What:		/sys/.../iio:deviceX/scan_elements/in_anglvel_z_en
What:		/sys/.../iio:deviceX/scan_elements/in_magn_x_en
What:		/sys/.../iio:deviceX/scan_elements/in_magn_y_en
What:		/sys/.../iio:deviceX/scan_elements/in_magn_z_en
What:		/sys/.../iio:deviceX/scan_elements/in_rot_from_north_magnetic_en
What:		/sys/.../iio:deviceX/scan_elements/in_rot_from_north_true_en
What:		/sys/.../iio:deviceX/scan_elements/in_rot_from_north_magnetic_tilt_comp_en
What:		/sys/.../iio:deviceX/scan_elements/in_rot_from_north_true_tilt_comp_en
What:		/sys/.../iio:deviceX/scan_elements/in_timestamp_en
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_supply_en
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_en
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY-voltageZ_en
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_i_en
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_q_en
What:		/sys/.../iio:deviceX/scan_elements/in_voltage_i_en
What:		/sys/.../iio:deviceX/scan_elements/in_voltage_q_en
What:		/sys/.../iio:deviceX/scan_elements/in_incli_x_en
What:		/sys/.../iio:deviceX/scan_elements/in_incli_y_en
What:		/sys/.../iio:deviceX/scan_elements/in_pressureY_en
What:		/sys/.../iio:deviceX/scan_elements/in_pressure_en
What:		/sys/.../iio:deviceX/scan_elements/in_rot_quaternion_en
What:		/sys/.../iio:deviceX/scan_elements/in_proximity_en
KernelVersion:	2.6.37
Contact:	<EMAIL>
Description:
		Scan element control for triggered data capture.

		Since kernel 5.11 the scan_elements attributes are merged into
		the bufferY directory, to be configurable per buffer.

What:		/sys/.../iio:deviceX/scan_elements/in_accel_type
What:		/sys/.../iio:deviceX/scan_elements/in_anglvel_type
What:		/sys/.../iio:deviceX/scan_elements/in_magn_type
What:		/sys/.../iio:deviceX/scan_elements/in_incli_type
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_type
What:		/sys/.../iio:deviceX/scan_elements/in_voltage_type
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_supply_type
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_i_type
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_q_type
What:		/sys/.../iio:deviceX/scan_elements/in_voltage_i_type
What:		/sys/.../iio:deviceX/scan_elements/in_voltage_q_type
What:		/sys/.../iio:deviceX/scan_elements/in_timestamp_type
What:		/sys/.../iio:deviceX/scan_elements/in_pressureY_type
What:		/sys/.../iio:deviceX/scan_elements/in_pressure_type
What:		/sys/.../iio:deviceX/scan_elements/in_rot_quaternion_type
What:		/sys/.../iio:deviceX/scan_elements/in_proximity_type
KernelVersion:	2.6.37
Contact:	<EMAIL>
Description:
		Description of the scan element data storage within the buffer
		and hence the form in which it is read from user-space.
		Form is [be|le]:[s|u]bits/storagebits[>>shift].
		be or le specifies big or little endian. s or u specifies if
		signed (2's complement) or unsigned. bits is the number of bits
		of data and storagebits is the space (after padding) that it
		occupies in the buffer. shift if specified, is the shift that
		needs to be applied prior to masking out unused bits. Some
		devices put their data in the middle of the transferred elements
		with additional information on both sides.  Note that some
		devices will have additional information in the unused bits
		so to get a clean value, the bits value must be used to mask
		the buffer output value appropriately.  The storagebits value
		also specifies the data alignment.  So s48/64>>2 will be a
		signed 48 bit integer stored in a 64 bit location aligned to
		a 64 bit boundary. To obtain the clean value, shift right 2
		and apply a mask to zero the top 16 bits of the result.
		For other storage combinations this attribute will be extended
		appropriately.

		Since kernel 5.11 the scan_elements attributes are merged into
		the bufferY directory, to be configurable per buffer.

What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_index
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_supply_index
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_i_index
What:		/sys/.../iio:deviceX/scan_elements/in_voltageY_q_index
What:		/sys/.../iio:deviceX/scan_elements/in_voltage_i_index
What:		/sys/.../iio:deviceX/scan_elements/in_voltage_q_index
What:		/sys/.../iio:deviceX/scan_elements/in_accel_x_index
What:		/sys/.../iio:deviceX/scan_elements/in_accel_y_index
What:		/sys/.../iio:deviceX/scan_elements/in_accel_z_index
What:		/sys/.../iio:deviceX/scan_elements/in_anglvel_x_index
What:		/sys/.../iio:deviceX/scan_elements/in_anglvel_y_index
What:		/sys/.../iio:deviceX/scan_elements/in_anglvel_z_index
What:		/sys/.../iio:deviceX/scan_elements/in_magn_x_index
What:		/sys/.../iio:deviceX/scan_elements/in_magn_y_index
What:		/sys/.../iio:deviceX/scan_elements/in_magn_z_index
What:		/sys/.../iio:deviceX/scan_elements/in_rot_from_north_magnetic_index
What:		/sys/.../iio:deviceX/scan_elements/in_rot_from_north_true_index
What:		/sys/.../iio:deviceX/scan_elements/in_rot_from_north_magnetic_tilt_comp_index
What:		/sys/.../iio:deviceX/scan_elements/in_rot_from_north_true_tilt_comp_index
What:		/sys/.../iio:deviceX/scan_elements/in_incli_x_index
What:		/sys/.../iio:deviceX/scan_elements/in_incli_y_index
What:		/sys/.../iio:deviceX/scan_elements/in_timestamp_index
What:		/sys/.../iio:deviceX/scan_elements/in_pressureY_index
What:		/sys/.../iio:deviceX/scan_elements/in_pressure_index
What:		/sys/.../iio:deviceX/scan_elements/in_rot_quaternion_index
What:		/sys/.../iio:deviceX/scan_elements/in_proximity_index
KernelVersion:	2.6.37
Description:
		A single positive integer specifying the position of this
		scan element in the buffer. Note these are not dependent on
		what is enabled and may not be contiguous. Thus for user-space
		to establish the full layout these must be used in conjunction
		with all _en attributes to establish which channels are present,
		and the relevant _type attributes to establish the data storage
		format.

		Since kernel 5.11 the scan_elements attributes are merged into
		the bufferY directory, to be configurable per buffer.

What:		/sys/bus/iio/devices/iio:deviceX/buffer/watermark
KernelVersion:	4.2
Contact:	<EMAIL>
Description:
		A single positive integer specifying the maximum number of scan
		elements to wait for.

		Poll will block until the watermark is reached.

		Blocking read will wait until the minimum between the requested
		read amount or the low water mark is available.

		Non-blocking read will retrieve the available samples from the
		buffer even if there are less samples then watermark level. This
		allows the application to block on poll with a timeout and read
		the available samples after the timeout expires and thus have a
		maximum delay guarantee.

		Since Kernel 5.11, multiple buffers are supported.
		so, it is better to use, instead:

			/sys/bus/iio/devices/iio:deviceX/bufferY/watermark

What:		/sys/bus/iio/devices/iio:deviceX/buffer/data_available
KernelVersion: 4.16
Contact:	<EMAIL>
Description:
		A read-only value indicating the bytes of data available in the
		buffer. In the case of an output buffer, this indicates the
		amount of empty space available to write data to. In the case of
		an input buffer, this indicates the amount of data available for
		reading.

		Since Kernel 5.11, multiple buffers are supported.
		so, it is better to use, instead:

			/sys/bus/iio/devices/iio:deviceX/bufferY/data_available
