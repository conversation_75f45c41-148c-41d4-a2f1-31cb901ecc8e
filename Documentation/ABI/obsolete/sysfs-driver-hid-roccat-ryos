What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/control
Date:		October 2013
Contact:	<PERSON> <<EMAIL>>
Description:	When written, this file lets one select which data from which
		profile will be	read next. The data has to be 3 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/profile
Date:		October 2013
Contact:	<PERSON> <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. profile holds index of actual profile.
		This value is persistent, so its value determines the profile
		that's active when the device is powered on next time.
		When written, the device activates the set profile immediately.
		The data has to be 3 bytes long.
		The device will reject invalid data.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/keys_primary
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the default of all keys for
		a specific profile. Profile index is included in written data.
		The data has to be 125 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/keys_function
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the
		function keys for a specific profile. Profile index is included
		in written data. The data has to be 95 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/keys_macro
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the macro
		keys for a specific profile. Profile index is included in
		written data. The data has to be 35 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/keys_thumbster
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the
		thumbster keys for a specific profile. Profile index is included
		in written data. The data has to be 23 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/keys_extra
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the
		capslock and function keys for a specific profile. Profile index
		is included in written data. The data has to be 8 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/keys_easyzone
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the function of the
		easyzone keys for a specific profile. Profile index is included
		in written data. The data has to be 294 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/key_mask
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one deactivate certain keys like
		windows and application keys, to prevent accidental presses.
		Profile index for which this settings occur is included in
		written data. The data has to be 6 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/light
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the backlight intensity for
		a specific profile. Profile index is included in written data.
		This attribute is only valid for the glow and pro variant.
		The data has to be 16 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/macro
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one store macros with max 480
		keystrokes for a specific button for a specific profile.
		Button and profile indexes are included in written data.
		The data has to be 2002 bytes long.
		Before reading this file, control has to be written to select
		which profile and key to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/info
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When read, this file returns general data like firmware version.
		The data is 8 bytes long.
		This file is readonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/reset
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one reset the device.
		The data has to be 3 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/talk
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one trigger easyshift functionality
		from the host.
		The data has to be 16 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/light_control
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one switch between stored and custom
		light settings.
		This attribute is only valid for the pro variant.
		The data has to be 8 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/stored_lights
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set per-key lighting for different
		layers.
		This attribute is only valid for the pro variant.
		The data has to be 1382 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/custom_lights
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set the actual per-key lighting.
		This attribute is only valid for the pro variant.
		The data has to be 20 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/ryos/roccatryos<minor>/light_macro
Date:		October 2013
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written, this file lets one set a light macro that is looped
		whenever the device gets in dimness mode.
		This attribute is only valid for the pro variant.
		The data has to be 2002 bytes long.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net
