What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/actual_cpi
Date:		August 2010
Contact:	<PERSON> <<EMAIL>>
Description:	It is possible to switch the cpi setting of the mouse with the
		press of a button.
		When read, this file returns the raw number of the actual cpi
		setting reported by the mouse. This number has to be further
		processed to receive the real dpi value:

		===== ====
		VALUE DPI
		===== ====
		1     400
		2     800
		4     1600
		===== ====

		This file is readonly.
		Has never been used. If bookkeeping is done, it's done in userland tools.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/actual_profile
Date:		August 2010
Contact:	<PERSON> <<EMAIL>>
Description:	When read, this file returns the number of the actual profile in
		range 0-4.
		This file is readonly.
		Please use binary attribute "settings" which provides this information.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/firmware_version
Date:		August 2010
Contact:	Stefan Achatz <<EMAIL>>
Description:	When read, this file returns the raw integer version number of the
		firmware reported by the mouse. Using the integer value eases
		further usage in other programs. To receive the real version
		number the decimal point has to be shifted 2 positions to the
		left. E.g. a returned value of 138 means 1.38
		This file is readonly.
		Please use binary attribute "info" which provides this information.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/info
Date:		November 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	When read, this file returns general data like firmware version.
		When written, the device can be reset.
		The data is 6 bytes long.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/profile_buttons
Date:		August 2010
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. A profile is split in settings and buttons.
		profile_buttons holds information about button layout.
		When written, this file lets one write the respective profile
		buttons back to the mouse. The data has to be 19 bytes long.
		The mouse will reject invalid data.
		Which profile to write is determined by the profile number
		contained in the data.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/profile[1-5]_buttons
Date:		August 2010
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. A profile is split in settings and buttons.
		profile_buttons holds information about button layout.
		When read, these files return the respective profile buttons.
		The returned data is 19 bytes in size.
		This file is readonly.
		Write control to select profile and read profile_buttons instead.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/profile_settings
Date:		August 2010
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. A profile is split in settings and buttons.
		profile_settings holds information like resolution, sensitivity
		and light effects.
		When written, this file lets one write the respective profile
		settings back to the mouse. The data has to be 13 bytes long.
		The mouse will reject invalid data.
		Which profile to write is determined by the profile number
		contained in the data.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/profile[1-5]_settings
Date:		August 2010
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. A profile is split in settings and buttons.
		profile_settings holds information like resolution, sensitivity
		and light effects.
		When read, these files return the respective profile settings.
		The returned data is 13 bytes in size.
		This file is readonly.
		Write control to select profile and read profile_settings instead.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/settings
Date:		August 2010
Contact:	Stefan Achatz <<EMAIL>>
Description:	When read, this file returns the settings stored in the mouse.
		The size of the data is 3 bytes and holds information on the
		startup_profile.
		When written, this file lets write settings back to the mouse.
		The data has to be 3 bytes long. The mouse will reject invalid
		data.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/pyra/roccatpyra<minor>/startup_profile
Date:		August 2010
Contact:	Stefan Achatz <<EMAIL>>
Description:	The integer value of this attribute ranges from 0-4.
                When read, this attribute returns the number of the profile
                that's active when the mouse is powered on.
		This file is readonly.
		Please use binary attribute "settings" which provides this information.
Users:		http://roccat.sourceforge.net
